<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\Output\NewStation-F429.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\Output\NewStation-F429.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Aug 07 16:50:10 2025
<BR><P>
<H3>Maximum Stack Usage =       7576 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
work_thread_entry &rArr; RUN_SHOW &rArr; RateMode_Set &rArr; init_odsp_mode_host_prbs_intf_8 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[35]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[35]">ADC_IRQHandler</a><BR>
 <LI><a href="#[248]">odsp_efuse_fetch</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[249]">odsp_reg_channel_rmw</a><BR>
 <LI><a href="#[1be]">odsp_reg_channel_read</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[288]">odsp_rebase_by_addr</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[35]">ADC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1d]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[37]">CAN1_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[38]">CAN1_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[39]">CAN1_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[36]">CAN1_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[63]">CAN2_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[64]">CAN2_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[65]">CAN2_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[62]">CAN2_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[72]">CRYP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[71]">DCMI_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2e]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2f]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[30]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[31]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[32]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[33]">DMA1_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[34]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[52]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7d]">DMA2D_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5b]">DMA2_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5c]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5d]">DMA2_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5e]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5f]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[67]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[68]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[69]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[20]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[60]">ETH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[61]">ETH_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[29]">EXTI0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4b]">EXTI15_10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2a]">EXTI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2b]">EXTI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2c]">EXTI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2d]">EXTI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3a]">EXTI9_5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[27]">FLASH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[53]">FMC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[74]">FPU_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[73]">HASH_RNG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1b]">HardFault_Handler</a> from context_rvds.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[43]">I2C1_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[42]">I2C1_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[45]">I2C2_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[44]">I2C2_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6c]">I2C3_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6b]">I2C3_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7c]">LTDC_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7b]">LTDC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1c]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1a]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[66]">OTG_FS_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4d]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6e]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6d]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[70]">OTG_HS_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6f]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[24]">PVD_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[21]">PendSV_Handler</a> from context_rvds.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[28]">RCC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4c]">RTC_Alarm_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[26]">RTC_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[19]">Reset_Handler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7a]">SAI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[54]">SDIO_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[46]">SPI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[47]">SPI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[56]">SPI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[77]">SPI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[78]">SPI5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[79]">SPI6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1f]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[22]">SysTick_Handler</a> from board.o(i.SysTick_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7f]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[25]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3b]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3e]">TIM1_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3d]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3c]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3f]">TIM2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[40]">TIM3_IRQHandler</a> from stm32f4xx_it.o(i.TIM3_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[41]">TIM4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[55]">TIM5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[59]">TIM6_DAC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5a]">TIM7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4e]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[51]">TIM8_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[50]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4f]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[57]">UART4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[58]">UART5_IRQHandler</a> from stm32f4xx_it.o(i.UART5_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[75]">UART7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[76]">UART8_IRQHandler</a> from stm32f4xx_it.o(i.UART8_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[48]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[49]">USART2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4a]">USART3_IRQHandler</a> from stm32f4xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6a]">USART6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1e]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[23]">WWDG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[80]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[14]">deserialize</a> from odsp_api.o(i.deserialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[86]">eeprom_thread_entry</a> from main.o(i.eeprom_thread_entry) referenced from main.o(i.main)
 <LI><a href="#[81]">fputc</a> from main.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[7e]">main</a> from components.o(i.$Sub$$main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[87]">main_thread_entry</a> from components.o(i.main_thread_entry) referenced from components.o(i.rt_application_init)
 <LI><a href="#[16]">msg_text_deserialize</a> from odsp_api.o(i.msg_text_deserialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[15]">msg_text_serialize</a> from odsp_api.o(i.msg_text_serialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[18]">msg_uint16_deserialize</a> from odsp_api.o(i.msg_uint16_deserialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[17]">msg_uint16_serialize</a> from odsp_api.o(i.msg_uint16_serialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[11]">msgr_default_inbox_check</a> from odsp_api.o(i.msgr_default_inbox_check) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[12]">msgr_default_load</a> from odsp_api.o(i.msgr_default_load) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[10]">msgr_default_send</a> from odsp_api.o(i.msgr_default_send) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[a0]">odsp_mcu__log_text_print</a> from odsp_api.o(i.odsp_mcu__log_text_print) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9b]">ring_coder__alignment_get</a> from odsp_api.o(i.ring_coder__alignment_get) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9c]">ring_coder__alignment_set</a> from odsp_api.o(i.ring_coder__alignment_set) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9f]">ring_coder__decoding_reset</a> from odsp_api.o(i.ring_coder__decoding_reset) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9e]">ring_coder__encoding_reset</a> from odsp_api.o(i.ring_coder__encoding_reset) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9d]">ring_coder__flush</a> from odsp_api.o(i.ring_coder__flush) referenced from odsp_api.o(.constdata)
 <LI><a href="#[99]">ring_coder__rx_avail</a> from odsp_api.o(i.ring_coder__rx_avail) referenced from odsp_api.o(.constdata)
 <LI><a href="#[9a]">ring_coder__tx_avail</a> from odsp_api.o(i.ring_coder__tx_avail) referenced from odsp_api.o(.constdata)
 <LI><a href="#[95]">ring_coder__uint16_decode</a> from odsp_api.o(i.ring_coder__uint16_decode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[90]">ring_coder__uint16_encode</a> from odsp_api.o(i.ring_coder__uint16_encode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[96]">ring_coder__uint32_decode</a> from odsp_api.o(i.ring_coder__uint32_decode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[91]">ring_coder__uint32_encode</a> from odsp_api.o(i.ring_coder__uint32_encode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[97]">ring_coder__uint64_decode</a> from odsp_api.o(i.ring_coder__uint64_decode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[92]">ring_coder__uint64_encode</a> from odsp_api.o(i.ring_coder__uint64_encode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[94]">ring_coder__uint8_decode</a> from odsp_api.o(i.ring_coder__uint8_decode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8f]">ring_coder__uint8_encode</a> from odsp_api.o(i.ring_coder__uint8_encode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[98]">ring_coder__uint8s_decode</a> from odsp_api.o(i.ring_coder__uint8s_decode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[93]">ring_coder__uint8s_encode</a> from odsp_api.o(i.ring_coder__uint8s_encode) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8a]">ring_hal_direct_alignment</a> from odsp_api.o(i.ring_hal_direct_alignment) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8d]">ring_hal_direct_block_read</a> from odsp_api.o(i.ring_hal_direct_block_read) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8e]">ring_hal_direct_block_write</a> from odsp_api.o(i.ring_hal_direct_block_write) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8b]">ring_hal_direct_read</a> from odsp_api.o(i.ring_hal_direct_read) referenced from odsp_api.o(.constdata)
 <LI><a href="#[8c]">ring_hal_direct_write</a> from odsp_api.o(i.ring_hal_direct_write) referenced from odsp_api.o(.constdata)
 <LI><a href="#[b]">ring_hal_pif_alignment</a> from odsp_api.o(i.ring_hal_pif_alignment) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[e]">ring_hal_pif_block_read</a> from odsp_api.o(i.ring_hal_pif_block_read) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[f]">ring_hal_pif_block_write</a> from odsp_api.o(i.ring_hal_pif_block_write) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[c]">ring_hal_pif_read</a> from odsp_api.o(i.ring_hal_pif_read) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[d]">ring_hal_pif_write</a> from odsp_api.o(i.ring_hal_pif_write) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[82]">rt_thread_exit</a> from thread.o(i.rt_thread_exit) referenced from thread.o(i._rt_thread_init)
 <LI><a href="#[89]">rt_thread_idle_entry</a> from idle.o(i.rt_thread_idle_entry) referenced from idle.o(i.rt_thread_idle_init)
 <LI><a href="#[83]">rt_thread_timeout</a> from thread.o(i.rt_thread_timeout) referenced from thread.o(i._rt_thread_init)
 <LI><a href="#[88]">rt_thread_timer_entry</a> from timer_1.o(i.rt_thread_timer_entry) referenced from timer_1.o(i.rt_system_timer_thread_init)
 <LI><a href="#[a3]">rti_board_end</a> from components.o(i.rti_board_end) referenced from components.o(.rti_fn.1.end)
 <LI><a href="#[a2]">rti_board_start</a> from components.o(i.rti_board_start) referenced from components.o(.rti_fn.0.end)
 <LI><a href="#[a4]">rti_end</a> from components.o(i.rti_end) referenced from components.o(.rti_fn.6.end)
 <LI><a href="#[a1]">rti_start</a> from components.o(i.rti_start) referenced from components.o(.rti_fn.0)
 <LI><a href="#[13]">serialize</a> from odsp_api.o(i.serialize) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[6]">smem_ring_hal_alignment</a> from odsp_api.o(i.smem_ring_hal_alignment) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[9]">smem_ring_hal_block_read</a> from odsp_api.o(i.smem_ring_hal_block_read) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[a]">smem_ring_hal_block_write</a> from odsp_api.o(i.smem_ring_hal_block_write) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[7]">smem_ring_hal_read</a> from odsp_api.o(i.smem_ring_hal_read) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[8]">smem_ring_hal_write</a> from odsp_api.o(i.smem_ring_hal_write) referenced 2 times from odsp_api.o(.data)
 <LI><a href="#[84]">uart_thread_entry</a> from main.o(i.uart_thread_entry) referenced from main.o(i.main)
 <LI><a href="#[85]">work_thread_entry</a> from main.o(i.work_thread_entry) referenced from main.o(i.main)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[80]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[31d]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[a5]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[bd]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[31e]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[31f]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[320]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[321]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[322]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[323]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[19]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2cc]"></a>rt_hw_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[2cd]"></a>rt_hw_interrupt_enable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[2f3]"></a>rt_hw_context_switch</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>

<P><STRONG><a name="[2f4]"></a>rt_hw_context_switch_interrupt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>

<P><STRONG><a name="[21]"></a>PendSV_Handler</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2fd]"></a>rt_hw_context_switch_to</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
</UL>

<P><STRONG><a name="[324]"></a>rt_hw_interrupt_thread_switch</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, context_rvds.o(.text), UNUSED)

<P><STRONG><a name="[1b]"></a>HardFault_Handler</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = HardFault_Handler &rArr; rt_hw_hard_fault_exception &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[325]"></a>____aeabi_memcpy8$ret$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text), UNUSED)

<P><STRONG><a name="[326]"></a>___aeabi_memmove4$ret</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text), UNUSED)

<P><STRONG><a name="[327]"></a>___aeabi_memmove8$ret</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text), UNUSED)

<P><STRONG><a name="[16a]"></a>__aeabi_memcpy</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, memmove.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_direct_block_write
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_direct_block_read
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_from_cache
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[328]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text), UNUSED)

<P><STRONG><a name="[2c8]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memmove
</UL>
<BR>[Called By]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>

<P><STRONG><a name="[13d]"></a>memcpy</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>

<P><STRONG><a name="[329]"></a>memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmove.o(.text), UNUSED)

<P><STRONG><a name="[a9]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[32a]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[32b]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ig_fec_rules_default_set
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_eg_fec_rules_default_set
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encoding_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decoding_reset
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>

<P><STRONG><a name="[26b]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
</UL>

<P><STRONG><a name="[32c]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[aa]"></a>memset</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memset
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>

<P><STRONG><a name="[140]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_STRNLEN
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[b0]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[b1]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[b2]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[b4]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[b5]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[b6]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ul2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[b7]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[157]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[171]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[32d]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[17a]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[32e]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>

<P><STRONG><a name="[32f]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[ad]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[330]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[331]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[ba]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[332]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[ae]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[bc]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a6]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[333]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[334]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[335]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[7e]"></a>main</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, components.o(i.$Sub$$main))
<BR><BR>[Stack]<UL><LI>Max Depth = 300<LI>Call Chain = main &rArr; rtthread_startup &rArr; rt_application_init &rArr; rt_thread_create &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[bf]"></a>AD5272_50TPfuse</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, iic_ad5272.o(i.AD5272_50TPfuse))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AD5272_50TPfuse &rArr; Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
</UL>

<P><STRONG><a name="[c2]"></a>AD5272_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, iic_ad5272.o(i.AD5272_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AD5272_Init &rArr; AD52xx_Write &rArr; IICAD_Wait_Ack &rArr; IICAD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[c4]"></a>AD5272_READ</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, iic_ad5272.o(i.AD5272_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = AD5272_READ &rArr; AD52xx_Read &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
</UL>

<P><STRONG><a name="[c6]"></a>AD5272_Write</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, iic_ad5272.o(i.AD5272_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AD5272_Write &rArr; AD52xx_Write &rArr; IICAD_Wait_Ack &rArr; IICAD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
</UL>

<P><STRONG><a name="[c7]"></a>AD52_Reset_Init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, gpio.o(i.AD52_Reset_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = AD52_Reset_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[c5]"></a>AD52xx_Read</STRONG> (Thumb, 84 bytes, Stack size 56 bytes, iic_ad5272.o(i.AD52xx_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AD52xx_Read &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Wait_Ack
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Start
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Send_Byte
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_READ
</UL>

<P><STRONG><a name="[c0]"></a>AD52xx_Write</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, iic_ad5272.o(i.AD52xx_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD52xx_Write &rArr; IICAD_Wait_Ack &rArr; IICAD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Wait_Ack
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Start
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Write
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_50TPfuse
</UL>

<P><STRONG><a name="[d0]"></a>Auto_Mode</STRONG> (Thumb, 584 bytes, Stack size 56 bytes, ber_test.o(i.Auto_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = Auto_Mode &rArr; odsp_prbs_chk_status &rArr; rx_core_gc_prbs_chk_status_query &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
</UL>

<P><STRONG><a name="[1d]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[d2]"></a>CMD_AD52RD_RUN</STRONG> (Thumb, 242 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_AD52RD_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = CMD_AD52RD_RUN &rArr; AD5272_READ &rArr; AD52xx_Read &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[d8]"></a>CMD_AD52WR_RUN</STRONG> (Thumb, 240 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_AD52WR_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = CMD_AD52WR_RUN &rArr; AD5272_50TPfuse &rArr; Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Write
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_50TPfuse
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[d9]"></a>CMD_CTRLEDCHAN_RUN</STRONG> (Thumb, 502 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_CTRLEDCHAN_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_CTRLEDCHAN_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[da]"></a>CMD_CTRLED_RUN</STRONG> (Thumb, 312 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_CTRLED_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_CTRLED_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Para_Ini
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[dc]"></a>CMD_CTRLPPG_RUN</STRONG> (Thumb, 550 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_CTRLPPG_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_CTRLPPG_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[dd]"></a>CMD_DDMSWITCH_RUN</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_DDMSWITCH_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_DDMSWITCH_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[df]"></a>CMD_IMONRD_RUN</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_IMONRD_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CMD_IMONRD_RUN &rArr; IMON_ReadTwoByte &rArr; IICIMON_Read_Byte &rArr; IICIMON_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[e1]"></a>CMD_IMONWR_RUN</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_IMONWR_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CMD_IMONWR_RUN &rArr; IMON_WriteTwoByte &rArr; IICIMON_Wait_Ack &rArr; IICIMON_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[e3]"></a>CMD_OSFPGPIO_RUN</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_OSFPGPIO_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_OSFPGPIO_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[e4]"></a>CMD_OSFPRDPAGE_RUN</STRONG> (Thumb, 270 bytes, Stack size 160 bytes, usb_interface.o(i.CMD_OSFPRDPAGE_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = CMD_OSFPRDPAGE_RUN &rArr; OSFP1_WriteOneByte &rArr; OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[e7]"></a>CMD_OSFPRD_RUN</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_OSFPRD_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CMD_OSFPRD_RUN &rArr; OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[e9]"></a>CMD_OSFPWR_RUN</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_OSFPWR_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = CMD_OSFPWR_RUN &rArr; OSFP1_WriteOneByte &rArr; OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[ea]"></a>CMD_SETMODE_RUN</STRONG> (Thumb, 1064 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETMODE_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETMODE_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[eb]"></a>CMD_SETRXPAT_RUN</STRONG> (Thumb, 308 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETRXPAT_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETRXPAT_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[ec]"></a>CMD_SETTIMER_RUN</STRONG> (Thumb, 320 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETTIMER_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETTIMER_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[ed]"></a>CMD_SETTRIG_RUN</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETTRIG_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETTRIG_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[ee]"></a>CMD_SETTXPAT_RUN</STRONG> (Thumb, 308 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETTXPAT_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETTXPAT_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[ef]"></a>CMD_SETUSERDEF_RUN</STRONG> (Thumb, 552 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_SETUSERDEF_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_SETUSERDEF_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f0]"></a>CMD_UPABER_RUN</STRONG> (Thumb, 886 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_UPABER_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_UPABER_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f1]"></a>CMD_UPAFEC_RUN</STRONG> (Thumb, 1954 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_UPAFEC_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_UPAFEC_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f2]"></a>CMD_UPCONFIG_RUN</STRONG> (Thumb, 1138 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_UPCONFIG_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_UPCONFIG_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f3]"></a>CMD_UPIBER_RUN</STRONG> (Thumb, 882 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_UPIBER_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_UPIBER_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f4]"></a>CMD_UPIFEC_RUN</STRONG> (Thumb, 1954 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_UPIFEC_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_UPIFEC_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f5]"></a>CMD_UPSNR_RUN</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, usb_interface.o(i.CMD_UPSNR_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_UPSNR_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f6]"></a>CMD_UPUSERDEF_RUN</STRONG> (Thumb, 546 bytes, Stack size 16 bytes, usb_interface.o(i.CMD_UPUSERDEF_RUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CMD_UPUSERDEF_RUN &rArr; Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f7]"></a>CMD_module_insertion_CHECK</STRONG> (Thumb, 256 bytes, Stack size 24 bytes, ber_test.o(i.CMD_module_insertion_CHECK))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CMD_module_insertion_CHECK &rArr; EEpromReadStr
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromReadStr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[f9]"></a>CMD_module_insertion_CLEAR</STRONG> (Thumb, 208 bytes, Stack size 16 bytes, ber_test.o(i.CMD_module_insertion_CLEAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CMD_module_insertion_CLEAR &rArr; EEpromWriteStr &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[fb]"></a>CMD_module_insertion_SET</STRONG> (Thumb, 238 bytes, Stack size 24 bytes, ber_test.o(i.CMD_module_insertion_SET))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CMD_module_insertion_SET &rArr; EEpromWriteStr &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[d3]"></a>Checksum</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(i.Checksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
</UL>

<P><STRONG><a name="[fc]"></a>DDMSwitch_Init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, gpio.o(i.DDMSwitch_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DDMSwitch_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[fd]"></a>DSP_Ctrl_Init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, gpio.o(i.DSP_Ctrl_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DSP_Ctrl_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[20]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[c1]"></a>Delay_Ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, delay.o(i.Delay_Ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_mdelay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_50TPfuse
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[c3]"></a>Delay_Us</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, delay.o(i.Delay_Us))
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_UDELAY
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Write
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Wait_Ack
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Stop
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Start
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Send_Byte
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Read_Byte
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_NAck
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Ack
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Wait_Ack
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Stop
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Start
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Send_Byte
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Read_Byte
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_NAck
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Ack
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Wait_Ack
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Start
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Send_Byte
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Read_Byte
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_NAck
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Ack
</UL>

<P><STRONG><a name="[f8]"></a>EEpromReadStr</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, eeprom.o(i.EEpromReadStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = EEpromReadStr
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read_count
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
</UL>

<P><STRONG><a name="[fa]"></a>EEpromWriteStr</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, eeprom.o(i.EEpromWriteStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EEpromWriteStr &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_DataCacheCmd
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_count
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
</UL>

<P><STRONG><a name="[100]"></a>FLASH_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_flash.o(i.FLASH_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[101]"></a>FLASH_DataCacheCmd</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_flash.o(i.FLASH_DataCacheCmd))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[102]"></a>FLASH_EraseSector</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, stm32f4xx_flash.o(i.FLASH_EraseSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLASH_EraseSector &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[106]"></a>FLASH_GetStatus</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, stm32f4xx_flash.o(i.FLASH_GetStatus))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[104]"></a>FLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_flash.o(i.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[103]"></a>FLASH_ProgramHalfWord</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, stm32f4xx_flash.o(i.FLASH_ProgramHalfWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = FLASH_ProgramHalfWord &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[ff]"></a>FLASH_Unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_flash.o(i.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[105]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_GetStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
</UL>

<P><STRONG><a name="[107]"></a>FreqData_Write</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, si570abb.o(i.FreqData_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = FreqData_Write &rArr; Si570_WriteReg135 &rArr; Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[10b]"></a>Freq_Set</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, si570abb.o(i.Freq_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = Freq_Set &rArr; RunFreq &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[c8]"></a>GPIO_Init</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_Out_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP_Pin_Init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEVEL_Switch_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDPOWER_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DSP_Ctrl_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDMSwitch_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52_Reset_Init
</UL>

<P><STRONG><a name="[163]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
</UL>

<P><STRONG><a name="[1af]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_check
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eeprom_thread_entry
</UL>

<P><STRONG><a name="[de]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP_Pin_Init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDPOWER_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DSP_Ctrl_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDMSwitch_Init
</UL>

<P><STRONG><a name="[c9]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_Out_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP_Pin_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEVEL_Switch_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDPOWER_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DSP_Ctrl_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52_Reset_Init
</UL>

<P><STRONG><a name="[10e]"></a>IICAD_Ack</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_ad5272.o(i.IICAD_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICAD_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Read_Byte
</UL>

<P><STRONG><a name="[10f]"></a>IICAD_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, iic_ad5272.o(i.IICAD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = IICAD_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[110]"></a>IICAD_NAck</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, iic_ad5272.o(i.IICAD_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICAD_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Read_Byte
</UL>

<P><STRONG><a name="[cd]"></a>IICAD_Read_Byte</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, iic_ad5272.o(i.IICAD_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IICAD_Read_Byte &rArr; IICAD_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_NAck
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[cb]"></a>IICAD_Send_Byte</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, iic_ad5272.o(i.IICAD_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICAD_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[ca]"></a>IICAD_Start</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_ad5272.o(i.IICAD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICAD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[cf]"></a>IICAD_Stop</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, iic_ad5272.o(i.IICAD_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICAD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Wait_Ack
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[cc]"></a>IICAD_Wait_Ack</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, iic_ad5272.o(i.IICAD_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICAD_Wait_Ack &rArr; IICAD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[111]"></a>IICIMON_Ack</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, iic_imon.o(i.IICIMON_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICIMON_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Read_Byte
</UL>

<P><STRONG><a name="[112]"></a>IICIMON_Init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, iic_imon.o(i.IICIMON_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = IICIMON_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[113]"></a>IICIMON_NAck</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, iic_imon.o(i.IICIMON_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICIMON_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Read_Byte
</UL>

<P><STRONG><a name="[114]"></a>IICIMON_Read_Byte</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, iic_imon.o(i.IICIMON_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IICIMON_Read_Byte &rArr; IICIMON_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_NAck
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
</UL>

<P><STRONG><a name="[115]"></a>IICIMON_Send_Byte</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, iic_imon.o(i.IICIMON_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICIMON_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
</UL>

<P><STRONG><a name="[116]"></a>IICIMON_Start</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_imon.o(i.IICIMON_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICIMON_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
</UL>

<P><STRONG><a name="[117]"></a>IICIMON_Stop</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, iic_imon.o(i.IICIMON_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICIMON_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Wait_Ack
</UL>

<P><STRONG><a name="[118]"></a>IICIMON_Wait_Ack</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, iic_imon.o(i.IICIMON_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICIMON_Wait_Ack &rArr; IICIMON_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_ReadTwoByte
</UL>

<P><STRONG><a name="[119]"></a>IICOSFP1_Ack</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, iic_osfp.o(i.IICOSFP1_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICOSFP1_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Read_Byte
</UL>

<P><STRONG><a name="[11a]"></a>IICOSFP1_NAck</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, iic_osfp.o(i.IICOSFP1_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Read_Byte
</UL>

<P><STRONG><a name="[11b]"></a>IICOSFP1_Read_Byte</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, iic_osfp.o(i.IICOSFP1_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_NAck
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
</UL>

<P><STRONG><a name="[11c]"></a>IICOSFP1_Send_Byte</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, iic_osfp.o(i.IICOSFP1_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICOSFP1_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
</UL>

<P><STRONG><a name="[11d]"></a>IICOSFP1_Start</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, iic_osfp.o(i.IICOSFP1_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICOSFP1_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
</UL>

<P><STRONG><a name="[11e]"></a>IICOSFP1_Stop</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_osfp.o(i.IICOSFP1_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IICOSFP1_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Wait_Ack
</UL>

<P><STRONG><a name="[11f]"></a>IICOSFP1_Wait_Ack</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, iic_osfp.o(i.IICOSFP1_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IICOSFP1_Wait_Ack &rArr; IICOSFP1_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadBytes
</UL>

<P><STRONG><a name="[120]"></a>IICOSFP_Init</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, iic_osfp.o(i.IICOSFP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = IICOSFP_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[121]"></a>IIC_Ack</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_si570.o(i.IIC_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[122]"></a>IIC_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, iic_si570.o(i.IIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = IIC_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[123]"></a>IIC_NAck</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, iic_si570.o(i.IIC_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[124]"></a>IIC_Read_Byte</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, iic_si570.o(i.IIC_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IIC_Read_Byte &rArr; IIC_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[125]"></a>IIC_Send_Byte</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, iic_si570.o(i.IIC_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[126]"></a>IIC_Start</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, iic_si570.o(i.IIC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[127]"></a>IIC_Stop</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, iic_si570.o(i.IIC_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
</UL>

<P><STRONG><a name="[128]"></a>IIC_Wait_Ack</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, iic_si570.o(i.IIC_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Wait_Ack &rArr; IIC_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[e0]"></a>IMON_ReadTwoByte</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, iic_imon.o(i.IMON_ReadTwoByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IMON_ReadTwoByte &rArr; IICIMON_Read_Byte &rArr; IICIMON_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Wait_Ack
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Stop
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Start
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Send_Byte
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
</UL>

<P><STRONG><a name="[e2]"></a>IMON_WriteTwoByte</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, iic_imon.o(i.IMON_WriteTwoByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IMON_WriteTwoByte &rArr; IICIMON_Wait_Ack &rArr; IICIMON_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Wait_Ack
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Stop
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Start
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
</UL>

<P><STRONG><a name="[129]"></a>LEDPOWER_Init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, gpio.o(i.LEDPOWER_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LEDPOWER_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[12a]"></a>LEVEL_Switch_Init</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, gpio.o(i.LEVEL_Switch_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LEVEL_Switch_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[12b]"></a>MDC_Period</STRONG> (Thumb, 42 bytes, Stack size 4 bytes, mdio_simulation.o(i.MDC_Period))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
</UL>

<P><STRONG><a name="[12d]"></a>MDIO_DEVAD</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, mdio_simulation.o(i.MDIO_DEVAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MDIO_DEVAD &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[12e]"></a>MDIO_IDLE</STRONG> (Thumb, 70 bytes, Stack size 4 bytes, mdio_simulation.o(i.MDIO_IDLE))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MDIO_IDLE
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[12f]"></a>MDIO_Init</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, mdio_simulation.o(i.MDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MDIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[130]"></a>MDIO_OP</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, mdio_simulation.o(i.MDIO_OP))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MDIO_OP &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[131]"></a>MDIO_PRE</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, mdio_simulation.o(i.MDIO_PRE))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MDIO_PRE &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[132]"></a>MDIO_PRTAD</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, mdio_simulation.o(i.MDIO_PRTAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MDIO_PRTAD &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[133]"></a>MDIO_Reg_Addr_Data_Read</STRONG> (Thumb, 104 bytes, Stack size 4 bytes, mdio_simulation.o(i.MDIO_Reg_Addr_Data_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MDIO_Reg_Addr_Data_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[134]"></a>MDIO_Reg_Addr_Data_Write</STRONG> (Thumb, 102 bytes, Stack size 12 bytes, mdio_simulation.o(i.MDIO_Reg_Addr_Data_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[135]"></a>MDIO_ST</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, mdio_simulation.o(i.MDIO_ST))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MDIO_ST &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[136]"></a>MDIO_TA</STRONG> (Thumb, 296 bytes, Stack size 8 bytes, mdio_simulation.o(i.MDIO_TA))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MDIO_TA &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[12c]"></a>MDIO_delay</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, mdio_simulation.o(i.MDIO_delay))
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Read
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>

<P><STRONG><a name="[1c]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[137]"></a>NVIC_Configuration</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, nvic.o(i.NVIC_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = NVIC_Configuration &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[139]"></a>NVIC_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[138]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[2a6]"></a>ODSP_ABS</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, odsp_rtos.o(i.ODSP_ABS))
<BR><BR>[Called By]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_check_rules
</UL>

<P><STRONG><a name="[13a]"></a>ODSP_MDELAY</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, odsp_rtos.o(i.ODSP_MDELAY))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ODSP_MDELAY &rArr; ODSP_UDELAY
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_UDELAY
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_lite_lock
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_start_from_iram_into_application_mode
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wait_fw_reset_control_bits
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;receive_res
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ram_erase_status_done
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_loop_count_is_updated
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_block_application_mode
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem_lock
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
</UL>

<P><STRONG><a name="[13c]"></a>ODSP_MEMCPY</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_rtos.o(i.ODSP_MEMCPY))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ODSP_MEMCPY &rArr; memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
</UL>

<P><STRONG><a name="[13e]"></a>ODSP_MEMSET</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_rtos.o(i.ODSP_MEMSET))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ODSP_MEMSET &rArr; memset
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_tx_xbar_rules_default_set
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_default_set
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_default_set
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_default_set
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_rule_default_chn_mask_set
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_prbs_gen_rules_req
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_info_fetch
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_prbs_chk_rules_req
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_chn_mask_from_rules
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[13f]"></a>ODSP_STRNLEN</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, odsp_rtos.o(i.ODSP_STRNLEN))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ODSP_STRNLEN
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu__log_text_print
</UL>

<P><STRONG><a name="[13b]"></a>ODSP_UDELAY</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, odsp_rtos.o(i.ODSP_UDELAY))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ODSP_UDELAY
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_fetch
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_reset_check
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_wait_done
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
</UL>

<P><STRONG><a name="[e6]"></a>OSFP1_ReadBytes</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, iic_osfp.o(i.OSFP1_ReadBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OSFP1_ReadBytes &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Wait_Ack
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Stop
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Start
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Send_Byte
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
</UL>

<P><STRONG><a name="[e8]"></a>OSFP1_ReadOneByte</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, iic_osfp.o(i.OSFP1_ReadOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Wait_Ack
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Stop
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Start
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Send_Byte
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_WriteOneByte
</UL>

<P><STRONG><a name="[e5]"></a>OSFP1_WriteOneByte</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, iic_osfp.o(i.OSFP1_WriteOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OSFP1_WriteOneByte &rArr; OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP1_ReadOneByte
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Wait_Ack
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Stop
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Start
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP1_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
</UL>

<P><STRONG><a name="[141]"></a>OSFP_Pin_Init</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, gpio.o(i.OSFP_Pin_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = OSFP_Pin_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[142]"></a>POWER_Init</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, gpio.o(i.POWER_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = POWER_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[db]"></a>Para_Ini</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, ber_test.o(i.Para_Ini))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
</UL>

<P><STRONG><a name="[146]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[144]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[145]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[16e]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[143]"></a>RCC_Init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, rcc.o(i.RCC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[147]"></a>RUN_BERT</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, ber_test.o(i.RUN_BERT))
<BR><BR>[Stack]<UL><LI>Max Depth = 2808<LI>Call Chain = RUN_BERT &rArr; Tra_Mode &rArr; odsp_fec_pcs_stats_query &rArr; num_rx_channels_per_100g_slice &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tra_Mode
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[14c]"></a>RUN_SHOW</STRONG> (Thumb, 362 bytes, Stack size 16 bytes, ber_test.o(i.RUN_SHOW))
<BR><BR>[Stack]<UL><LI>Max Depth = 7576<LI>Call Chain = RUN_SHOW &rArr; RateMode_Set &rArr; init_odsp_mode_host_prbs_intf_8 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_squelch
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config_chan
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;monclk_config
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;work_thread_entry
</UL>

<P><STRONG><a name="[14d]"></a>RateMode_Set</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, ber_test.o(i.RateMode_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 7560<LI>Call Chain = RateMode_Set &rArr; init_odsp_mode_host_prbs_intf_8 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_8
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_4
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_2
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_1
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_1bundle_fec_mon
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[10c]"></a>ReStartupConfig</STRONG> (Thumb, 252 bytes, Stack size 64 bytes, si570abb.o(i.ReStartupConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ReStartupConfig &rArr; Si570_WriteReg135 &rArr; Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[10d]"></a>RunFreq</STRONG> (Thumb, 598 bytes, Stack size 96 bytes, si570abb.o(i.RunFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = RunFreq &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[1f]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[15a]"></a>Si570_Out_Init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, gpio.o(i.Si570_Out_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Si570_Out_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[108]"></a>Si570_ReadOneByte</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, iic_si570.o(i.Si570_ReadOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Si570_ReadOneByte &rArr; IIC_Read_Byte &rArr; IIC_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
</UL>

<P><STRONG><a name="[109]"></a>Si570_WriteOneByte</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, iic_si570.o(i.Si570_WriteOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Si570_WriteOneByte &rArr; Si570_ReadOneByte &rArr; IIC_Read_Byte &rArr; IIC_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
</UL>

<P><STRONG><a name="[10a]"></a>Si570_WriteReg135</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, iic_si570.o(i.Si570_WriteReg135))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Si570_WriteReg135 &rArr; Delay_Ms &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
</UL>

<P><STRONG><a name="[22]"></a>SysTick_Handler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, board.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = SysTick_Handler &rArr; rt_tick_increase &rArr; rt_timer_check &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>SystemInit</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[40]"></a>TIM3_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM3_IRQHandler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[15e]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[15d]"></a>TIM_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stm32f4xx_tim.o(i.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[14a]"></a>Tra_Mode</STRONG> (Thumb, 1288 bytes, Stack size 24 bytes, ber_test.o(i.Tra_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 2792<LI>Call Chain = Tra_Mode &rArr; odsp_fec_pcs_stats_query &rArr; num_rx_channels_per_100g_slice &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
</UL>

<P><STRONG><a name="[162]"></a>UART3_Init</STRONG> (Thumb, 168 bytes, Stack size 16 bytes, uart.o(i.UART3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART3_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[58]"></a>UART5_IRQHandler</STRONG> (Thumb, 288 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.UART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART5_IRQHandler &rArr; rt_mq_send &rArr; rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[16d]"></a>UART5_Init</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, uart.o(i.UART5_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART5_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[76]"></a>UART8_IRQHandler</STRONG> (Thumb, 290 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.UART8_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART8_IRQHandler &rArr; rt_mq_send &rArr; rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>USART1_IRQHandler</STRONG> (Thumb, 274 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USART1_IRQHandler &rArr; rt_mq_send &rArr; rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>USART3_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART3_IRQHandler &rArr; rt_interrupt_leave
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[167]"></a>USART_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
</UL>

<P><STRONG><a name="[16c]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[166]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
</UL>

<P><STRONG><a name="[d6]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_console_output
</UL>

<P><STRONG><a name="[168]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[165]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f4xx_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
</UL>

<P><STRONG><a name="[164]"></a>USART_Init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
</UL>

<P><STRONG><a name="[169]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[d5]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_console_output
</UL>

<P><STRONG><a name="[1e]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[16f]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[336]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[ce]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_op_mode_to_intf
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu__log_text_print
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ig_fec_rules_default_set
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_check
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_params_check
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_eg_fec_rules_default_set
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_tx_xbar_rules_default_set
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;num_rx_channels_per_100g_slice
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_default_set
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_check_rules
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_default_set
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_check_rules
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_rules_check
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free_bundle_id_get
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mon_params_check
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_query_req_api_to_fw
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_api_to_fw
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_default_set
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_msg_size
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_is_valid
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wait_fw_reset_control_bits
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;receive_res
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_apply
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_apply
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_bundle_init
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_check_rules
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_params_check
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_api2fw_convert
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_adjust
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_download_firmware
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_squelch
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_rules_default_set
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_rules_default_set
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config_chan
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;monclk_config
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_8
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_4
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_2
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_1
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_1bundle_fec_mon
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;example_init_program_firmware
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52xx_Read
</UL>

<P><STRONG><a name="[337]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[338]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[159]"></a>__hardfp_ceil</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, ceil.o(i.__hardfp_ceil))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_ceil &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[158]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[2f2]"></a>__rt_ffs</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, kservice.o(i.__rt_ffs))
<BR><BR>[Called By]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
</UL>

<P><STRONG><a name="[339]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[33a]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[33b]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[20c]"></a>coder_alignment_get</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.coder_alignment_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = coder_alignment_get
</UL>
<BR>[Called By]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_padding_strip
</UL>

<P><STRONG><a name="[20f]"></a>coder_decoding_reset</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.coder_decoding_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = coder_decoding_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr__decoding_reset
</UL>

<P><STRONG><a name="[215]"></a>coder_encoding_reset</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.coder_encoding_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = coder_encoding_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_send
</UL>

<P><STRONG><a name="[214]"></a>coder_flush</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.coder_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = coder_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_send
</UL>

<P><STRONG><a name="[210]"></a>coder_rx_avail</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.coder_rx_avail))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = coder_rx_avail
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_inbox_check
</UL>

<P><STRONG><a name="[1ac]"></a>coder_uint16_decode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint16_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint16_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_inbox_check
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_uint16_deserialize
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deserialize
</UL>

<P><STRONG><a name="[208]"></a>coder_uint16_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint16_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint16_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_uint16_serialize
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serialize
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_encode
</UL>

<P><STRONG><a name="[1ab]"></a>coder_uint32_decode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint32_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint32_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deserialize
</UL>

<P><STRONG><a name="[314]"></a>coder_uint32_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint32_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint32_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serialize
</UL>

<P><STRONG><a name="[1ad]"></a>coder_uint8_decode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint8_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint8_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_text_deserialize
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deserialize
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_padding_strip
</UL>

<P><STRONG><a name="[20d]"></a>coder_uint8_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint8_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint8_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_text_serialize
</UL>

<P><STRONG><a name="[1ae]"></a>coder_uint8s_decode</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint8s_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint8s_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deserialize
</UL>

<P><STRONG><a name="[315]"></a>coder_uint8s_encode</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, odsp_api.o(i.coder_uint8s_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = coder_uint8s_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serialize
</UL>

<P><STRONG><a name="[294]"></a>dsp_pkr_is_valid_intf</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, odsp_api.o(i.dsp_pkr_is_valid_intf))
<BR><BR>[Called By]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
</UL>

<P><STRONG><a name="[153]"></a>enable_1bundle_fec_mon</STRONG> (Thumb, 224 bytes, Stack size 512 bytes, ber_test.o(i.enable_1bundle_fec_mon))
<BR><BR>[Stack]<UL><LI>Max Depth = 3320<LI>Call Chain = enable_1bundle_fec_mon &rArr; odsp_fec_mon_cfg &rArr; odsp_fec_free_stream_id_get &rArr; fec_tp_stream_broadcast_is_valid &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
</UL>

<P><STRONG><a name="[1b3]"></a>example_init_program_firmware</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, odsp_operation.o(i.example_init_program_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = example_init_program_firmware &rArr; odsp_mcu_download_firmware &rArr; odsp_per_mcu_download_firmware &rArr; mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_download_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[1c4]"></a>fec_read_histogram_bins</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, odsp_api.o(i.fec_read_histogram_bins))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = fec_read_histogram_bins &rArr; fec_stats_read_decoder_storage &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[1c5]"></a>fec_stats_read_decoder_storage</STRONG> (Thumb, 190 bytes, Stack size 48 bytes, odsp_api.o(i.fec_stats_read_decoder_storage))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = fec_stats_read_decoder_storage &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_write
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_read_histogram_bins
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[81]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[156]"></a>init_odsp_mode_host_prbs_intf_1</STRONG> (Thumb, 3892 bytes, Stack size 1984 bytes, ber_test.o(i.init_odsp_mode_host_prbs_intf_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 7536<LI>Call Chain = init_odsp_mode_host_prbs_intf_1 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_enter_operational_state
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
</UL>

<P><STRONG><a name="[155]"></a>init_odsp_mode_host_prbs_intf_2</STRONG> (Thumb, 1994 bytes, Stack size 1984 bytes, ber_test.o(i.init_odsp_mode_host_prbs_intf_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 7536<LI>Call Chain = init_odsp_mode_host_prbs_intf_2 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_enter_operational_state
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
</UL>

<P><STRONG><a name="[154]"></a>init_odsp_mode_host_prbs_intf_4</STRONG> (Thumb, 990 bytes, Stack size 1984 bytes, ber_test.o(i.init_odsp_mode_host_prbs_intf_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 7536<LI>Call Chain = init_odsp_mode_host_prbs_intf_4 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_enter_operational_state
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
</UL>

<P><STRONG><a name="[152]"></a>init_odsp_mode_host_prbs_intf_8</STRONG> (Thumb, 770 bytes, Stack size 1984 bytes, ber_test.o(i.init_odsp_mode_host_prbs_intf_8))
<BR><BR>[Stack]<UL><LI>Max Depth = 7536<LI>Call Chain = init_odsp_mode_host_prbs_intf_8 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_enter_operational_state
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config_chan
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
</UL>

<P><STRONG><a name="[1e8]"></a>$Super$$main</STRONG> (Thumb, 372 bytes, Stack size 32 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = $Super$$main &rArr; example_init_program_firmware &rArr; odsp_mcu_download_firmware &rArr; odsp_per_mcu_download_firmware &rArr; mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_init
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_count_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;example_init_program_firmware
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMON_WriteTwoByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Write
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD5272_50TPfuse
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_thread_entry
</UL>

<P><STRONG><a name="[1ed]"></a>main_mcu_dma0_ch0_start</STRONG> (Thumb, 168 bytes, Stack size 40 bytes, odsp_api.o(i.main_mcu_dma0_ch0_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = main_mcu_dma0_ch0_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[1ef]"></a>main_mcu_dma0_ch0_stop</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, odsp_api.o(i.main_mcu_dma0_ch0_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = main_mcu_dma0_ch0_stop &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[1f0]"></a>main_mcu_dma0_ch0_wait_done</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, odsp_api.o(i.main_mcu_dma0_ch0_wait_done))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = main_mcu_dma0_ch0_wait_done &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_UDELAY
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[87]"></a>main_thread_entry</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, components.o(i.main_thread_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = main_thread_entry &rArr; $Super$$main &rArr; example_init_program_firmware &rArr; odsp_mcu_download_firmware &rArr; odsp_per_mcu_download_firmware &rArr; mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_components_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> components.o(i.rt_application_init)
</UL>
<P><STRONG><a name="[203]"></a>mcu_top_reset</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, odsp_api.o(i.mcu_top_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = mcu_top_reset &rArr; odsp_reg_rmw &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_download_firmware
</UL>

<P><STRONG><a name="[204]"></a>mdio_read</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, mdio_simulation.o(i.mdio_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Read
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_get
</UL>

<P><STRONG><a name="[205]"></a>mdio_write</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, mdio_simulation.o(i.mdio_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
</UL>
<BR>[Called By]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_set
</UL>

<P><STRONG><a name="[1b0]"></a>module_insertion_check</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, ber_test.o(i.module_insertion_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = module_insertion_check &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_mdelay
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_count
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eeprom_thread_entry
</UL>

<P><STRONG><a name="[1e9]"></a>module_insertion_count_read</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ber_test.o(i.module_insertion_count_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = module_insertion_count_read &rArr; flash_read_count &rArr; EEpromReadStr
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read_count
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[150]"></a>monclk_config</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, ber_test.o(i.monclk_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 2480<LI>Call Chain = monclk_config &rArr; odsp_dbg_monclk_set &rArr; odsp_fw_generic_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[212]"></a>msg_deserialize</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.msg_deserialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = msg_deserialize
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_load
</UL>

<P><STRONG><a name="[207]"></a>msg_hdr_encode</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, odsp_api.o(i.msg_hdr_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = msg_hdr_encode &rArr; coder_uint16_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_uint16_serialize
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_text_serialize
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serialize
</UL>

<P><STRONG><a name="[1aa]"></a>msg_hdr_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.msg_hdr_get))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deserialize
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serialize
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_padding_strip
</UL>

<P><STRONG><a name="[211]"></a>msg_hdr_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, odsp_api.o(i.msg_hdr_set))
<BR><BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_load
</UL>

<P><STRONG><a name="[20b]"></a>msg_padding_strip</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, odsp_api.o(i.msg_padding_strip))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = msg_padding_strip &rArr; coder_uint8_decode
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_get
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8_decode
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_alignment_get
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_load
</UL>

<P><STRONG><a name="[213]"></a>msg_serialize</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.msg_serialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = msg_serialize
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_send
</UL>

<P><STRONG><a name="[1a9]"></a>msgr_delegate_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, odsp_api.o(i.msgr_delegate_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delegate_coder
</UL>

<P><STRONG><a name="[21d]"></a>odsp_ahb_brdg__burst_read</STRONG> (Thumb, 164 bytes, Stack size 64 bytes, odsp_api.o(i.odsp_ahb_brdg__burst_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__addr_write
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__block_read
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_receive
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>

<P><STRONG><a name="[21e]"></a>odsp_ahb_brdg__burst_write</STRONG> (Thumb, 104 bytes, Stack size 64 bytes, odsp_api.o(i.odsp_ahb_brdg__burst_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__addr_write
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_send
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>

<P><STRONG><a name="[222]"></a>odsp_ahb_brdg_burst_read</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb_brdg_burst_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
<LI><a href="#[31b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_read
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_rules_fetch
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_status_fetch
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_fetch
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_info_fetch
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_burst_read
</UL>

<P><STRONG><a name="[193]"></a>odsp_ahb_brdg_burst_write</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb_brdg_burst_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;burst_write_verify
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_fw_bdl_rules_fill
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_write
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_burst_write
</UL>

<P><STRONG><a name="[225]"></a>odsp_ahb_brdg_single_read</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ahb_brdg_single_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = odsp_ahb_brdg_single_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
</UL>
<BR>[Called By]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem__single_read
</UL>

<P><STRONG><a name="[226]"></a>odsp_ahb_brdg_single_read_unsafe</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_ahb_brdg_single_read_unsafe))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__ready_wait
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_write
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_read
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_receive
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read
</UL>

<P><STRONG><a name="[227]"></a>odsp_ahb_brdg_single_write</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ahb_brdg_single_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = odsp_ahb_brdg_single_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write_unsafe
</UL>
<BR>[Called By]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem__single_write
</UL>

<P><STRONG><a name="[228]"></a>odsp_ahb_brdg_single_write_unsafe</STRONG> (Thumb, 124 bytes, Stack size 56 bytes, odsp_api.o(i.odsp_ahb_brdg_single_write_unsafe))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__ready_wait
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_write
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_send
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write
</UL>

<P><STRONG><a name="[223]"></a>odsp_ahb_lock</STRONG> (Thumb, 24 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_ahb_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_ahb_lock &rArr; odsp_hw_sem_lock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__sem
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_lock
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_force_lock
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>

<P><STRONG><a name="[224]"></a>odsp_ahb_unlock</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = odsp_ahb_unlock &rArr; odsp_hw_sem_unlock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__sem
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_force_lock
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>

<P><STRONG><a name="[1c0]"></a>odsp_api_channel_to_bundle</STRONG> (Thumb, 46 bytes, Stack size 56 bytes, odsp_api.o(i.odsp_api_channel_to_bundle))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = odsp_api_channel_to_bundle &rArr; sram_bdl_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_id_is_valid
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_fetch
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_chn_index
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mon_params_check
</UL>

<P><STRONG><a name="[22d]"></a>odsp_api_chn_to_fw_chn_table</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_api_chn_to_fw_chn_table))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = odsp_api_chn_to_fw_chn_table &rArr; pkg_intf_to_hw_value
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_intf_to_hw_value
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[22f]"></a>odsp_api_fw_receive</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_api_fw_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pfl_addr
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;receive_res
</UL>

<P><STRONG><a name="[231]"></a>odsp_api_fw_send</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_api_fw_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = odsp_api_fw_send &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pfl_addr
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write_unsafe
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_req
</UL>

<P><STRONG><a name="[1df]"></a>odsp_bundle_enter_operational_state</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_bundle_enter_operational_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 5552<LI>Call Chain = odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_8
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_4
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_2
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_1
</UL>

<P><STRONG><a name="[1c1]"></a>odsp_bundle_fec_mode_get</STRONG> (Thumb, 48 bytes, Stack size 136 bytes, odsp_api.o(i.odsp_bundle_fec_mode_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = odsp_bundle_fec_mode_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mon_params_check
</UL>

<P><STRONG><a name="[1de]"></a>odsp_bundle_rules_default_set</STRONG> (Thumb, 746 bytes, Stack size 72 bytes, odsp_api.o(i.odsp_bundle_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = odsp_bundle_rules_default_set &rArr; odsp_rules_chn_default_set &rArr; odsp_bundle_rules_xbar_default_set &rArr; odsp_chn_tx_xbar_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_id_is_valid
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_rule_default_chn_mask_set
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reverse_gearbox_is_running
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_8
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_4
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_2
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_1
</UL>

<P><STRONG><a name="[237]"></a>odsp_bundle_rules_xbar_default_set</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_bundle_rules_xbar_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = odsp_bundle_rules_xbar_default_set &rArr; odsp_chn_tx_xbar_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_tx_xbar_rules_default_set
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[239]"></a>odsp_channel_adjust</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_channel_adjust))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rebase_by_addr
</UL>

<P><STRONG><a name="[289]"></a>odsp_channel_info</STRONG> (Thumb, 206 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_channel_info))
<BR><BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rebase_by_addr
</UL>

<P><STRONG><a name="[1b6]"></a>odsp_channel_intf_to_fec_intf</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_channel_intf_to_fec_intf))
<BR><BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_query_req_api_to_fw
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_api_to_fw
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1bc]"></a>odsp_channel_intf_to_fec_reg_index</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_channel_intf_to_fec_reg_index))
<BR><BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_fec_xbar_sel_from_bump
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_rsdec_aligned
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_protocol_mode_from_hw_get
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_lock_inst_to_fecl
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[23a]"></a>odsp_channel_is_link_ready</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_channel_is_link_ready))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_channel_is_link_ready &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
</UL>

<P><STRONG><a name="[1e1]"></a>odsp_channels</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_channels))
<BR><BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_channel_valid
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_channels
</UL>

<P><STRONG><a name="[23b]"></a>odsp_check_rules</STRONG> (Thumb, 486 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_check_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = odsp_check_rules &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_check
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_check_rules
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_check_rules
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_in_half_rate_mode
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_check_rules
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_params_check
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_channels
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[241]"></a>odsp_chn_api2fw_convert</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_chn_api2fw_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = odsp_chn_api2fw_convert &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_prbs_gen_rules_req
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_prbs_chk_rules_req
</UL>

<P><STRONG><a name="[206]"></a>odsp_dbg_monclk_set</STRONG> (Thumb, 102 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_dbg_monclk_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 2456<LI>Call Chain = odsp_dbg_monclk_set &rArr; odsp_fw_generic_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;monclk_source_to_fw
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;monclk_rate_to_fw
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;monclk_config
</UL>

<P><STRONG><a name="[24c]"></a>odsp_dbg_translate_fec_mode</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_dbg_translate_fec_mode))
<BR><BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ig_fec_rules_default_set
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_eg_fec_rules_default_set
</UL>

<P><STRONG><a name="[26a]"></a>odsp_dbg_translate_intf</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_dbg_translate_intf))
<BR><BR>[Called By]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_check_rules
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
</UL>

<P><STRONG><a name="[232]"></a>odsp_enter_operational_state</STRONG> (Thumb, 450 bytes, Stack size 2552 bytes, odsp_api.o(i.odsp_enter_operational_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 5528<LI>Call Chain = odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free_bundle_id_get
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bdl_lkup_mask_set
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bdl_lkup_bundle_id_update
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_fw_bdl_rules_fill
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bundle_fec_mode_update
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_write
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_fetch
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_enter_operational_state
</UL>

<P><STRONG><a name="[1b7]"></a>odsp_fec_api_rules_to_fw_rules</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_fec_api_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_fec_api_rules_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_type_api_to_fw
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_api_to_fw
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mode_api_to_fw
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_intf
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_chn_to_fw_chn_table
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_api_to_fw
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[25a]"></a>odsp_fec_free_stream_id_from_mask</STRONG> (Thumb, 110 bytes, Stack size 48 bytes, odsp_api.o(i.odsp_fec_free_stream_id_from_mask))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = odsp_fec_free_stream_id_from_mask &rArr; _num_in_out_fec_streams &rArr; _num_fec_streams
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_stream_is_available
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_num_in_out_fec_streams
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_idist_for_stream_are_available
</UL>
<BR>[Called By]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
</UL>

<P><STRONG><a name="[25c]"></a>odsp_fec_free_stream_id_get</STRONG> (Thumb, 136 bytes, Stack size 160 bytes, odsp_api.o(i.odsp_fec_free_stream_id_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 2712<LI>Call Chain = odsp_fec_free_stream_id_get &rArr; fec_tp_stream_broadcast_is_valid &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_tp_stream_broadcast_is_valid
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_id_is_valid
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_tp_stream_value
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_from_mask
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[1b9]"></a>odsp_fec_fw_rules_to_api_rules</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_fec_fw_rules_to_api_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_fec_fw_rules_to_api_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_type_fw_to_api
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_fw_to_api
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_chn_to_api_chn_table
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mode_fw_to_api
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_fw_to_api
</UL>

<P><STRONG><a name="[25e]"></a>odsp_fec_mode_fw_to_api</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_fec_mode_fw_to_api))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>

<P><STRONG><a name="[1b2]"></a>odsp_fec_mon_cfg</STRONG> (Thumb, 436 bytes, Stack size 96 bytes, odsp_api.o(i.odsp_fec_mon_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 2808<LI>Call Chain = odsp_fec_mon_cfg &rArr; odsp_fec_free_stream_id_get &rArr; fec_tp_stream_broadcast_is_valid &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_rules_is_identical
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mon_params_check
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_api_to_fw
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_1bundle_fec_mon
</UL>

<P><STRONG><a name="[15f]"></a>odsp_fec_pcs_bundle_status_query</STRONG> (Thumb, 378 bytes, Stack size 80 bytes, odsp_api.o(i.odsp_fec_pcs_bundle_status_query))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = odsp_fec_pcs_bundle_status_query &rArr; fec_stream_rsdec_aligned &rArr; fec_nom_data_rate_from_stream_get &rArr; odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_rsdec_aligned
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_slice_to_fec_lock_inst
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_norm_data_rate_to_num_fecls
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_from_stream_get
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_lock_inst_to_fecl
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_to_fec_slices
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tra_Mode
</UL>

<P><STRONG><a name="[160]"></a>odsp_fec_pcs_snapshot_intf</STRONG> (Thumb, 220 bytes, Stack size 64 bytes, odsp_api.o(i.odsp_fec_pcs_snapshot_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 2576<LI>Call Chain = odsp_fec_pcs_snapshot_intf &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_1bundle_fec_mon
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tra_Mode
</UL>

<P><STRONG><a name="[161]"></a>odsp_fec_pcs_stats_query</STRONG> (Thumb, 1504 bytes, Stack size 216 bytes, odsp_api.o(i.odsp_fec_pcs_stats_query))
<BR><BR>[Stack]<UL><LI>Max Depth = 2768<LI>Call Chain = odsp_fec_pcs_stats_query &rArr; num_rx_channels_per_100g_slice &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;num_rx_channels_per_100g_slice
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_protocol_mode_from_hw_get
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_lock_inst_to_fecl
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_input_distribution_list
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_read_histogram_bins
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_1bundle_fec_mon
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tra_Mode
</UL>

<P><STRONG><a name="[236]"></a>odsp_fec_rules_default_set</STRONG> (Thumb, 166 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_fec_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = odsp_fec_rules_default_set &rArr; odsp_ig_fec_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ig_fec_rules_default_set
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_eg_fec_rules_default_set
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_chn_mask_from_rules_get
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_chn_mask_from_rules_get
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mode_needed_rx_channels
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_translate_intf
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[1ce]"></a>odsp_fec_stream_rules_query</STRONG> (Thumb, 206 bytes, Stack size 80 bytes, odsp_api.o(i.odsp_fec_stream_rules_query))
<BR><BR>[Stack]<UL><LI>Max Depth = 2512<LI>Call Chain = odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_query_req_api_to_fw
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_cfg_fw_to_api
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_is_valid
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;num_rx_channels_per_100g_slice
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_tp_stream_broadcast_is_valid
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[1cd]"></a>odsp_fec_tp_stream_value</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_fec_tp_stream_value))
<BR><BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_tp_stream_broadcast_is_valid
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
</UL>

<P><STRONG><a name="[273]"></a>odsp_forward_gearbox_is_running</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_forward_gearbox_is_running))
<BR><BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
</UL>

<P><STRONG><a name="[250]"></a>odsp_fw_bundle_msg_send_and_receive</STRONG> (Thumb, 194 bytes, Stack size 2096 bytes, odsp_api.o(i.odsp_fw_bundle_msg_send_and_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 2424<LI>Call Chain = odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_msg_size
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>
<BR>[Called By]<UL><LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_bundle_init
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[26d]"></a>odsp_fw_channel_msg_send_and_receive</STRONG> (Thumb, 222 bytes, Stack size 2104 bytes, odsp_api.o(i.odsp_fw_channel_msg_send_and_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 2432<LI>Call Chain = odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_msg_size
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_prbs_gen_rules_req
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_prbs_chk_rules_req
</UL>

<P><STRONG><a name="[1d5]"></a>odsp_fw_chn_to_api_chn_table</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_fw_chn_to_api_chn_table))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = odsp_fw_chn_to_api_chn_table &rArr; pkg_intf_to_hw_value
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_intf_to_hw_value
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fw_chn_mask_to_api_chn_mask_convert
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>

<P><STRONG><a name="[262]"></a>odsp_fw_fec_stream_msg_send_and_receive</STRONG> (Thumb, 222 bytes, Stack size 2104 bytes, odsp_api.o(i.odsp_fw_fec_stream_msg_send_and_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 2432<LI>Call Chain = odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_msg_size
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[247]"></a>odsp_fw_generic_msg_send_and_receive</STRONG> (Thumb, 174 bytes, Stack size 2088 bytes, odsp_api.o(i.odsp_fw_generic_msg_send_and_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 2416<LI>Call Chain = odsp_fw_generic_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_msg_size
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>
<BR>[Called By]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>

<P><STRONG><a name="[184]"></a>odsp_fw_mapping_lookup</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_fw_mapping_lookup))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = odsp_fw_mapping_lookup &rArr; pkg_intf_to_hw_value
</UL>
<BR>[Calls]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_channel_valid
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_intf_to_hw_value
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;api_chn_mask_to_fw_chn_mask_convert
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_api2fw_convert
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_adjust
</UL>

<P><STRONG><a name="[26c]"></a>odsp_fw_msg_send_and_receive</STRONG> (Thumb, 152 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_fw_msg_send_and_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_lite_unlock
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_lite_lock
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wait_fw_reset_control_bits
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_req
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_api_control_bits
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;receive_res
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
</UL>

<P><STRONG><a name="[198]"></a>odsp_get_die_inst_from_pkg_ch</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_get_die_inst_from_pkg_ch))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = odsp_get_die_inst_from_pkg_ch &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_base_die
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_input_distribution_list
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[1d8]"></a>odsp_htx_xbar_api_to_fw_rules</STRONG> (Thumb, 334 bytes, Stack size 48 bytes, odsp_api.o(i.odsp_htx_xbar_api_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = odsp_htx_xbar_api_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_xbar_api_input_intf_convert
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_forward_gearbox_is_running
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_chn_to_fw_chn_table
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[221]"></a>odsp_hw_sem</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_hw_sem))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = odsp_hw_sem &rArr; ODSP_MEMSET &rArr; memset
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__sem
</UL>

<P><STRONG><a name="[229]"></a>odsp_hw_sem_lock</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_hw_sem_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = odsp_hw_sem_lock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
</UL>

<P><STRONG><a name="[22a]"></a>odsp_hw_sem_unlock</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_hw_sem_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = odsp_hw_sem_unlock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
</UL>

<P><STRONG><a name="[19e]"></a>odsp_intf_is_rx</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_intf_is_rx))
<BR><BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_pat_mode_is_supported
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_apply
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
</UL>

<P><STRONG><a name="[1fa]"></a>odsp_ireg_force_lock</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_ireg_force_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = odsp_ireg_force_lock &rArr; odsp_ahb_lock &rArr; odsp_hw_sem_lock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>

<P><STRONG><a name="[1f5]"></a>odsp_ireg_lock</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ireg_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = odsp_ireg_lock &rArr; odsp_ahb_lock &rArr; odsp_hw_sem_lock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
<LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_rules_host_addr
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_vector_set
</UL>

<P><STRONG><a name="[187]"></a>odsp_ireg_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ireg_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atom_checksum_start
<LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
<LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_rules_host_addr
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ram_erase_status_done
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_wait_done
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_start
</UL>

<P><STRONG><a name="[1ee]"></a>odsp_ireg_rmw</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ireg_rmw))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_stop
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_start
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_vector_set
</UL>

<P><STRONG><a name="[1f7]"></a>odsp_ireg_unlock</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_ireg_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = odsp_ireg_unlock &rArr; odsp_ahb_unlock &rArr; odsp_hw_sem_unlock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
<LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_rules_host_addr
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_vector_set
</UL>

<P><STRONG><a name="[186]"></a>odsp_ireg_write</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ireg_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write_unsafe
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atom_checksum_start
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_start
</UL>

<P><STRONG><a name="[274]"></a>odsp_is_fw_running_ok</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_is_fw_running_ok))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = odsp_is_fw_running_ok &rArr; per_mcu_loop_count_is_updated &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_loop_count_is_updated
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_fw_mode_query
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_apply
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_apply
</UL>

<P><STRONG><a name="[1c6]"></a>odsp_lock</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_lock &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_fw_mode_query
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_bundle_init
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_is_link_ready
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_squelch
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>

<P><STRONG><a name="[1e3]"></a>odsp_ltx_xbar_api_to_fw_rules</STRONG> (Thumb, 334 bytes, Stack size 48 bytes, odsp_api.o(i.odsp_ltx_xbar_api_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = odsp_ltx_xbar_api_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_xbar_api_input_intf_convert
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_chn_to_fw_chn_table
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reverse_gearbox_is_running
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1b4]"></a>odsp_mcu_download_firmware</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_mcu_download_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = odsp_mcu_download_firmware &rArr; odsp_per_mcu_download_firmware &rArr; mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_top_reset
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_download_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;example_init_program_firmware
</UL>

<P><STRONG><a name="[1f3]"></a>odsp_mcu_download_start</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_mcu_download_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[192]"></a>odsp_mcu_fw2pfl_addr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_mcu_fw2pfl_addr))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;burst_write_verify
<LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
<LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_rules_host_addr
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_info_fetch
</UL>

<P><STRONG><a name="[280]"></a>odsp_mcu_get_inline_firmware</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_mcu_get_inline_firmware))
<BR><BR>[Called By]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_download_firmware
</UL>

<P><STRONG><a name="[1f6]"></a>odsp_mcu_init_idram</STRONG> (Thumb, 308 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_mcu_init_idram))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_mcu_init_idram &rArr; ram_erase_status_done &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ram_erase_status_done
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[201]"></a>odsp_mcu_vector_set</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_mcu_vector_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = odsp_mcu_vector_set &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_start_from_iram_into_application_mode
</UL>

<P><STRONG><a name="[174]"></a>odsp_num_slices_per_stream</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_num_slices_per_stream))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nom_data_rate_to_num_fec_slices
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_idist_set
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_idist_clear
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_idist_for_stream_are_available
</UL>

<P><STRONG><a name="[27b]"></a>odsp_package_cache_clear</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_package_cache_clear))
<BR><BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>

<P><STRONG><a name="[19a]"></a>odsp_package_channel_to_fec_stream</STRONG> (Thumb, 60 bytes, Stack size 136 bytes, odsp_api.o(i.odsp_package_channel_to_fec_stream))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = odsp_package_channel_to_fec_stream &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_id_is_valid
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;num_rx_channels_per_100g_slice
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[182]"></a>odsp_package_get_base_die</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_package_get_base_die))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;api_chn_mask_to_fw_chn_mask_convert
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_type
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_enabled
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>

<P><STRONG><a name="[23c]"></a>odsp_package_get_channels</STRONG> (Thumb, 46 bytes, Stack size 20 bytes, odsp_api.o(i.odsp_package_get_channels))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = odsp_package_get_channels
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channels
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[1a2]"></a>odsp_package_get_driver_type</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_package_get_driver_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = odsp_package_get_driver_type &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_default_set
</UL>

<P><STRONG><a name="[183]"></a>odsp_package_get_type</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_package_get_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_query_efuse
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_type
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_enabled
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_base_die
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_cache_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fw_chn_mask_to_api_chn_mask_convert
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;api_chn_mask_to_fw_chn_mask_convert
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_api2fw_convert
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_adjust
</UL>

<P><STRONG><a name="[27a]"></a>odsp_package_override_enabled</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_package_override_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = odsp_package_override_enabled &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_package_type_to_api_convert
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;package_override_cache
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_base_die
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>

<P><STRONG><a name="[27c]"></a>odsp_package_override_type</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_package_override_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = odsp_package_override_type &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_base_die
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>

<P><STRONG><a name="[27d]"></a>odsp_package_query_efuse</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_package_query_efuse))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_is_programmed
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_fetch
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_package_type_to_api_convert
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>

<P><STRONG><a name="[277]"></a>odsp_per_mcu_download_firmware</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_per_mcu_download_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = odsp_per_mcu_download_firmware &rArr; mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_start_from_iram_into_application_mode
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_get_inline_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_download_firmware
</UL>

<P><STRONG><a name="[1fc]"></a>odsp_per_mcu_reset</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_per_mcu_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_per_mcu_reset &rArr; odsp_reg_rmw &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_reset_check
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_start_from_iram_into_application_mode
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>

<P><STRONG><a name="[1f4]"></a>odsp_per_mcu_runstall</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_per_mcu_runstall))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = odsp_per_mcu_runstall &rArr; odsp_reg_rmw &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[281]"></a>odsp_prbs_chk_config</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_prbs_chk_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 2576<LI>Call Chain = odsp_prbs_chk_config &rArr; prbs_chk_rules_apply &rArr; rx_prbs_chk_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_rules_check
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_apply
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
</UL>

<P><STRONG><a name="[283]"></a>odsp_prbs_chk_rules_default_set</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_prbs_chk_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = odsp_prbs_chk_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
</UL>

<P><STRONG><a name="[d1]"></a>odsp_prbs_chk_status</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_prbs_chk_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = odsp_prbs_chk_status &rArr; rx_core_gc_prbs_chk_status_query &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_intf_is_rx
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config_chan
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>

<P><STRONG><a name="[285]"></a>odsp_prbs_gen_config</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_prbs_gen_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 2584<LI>Call Chain = odsp_prbs_gen_config &rArr; prbs_gen_rules_apply &rArr; tx_prbs_gen_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_rules_check
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_apply
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config_chan
</UL>

<P><STRONG><a name="[287]"></a>odsp_prbs_gen_rules_default_set</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_prbs_gen_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = odsp_prbs_gen_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config_chan
</UL>

<P><STRONG><a name="[288]"></a>odsp_rebase_by_addr</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_rebase_by_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_info
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_adjust
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_write
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>

<P><STRONG><a name="[1be]"></a>odsp_reg_channel_read</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_reg_channel_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + In Cycle
<LI>Call Chain = odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rebase_by_addr
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_fec_xbar_sel_from_bump
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_fetch
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_rsdec_aligned
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_protocol_mode_from_hw_get
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_lock_inst_to_fecl
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_pam_read_mode
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_is_link_ready
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_value
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[249]"></a>odsp_reg_channel_rmw</STRONG> (Thumb, 40 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_reg_channel_rmw))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = odsp_reg_channel_rmw &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rebase_by_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_fetch
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_squelch
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
</UL>

<P><STRONG><a name="[1bd]"></a>odsp_reg_channel_write</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_reg_channel_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rebase_by_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_lock_inst_to_fecl
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
</UL>

<P><STRONG><a name="[28a]"></a>odsp_reg_get</STRONG> (Thumb, 40 bytes, Stack size 32 bytes, odsp_operation.o(i.odsp_reg_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>

<P><STRONG><a name="[1d2]"></a>odsp_reg_read</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_reg_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_efuse_is_programmed
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__ready_wait
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_read
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__block_read
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_lite_unlock
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_lite_lock
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_reset_check
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_un_self_init
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wait_fw_reset_control_bits
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_req
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;self_init_forcing_state_restore
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;self_init_forcing_state_get
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_api_control_bits
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;receive_res
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_loop_count_is_updated
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_fw_mode_query
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem_unlock
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem_lock
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_query_efuse
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_type
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_enabled
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_driver_type
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[1fd]"></a>odsp_reg_rmw</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_reg_rmw))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = odsp_reg_rmw &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_top_reset
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_runstall
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
</UL>

<P><STRONG><a name="[28b]"></a>odsp_reg_set</STRONG> (Thumb, 40 bytes, Stack size 32 bytes, odsp_operation.o(i.odsp_reg_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
</UL>

<P><STRONG><a name="[1d3]"></a>odsp_reg_write</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_reg_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_write
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__block_write
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_un_self_init
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_req
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;self_init_forcing_state_restore
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_api_control_bits
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_write
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_runstall
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
</UL>

<P><STRONG><a name="[234]"></a>odsp_reverse_gearbox_is_running</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_reverse_gearbox_is_running))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[28f]"></a>odsp_rx_dsp_pkr_snr_read_db_fixp</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_rx_dsp_pkr_snr_read_db_fixp))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = odsp_rx_dsp_pkr_snr_read_db_fixp &rArr; odsp_rx_pam_read_mode &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_pam_read_mode
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_value
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_format_fixp
</UL>
<BR>[Called By]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
</UL>

<P><STRONG><a name="[292]"></a>odsp_rx_dsp_snr_format_fixp</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_rx_dsp_snr_format_fixp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = odsp_rx_dsp_snr_format_fixp
</UL>
<BR>[Called By]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_pkr_snr_read_db_fixp
</UL>

<P><STRONG><a name="[148]"></a>odsp_rx_dsp_snr_read_db</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_rx_dsp_snr_read_db))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = odsp_rx_dsp_snr_read_db &rArr; odsp_rx_dsp_snr_read_db_fixp &rArr; odsp_rx_dsp_pkr_snr_read_db_fixp &rArr; odsp_rx_pam_read_mode &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db_fixp
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
</UL>

<P><STRONG><a name="[293]"></a>odsp_rx_dsp_snr_read_db_fixp</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_rx_dsp_snr_read_db_fixp))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = odsp_rx_dsp_snr_read_db_fixp &rArr; odsp_rx_dsp_pkr_snr_read_db_fixp &rArr; odsp_rx_pam_read_mode &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_translate_intf
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_is_link_ready
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dsp_pkr_is_valid_intf
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_pkr_snr_read_db_fixp
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_snr_read_db
</UL>

<P><STRONG><a name="[290]"></a>odsp_rx_dsp_snr_read_value</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_rx_dsp_snr_read_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_rx_dsp_snr_read_value &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_pkr_snr_read_db_fixp
</UL>

<P><STRONG><a name="[1e5]"></a>odsp_sfec_api_rules_to_fw_rules</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_sfec_api_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = odsp_sfec_api_rules_to_fw_rules
</UL>
<BR>[Calls]<UL><LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_api_rules_mode_to_fw
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_api_rules_cfg_to_fw
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[29a]"></a>odsp_smem_burst_read</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_smem_burst_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = odsp_smem_burst_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smem_ring_hal_block_read
</UL>

<P><STRONG><a name="[29b]"></a>odsp_smem_burst_write</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_smem_burst_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = odsp_smem_burst_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smem_ring_hal_block_write
</UL>

<P><STRONG><a name="[29c]"></a>odsp_smem_reg_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_smem_reg_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = odsp_smem_reg_read &rArr; odsp_smem__single_read &rArr; odsp_ahb_brdg_single_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem__single_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smem_ring_hal_read
</UL>

<P><STRONG><a name="[29d]"></a>odsp_smem_reg_write</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_smem_reg_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = odsp_smem_reg_write &rArr; odsp_smem__single_write &rArr; odsp_ahb_brdg_single_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem__single_write
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smem_ring_hal_write
</UL>

<P><STRONG><a name="[255]"></a>odsp_sram_fec_stream_update</STRONG> (Thumb, 98 bytes, Stack size 152 bytes, odsp_api.o(i.odsp_sram_fec_stream_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = odsp_sram_fec_stream_update &rArr; sram_stream_lkup_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_stream_id_update
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_set
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_clear
<LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_fec_rules_update
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[19b]"></a>odsp_stream_fec_rules_status_get</STRONG> (Thumb, 112 bytes, Stack size 144 bytes, odsp_api.o(i.odsp_stream_fec_rules_status_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_from_stream_get
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
</UL>

<P><STRONG><a name="[151]"></a>odsp_tx_squelch</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_tx_squelch))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = odsp_tx_squelch &rArr; odsp_reg_channel_rmw &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[1c7]"></a>odsp_unlock</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_unlock &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_fw_mode_query
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_bundle_init
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_is_link_ready
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stats_read_decoder_storage
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_tx_squelch
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_snapshot_intf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>

<P><STRONG><a name="[14f]"></a>prbs_checker_config_chan</STRONG> (Thumb, 284 bytes, Stack size 72 bytes, ber_test.o(i.prbs_checker_config_chan))
<BR><BR>[Stack]<UL><LI>Max Depth = 2648<LI>Call Chain = prbs_checker_config_chan &rArr; odsp_prbs_chk_config &rArr; prbs_chk_rules_apply &rArr; rx_prbs_chk_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_rules_default_set
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RateMode_Set
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[14e]"></a>prbs_generator_config_chan</STRONG> (Thumb, 486 bytes, Stack size 48 bytes, ber_test.o(i.prbs_generator_config_chan))
<BR><BR>[Stack]<UL><LI>Max Depth = 2632<LI>Call Chain = prbs_generator_config_chan &rArr; odsp_prbs_gen_config &rArr; prbs_gen_rules_apply &rArr; tx_prbs_gen_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_rules_default_set
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_odsp_mode_host_prbs_intf_8
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[2b3]"></a>ring_alignment_get</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, odsp_api.o(i.ring_alignment_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ring_alignment_get &rArr; ring_hal_alignment
</UL>
<BR>[Calls]<UL><LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_alignment
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__flush
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__alignment_get
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__enqueue
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__dequeue
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_free_count
</UL>

<P><STRONG><a name="[2b2]"></a>ring_avail_count</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, odsp_api.o(i.ring_avail_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__avail_count
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__tx_avail
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__rx_avail
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__dequeue
</UL>

<P><STRONG><a name="[2c4]"></a>ring_dequeue</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.ring_dequeue))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__dequeue
</UL>
<BR>[Called By]<UL><LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__dequeue
</UL>

<P><STRONG><a name="[2c9]"></a>ring_enqueue</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.ring_enqueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__enqueue
</UL>
<BR>[Called By]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
</UL>

<P><STRONG><a name="[2c3]"></a>ring_flush</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, odsp_api.o(i.ring_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ring_flush &rArr; ring__cidx_update &rArr; ring_hal_write
</UL>
<BR>[Calls]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__cidx_update
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_read
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encoding_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decoding_reset
</UL>

<P><STRONG><a name="[2b6]"></a>ring_free_count</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, odsp_api.o(i.ring_free_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__avail_count
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__enqueue
</UL>

<P><STRONG><a name="[2b9]"></a>ring_hal_alignment</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.ring_hal_alignment))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ring_hal_alignment
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>

<P><STRONG><a name="[2b4]"></a>ring_hal_block_read</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, odsp_api.o(i.ring_hal_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ring_hal_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__dequeue
</UL>

<P><STRONG><a name="[2b7]"></a>ring_hal_block_write</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, odsp_api.o(i.ring_hal_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ring_hal_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__enqueue
</UL>

<P><STRONG><a name="[2ae]"></a>ring_hal_read</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.ring_hal_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ring_hal_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__avail_count
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_flush
</UL>

<P><STRONG><a name="[2b0]"></a>ring_hal_write</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, odsp_api.o(i.ring_hal_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ring_hal_write
</UL>
<BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__pidx_update
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__cidx_update
</UL>

<P><STRONG><a name="[2ca]"></a>rt_application_init</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, components.o(i.rt_application_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = rt_application_init &rArr; rt_thread_create &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2d5]"></a>rt_components_board_init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, components.o(i.rt_components_board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_components_board_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[1f1]"></a>rt_components_init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, components.o(i.rt_components_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_components_init
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_thread_entry
</UL>

<P><STRONG><a name="[149]"></a>rt_enter_critical</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, scheduler.o(i.rt_enter_critical))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_enter_critical
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_console_output
</UL>

<P><STRONG><a name="[14b]"></a>rt_exit_critical</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, scheduler.o(i.rt_exit_critical))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_BERT
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_console_output
</UL>

<P><STRONG><a name="[2cf]"></a>rt_free</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, mem.o(i.rt_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;plug_holes
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_kprintf
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
</UL>
<BR>[Called By]<UL><LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[2d7]"></a>rt_heap_begin_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, board.o(i.rt_heap_begin_get))
<BR><BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[2d6]"></a>rt_heap_end_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, board.o(i.rt_heap_end_get))
<BR><BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[2d4]"></a>rt_hw_board_init</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, board.o(i.rt_hw_board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = rt_hw_board_init &rArr; rt_system_heap_init &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_heap_end_get
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_heap_begin_get
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_components_board_init
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_Out_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSFP_Pin_Init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEVEL_Switch_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDPOWER_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICOSFP_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICIMON_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICAD_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DSP_Ctrl_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDMSwitch_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD52_Reset_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2d9]"></a>rt_hw_console_output</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, board.o(i.rt_hw_console_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_hw_console_output &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_kprintf
</UL>

<P><STRONG><a name="[a7]"></a>rt_hw_hard_fault_exception</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, cpuport.o(i.rt_hw_hard_fault_exception))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = rt_hw_hard_fault_exception &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_kprintf
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>

<P><STRONG><a name="[17d]"></a>rt_hw_stack_init</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, cpuport.o(i.rt_hw_stack_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rt_hw_stack_init
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[d4]"></a>rt_interrupt_enter</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, irq.o(i.rt_interrupt_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_interrupt_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[d7]"></a>rt_interrupt_leave</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, irq.o(i.rt_interrupt_leave))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_interrupt_leave
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[2d1]"></a>rt_kprintf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, kservice.o(i.rt_kprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_console_output
</UL>
<BR>[Called By]<UL><LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
</UL>

<P><STRONG><a name="[2e3]"></a>rt_malloc</STRONG> (Thumb, 336 bytes, Stack size 24 bytes, mem.o(i.rt_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = rt_malloc &rArr; rt_sem_take &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
</UL>
<BR>[Called By]<UL><LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[2e9]"></a>rt_memcpy</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, kservice.o(i.rt_memcpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[17c]"></a>rt_memset</STRONG> (Thumb, 72 bytes, Stack size 20 bytes, kservice.o(i.rt_memset))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rt_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[1ea]"></a>rt_mq_init</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, ipc.o(i.rt_mq_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rt_mq_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_object_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[2e5]"></a>rt_mq_recv</STRONG> (Thumb, 252 bytes, Stack size 48 bytes, ipc.o(i.rt_mq_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = rt_mq_recv &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memcpy
<LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_thread_entry
</UL>

<P><STRONG><a name="[16b]"></a>rt_mq_send</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, ipc.o(i.rt_mq_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rt_mq_send &rArr; rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memcpy
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_resume
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[2eb]"></a>rt_object_allocate</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, object.o(i.rt_object_allocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = rt_object_allocate &rArr; rt_malloc &rArr; rt_sem_take &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_information
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strncpy
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memset
<LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[2ef]"></a>rt_object_delete</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, object.o(i.rt_object_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[2f1]"></a>rt_object_detach</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, object.o(i.rt_object_detach))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_object_detach
</UL>
<BR>[Calls]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[2ec]"></a>rt_object_get_information</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, object.o(i.rt_object_get_information))
<BR><BR>[Called By]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[2e4]"></a>rt_object_init</STRONG> (Thumb, 86 bytes, Stack size 32 bytes, object.o(i.rt_object_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_information
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strncpy
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
</UL>
<BR>[Called By]<UL><LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_init
</UL>

<P><STRONG><a name="[302]"></a>rt_object_is_systemobject</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, object.o(i.rt_object_is_systemobject))
<BR><BR>[Called By]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[2ce]"></a>rt_schedule</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, scheduler.o(i.rt_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch_interrupt
<LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ffs
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[2f5]"></a>rt_schedule_insert_thread</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, scheduler.o(i.rt_schedule_insert_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_schedule_insert_thread
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
</UL>

<P><STRONG><a name="[2f6]"></a>rt_schedule_remove_thread</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, scheduler.o(i.rt_schedule_remove_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_schedule_remove_thread
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[2f7]"></a>rt_sem_init</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, ipc.o(i.rt_sem_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rt_sem_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_object_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
</UL>

<P><STRONG><a name="[2d3]"></a>rt_sem_release</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, ipc.o(i.rt_sem_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rt_sem_release &rArr; rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_resume
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[2d0]"></a>rt_sem_take</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ipc.o(i.rt_sem_take))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_sem_take &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[2f8]"></a>rt_soft_timer_check</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, timer_1.o(i.rt_soft_timer_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rt_soft_timer_check &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
</UL>

<P><STRONG><a name="[310]"></a>rt_strlen</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, kservice.o(i.rt_strlen))
<BR><BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[2ed]"></a>rt_strncpy</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, kservice.o(i.rt_strncpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[2d8]"></a>rt_system_heap_init</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, mem.o(i.rt_system_heap_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = rt_system_heap_init &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_kprintf
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[2fa]"></a>rt_system_scheduler_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, scheduler.o(i.rt_system_scheduler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_system_scheduler_init
</UL>
<BR>[Calls]<UL><LI><a href="#[2fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2fc]"></a>rt_system_scheduler_start</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, scheduler.o(i.rt_system_scheduler_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_system_scheduler_start
</UL>
<BR>[Calls]<UL><LI><a href="#[2fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch_to
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ffs
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2fe]"></a>rt_system_timer_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, timer_1.o(i.rt_system_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_system_timer_init
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2ff]"></a>rt_system_timer_thread_init</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, timer_1.o(i.rt_system_timer_thread_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = rt_system_timer_thread_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_init
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[2cb]"></a>rt_thread_create</STRONG> (Thumb, 80 bytes, Stack size 56 bytes, thread.o(i.rt_thread_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = rt_thread_create &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
</UL>

<P><STRONG><a name="[1b1]"></a>rt_thread_delay</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, thread.o(i.rt_thread_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_thread_delay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;work_thread_entry
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eeprom_thread_entry
</UL>

<P><STRONG><a name="[82]"></a>rt_thread_exit</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, thread.o(i.rt_thread_exit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_thread_exit &rArr; rt_timer_detach &rArr; rt_object_detach
</UL>
<BR>[Calls]<UL><LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[302]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[303]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
</UL>
<BR>[Address Reference Count : 1]<UL><LI> thread.o(i._rt_thread_init)
</UL>
<P><STRONG><a name="[304]"></a>rt_thread_idle_excute</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, idle.o(i.rt_thread_idle_excute))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = rt_thread_idle_excute &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[302]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[305]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_has_defunct_thread
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_entry
</UL>

<P><STRONG><a name="[306]"></a>rt_thread_idle_init</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, idle.o(i.rt_thread_idle_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = rt_thread_idle_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[1eb]"></a>rt_thread_init</STRONG> (Thumb, 52 bytes, Stack size 56 bytes, thread.o(i.rt_thread_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
<LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_thread_init
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[fe]"></a>rt_thread_mdelay</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, thread.o(i.rt_thread_mdelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_from_millisecond
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_check
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[2dc]"></a>rt_thread_resume</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, thread.o(i.rt_thread_resume))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_resume
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
</UL>

<P><STRONG><a name="[2da]"></a>rt_thread_self</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, thread.o(i.rt_thread_self))
<BR><BR>[Called By]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>

<P><STRONG><a name="[300]"></a>rt_thread_sleep</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, thread.o(i.rt_thread_sleep))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_mdelay
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_delay
</UL>

<P><STRONG><a name="[1ec]"></a>rt_thread_startup</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, thread.o(i.rt_thread_startup))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_thread_startup &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
<LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_thread_init
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
</UL>

<P><STRONG><a name="[2de]"></a>rt_thread_suspend</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, thread.o(i.rt_thread_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_thread_suspend &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
</UL>

<P><STRONG><a name="[83]"></a>rt_thread_timeout</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, thread.o(i.rt_thread_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_thread_timeout &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
</UL>
<BR>[Address Reference Count : 1]<UL><LI> thread.o(i._rt_thread_init)
</UL>
<P><STRONG><a name="[30b]"></a>rt_thread_yield</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, thread.o(i.rt_thread_yield))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_thread_yield &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>

<P><STRONG><a name="[307]"></a>rt_tick_from_millisecond</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, clock.o(i.rt_tick_from_millisecond))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_mdelay
</UL>

<P><STRONG><a name="[2e6]"></a>rt_tick_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, clock.o(i.rt_tick_get))
<BR><BR>[Called By]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[15b]"></a>rt_tick_increase</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, clock.o(i.rt_tick_increase))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_tick_increase &rArr; rt_timer_check &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[30c]"></a>rt_timer_check</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, timer_1.o(i.rt_timer_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rt_timer_check &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
<LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>

<P><STRONG><a name="[2e7]"></a>rt_timer_control</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, timer_1.o(i.rt_timer_control))
<BR><BR>[Called By]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[301]"></a>rt_timer_detach</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, timer_1.o(i.rt_timer_detach))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_timer_detach &rArr; rt_object_detach
</UL>
<BR>[Calls]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[17e]"></a>rt_timer_init</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, timer_1.o(i.rt_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[2e8]"></a>rt_timer_start</STRONG> (Thumb, 282 bytes, Stack size 32 bytes, timer_1.o(i.rt_timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[30e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
<LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_sleep
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[309]"></a>rt_timer_stop</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, timer_1.o(i.rt_timer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
</UL>

<P><STRONG><a name="[2e2]"></a>rt_vsnprintf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, kservice.o(i.rt_vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[310]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strlen
<LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skip_atoi
<LI><a href="#[311]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>
<BR>[Called By]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_kprintf
</UL>

<P><STRONG><a name="[be]"></a>rtthread_startup</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, components.o(i.rtthread_startup))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = rtthread_startup &rArr; rt_application_init &rArr; rt_thread_create &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
<LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_thread_init
<LI><a href="#[2fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_init
<LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
<LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_init
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[86]"></a>eeprom_thread_entry</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, main.o(i.eeprom_thread_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = eeprom_thread_entry &rArr; module_insertion_check &rArr; rt_thread_mdelay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_delay
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_check
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[84]"></a>uart_thread_entry</STRONG> (Thumb, 370 bytes, Stack size 8 bytes, main.o(i.uart_thread_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = uart_thread_entry &rArr; CMD_OSFPRDPAGE_RUN &rArr; OSFP1_WriteOneByte &rArr; OSFP1_ReadOneByte &rArr; IICOSFP1_Read_Byte &rArr; IICOSFP1_NAck
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_SET
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CLEAR
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_module_insertion_CHECK
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPUSERDEF_RUN
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPSNR_RUN
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIFEC_RUN
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPIBER_RUN
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPCONFIG_RUN
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPAFEC_RUN
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_UPABER_RUN
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETUSERDEF_RUN
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTXPAT_RUN
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTRIG_RUN
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETTIMER_RUN
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETRXPAT_RUN
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SETMODE_RUN
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPWR_RUN
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRD_RUN
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPRDPAGE_RUN
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_OSFPGPIO_RUN
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONWR_RUN
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_IMONRD_RUN
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_DDMSWITCH_RUN
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLPPG_RUN
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLED_RUN
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_CTRLEDCHAN_RUN
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52WR_RUN
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_AD52RD_RUN
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[85]"></a>work_thread_entry</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, main.o(i.work_thread_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 7576<LI>Call Chain = work_thread_entry &rArr; RUN_SHOW &rArr; RateMode_Set &rArr; init_odsp_mode_host_prbs_intf_8 &rArr; odsp_bundle_enter_operational_state &rArr; odsp_enter_operational_state &rArr; odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_delay
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[1cf]"></a>flash_read_count</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, ber_test.o(i.flash_read_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = flash_read_count &rArr; EEpromReadStr
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromReadStr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_count_read
</UL>

<P><STRONG><a name="[1d0]"></a>flash_write_count</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ber_test.o(i.flash_write_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = flash_write_count &rArr; EEpromWriteStr &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_insertion_check
</UL>

<P><STRONG><a name="[15c]"></a>SetSysClock</STRONG> (Thumb, 272 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[a3]"></a>rti_board_end</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, components.o(i.rti_board_end))
<BR>[Address Reference Count : 1]<UL><LI> components.o(.rti_fn.1.end)
</UL>
<P><STRONG><a name="[a2]"></a>rti_board_start</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, components.o(i.rti_board_start))
<BR>[Address Reference Count : 1]<UL><LI> components.o(.rti_fn.0.end)
</UL>
<P><STRONG><a name="[a4]"></a>rti_end</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, components.o(i.rti_end))
<BR>[Address Reference Count : 1]<UL><LI> components.o(.rti_fn.6.end)
</UL>
<P><STRONG><a name="[a1]"></a>rti_start</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, components.o(i.rti_start))
<BR>[Address Reference Count : 1]<UL><LI> components.o(.rti_fn.0)
</UL>
<P><STRONG><a name="[305]"></a>_has_defunct_thread</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, idle.o(i._has_defunct_thread))
<BR><BR>[Called By]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[89]"></a>rt_thread_idle_entry</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, idle.o(i.rt_thread_idle_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = rt_thread_idle_entry &rArr; rt_thread_idle_excute &rArr; rt_object_delete &rArr; rt_free &rArr; rt_kprintf &rArr; rt_vsnprintf &rArr; print_number
</UL>
<BR>[Calls]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>
<BR>[Address Reference Count : 1]<UL><LI> idle.o(i.rt_thread_idle_init)
</UL>
<P><STRONG><a name="[2db]"></a>rt_ipc_list_resume</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ipc.o(i.rt_ipc_list_resume))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
</UL>
<BR>[Called By]<UL><LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
</UL>

<P><STRONG><a name="[2dd]"></a>rt_ipc_list_suspend</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, ipc.o(i.rt_ipc_list_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_before
</UL>
<BR>[Called By]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_recv
</UL>

<P><STRONG><a name="[2e0]"></a>rt_ipc_object_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ipc.o(i.rt_ipc_object_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ipc_object_init
</UL>
<BR>[Calls]<UL><LI><a href="#[2e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_init
</UL>

<P><STRONG><a name="[2e1]"></a>rt_list_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ipc.o(i.rt_list_init))
<BR><BR>[Called By]<UL><LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_object_init
</UL>

<P><STRONG><a name="[2df]"></a>rt_list_insert_before</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ipc.o(i.rt_list_insert_before))
<BR><BR>[Called By]<UL><LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
</UL>

<P><STRONG><a name="[2ea]"></a>rt_list_isempty</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ipc.o(i.rt_list_isempty))
<BR><BR>[Called By]<UL><LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_mq_send
</UL>

<P><STRONG><a name="[311]"></a>print_number</STRONG> (Thumb, 400 bytes, Stack size 60 bytes, kservice.o(i.print_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = print_number
</UL>
<BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[30f]"></a>skip_atoi</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, kservice.o(i.skip_atoi))
<BR><BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[2d2]"></a>plug_holes</STRONG> (Thumb, 108 bytes, Stack size 12 bytes, mem.o(i.plug_holes))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = plug_holes
</UL>
<BR>[Called By]<UL><LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[2ee]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, object.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[2f0]"></a>rt_list_remove</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, object.o(i.rt_list_remove))
<BR><BR>[Called By]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
</UL>

<P><STRONG><a name="[2fb]"></a>rt_list_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, scheduler.o(i.rt_list_init))
<BR><BR>[Called By]<UL><LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_init
</UL>

<P><STRONG><a name="[17b]"></a>_rt_thread_init</STRONG> (Thumb, 124 bytes, Stack size 48 bytes, thread.o(i._rt_thread_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memset
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_stack_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
</UL>

<P><STRONG><a name="[303]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, thread.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[308]"></a>rt_list_remove</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, thread.o(i.rt_list_remove))
<BR><BR>[Called By]<UL><LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
</UL>

<P><STRONG><a name="[17f]"></a>_rt_timer_init</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, timer_1.o(i._rt_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _rt_timer_init
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_init
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
</UL>

<P><STRONG><a name="[2f9]"></a>_rt_timer_remove</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, timer_1.o(i._rt_timer_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
</UL>

<P><STRONG><a name="[180]"></a>rt_list_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer_1.o(i.rt_list_init))
<BR><BR>[Called By]<UL><LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_thread_init
<LI><a href="#[2fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_init
</UL>

<P><STRONG><a name="[30e]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer_1.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
</UL>

<P><STRONG><a name="[30d]"></a>rt_list_isempty</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer_1.o(i.rt_list_isempty))
<BR><BR>[Called By]<UL><LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_list_next_timeout
</UL>

<P><STRONG><a name="[88]"></a>rt_thread_timer_entry</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, timer_1.o(i.rt_thread_timer_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_thread_timer_entry &rArr; rt_thread_delay &rArr; rt_thread_sleep &rArr; rt_timer_start &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; _rt_timer_remove
</UL>
<BR>[Calls]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_soft_timer_check
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_list_next_timeout
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timer_1.o(i.rt_system_timer_thread_init)
</UL>
<P><STRONG><a name="[30a]"></a>rt_timer_list_next_timeout</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, timer_1.o(i.rt_timer_list_next_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_timer_list_next_timeout
</UL>
<BR>[Calls]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timer_entry
</UL>

<P><STRONG><a name="[173]"></a>_idist_for_stream_are_available</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, odsp_api.o(i._idist_for_stream_are_available))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _idist_for_stream_are_available
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_num_slices_per_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_from_mask
</UL>

<P><STRONG><a name="[175]"></a>_num_fec_streams</STRONG> (Thumb, 86 bytes, Stack size 48 bytes, odsp_api.o(i._num_fec_streams))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _num_fec_streams
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_to_num_channel_options
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_num_in_out_fec_streams
</UL>

<P><STRONG><a name="[177]"></a>_num_in_out_fec_streams</STRONG> (Thumb, 180 bytes, Stack size 56 bytes, odsp_api.o(i._num_in_out_fec_streams))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _num_in_out_fec_streams &rArr; _num_fec_streams
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_num_fec_streams
</UL>
<BR>[Called By]<UL><LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_from_mask
</UL>

<P><STRONG><a name="[25b]"></a>_stream_is_available</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, odsp_api.o(i._stream_is_available))
<BR><BR>[Called By]<UL><LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_from_mask
</UL>

<P><STRONG><a name="[181]"></a>api_chn_mask_to_fw_chn_mask_convert</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, odsp_api.o(i.api_chn_mask_to_fw_chn_mask_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = api_chn_mask_to_fw_chn_mask_convert &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_base_die
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[185]"></a>atom_checksum_start</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, odsp_api.o(i.atom_checksum_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = atom_checksum_start &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[28e]"></a>bandgap_rules_to_fw</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.bandgap_rules_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[252]"></a>bdl_lkup_bundle_id_update</STRONG> (Thumb, 98 bytes, Stack size 12 bytes, odsp_api.o(i.bdl_lkup_bundle_id_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bdl_lkup_bundle_id_update
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[2a5]"></a>bdl_lkup_mask_clear</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, odsp_api.o(i.bdl_lkup_mask_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bdl_lkup_mask_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
</UL>

<P><STRONG><a name="[251]"></a>bdl_lkup_mask_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, odsp_api.o(i.bdl_lkup_mask_set))
<BR><BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[188]"></a>bundle_id_is_valid</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, odsp_api.o(i.bundle_id_is_valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bundle_id_is_valid
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_id_is_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
</UL>

<P><STRONG><a name="[18a]"></a>bundle_info_get</STRONG> (Thumb, 212 bytes, Stack size 72 bytes, odsp_api.o(i.bundle_info_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = bundle_info_get &rArr; sram_bundle_lkup_bundle_mask_get &rArr; sram_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fw_chn_mask_to_api_chn_mask_convert
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_rules_addr
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bundle_lkup_bundle_mask_get
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_info_fetch
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operational_mode_from_fw
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
</UL>

<P><STRONG><a name="[190]"></a>bundle_rule_default_chn_mask_set</STRONG> (Thumb, 286 bytes, Stack size 24 bytes, odsp_api.o(i.bundle_rule_default_chn_mask_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bundle_rule_default_chn_mask_set &rArr; ODSP_MEMSET &rArr; memset
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[191]"></a>burst_write_verify</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, odsp_api.o(i.burst_write_verify))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = burst_write_verify &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_fw2pfl_addr
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[189]"></a>channel_id_is_valid</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, odsp_api.o(i.channel_id_is_valid))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_id_is_valid
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_channel_to_bundle
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[23d]"></a>channel_in_half_rate_mode</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, odsp_api.o(i.channel_in_half_rate_mode))
<BR><BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[194]"></a>channel_intf_is_valid</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, odsp_api.o(i.channel_intf_is_valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = channel_intf_is_valid &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_mon_params_check
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
</UL>

<P><STRONG><a name="[196]"></a>channel_intf_to_pmd_intf</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, odsp_api.o(i.channel_intf_to_pmd_intf))
<BR><BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_input_distribution_list
</UL>

<P><STRONG><a name="[195]"></a>channel_to_fec_input_distribution_list</STRONG> (Thumb, 108 bytes, Stack size 48 bytes, odsp_api.o(i.channel_to_fec_input_distribution_list))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = channel_to_fec_input_distribution_list &rArr; channel_to_fec_stream_chans &rArr; odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_fec_xbar_sel_from_bump
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_stream_chans
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_to_pmd_intf
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[197]"></a>channel_to_fec_stream_chans</STRONG> (Thumb, 106 bytes, Stack size 40 bytes, odsp_api.o(i.channel_to_fec_stream_chans))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = channel_to_fec_stream_chans &rArr; odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_to_pmd_intf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_is_valid
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_stream_fec_rules_status_get
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_input_distribution_list
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[19c]"></a>check_msg_size</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, odsp_api.o(i.check_msg_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = check_msg_size &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_generic_msg_send_and_receive
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_fec_stream_msg_send_and_receive
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
</UL>

<P><STRONG><a name="[19d]"></a>chk_pat_mode_is_supported</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, odsp_api.o(i.chk_pat_mode_is_supported))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = chk_pat_mode_is_supported
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_intf_is_rx
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_rules_check
</UL>

<P><STRONG><a name="[19f]"></a>chk_rules_check</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, odsp_api.o(i.chk_rules_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = chk_rules_check &rArr; chk_pat_mode_is_supported
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_pat_mode_is_supported
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_pat_is_supported
</UL>
<BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
</UL>

<P><STRONG><a name="[1a1]"></a>chn_tx_rules_default_set</STRONG> (Thumb, 236 bytes, Stack size 32 bytes, odsp_api.o(i.chn_tx_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = chn_tx_rules_default_set &rArr; odsp_package_get_driver_type &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_driver_type
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[1a3]"></a>chn_tx_rules_to_fw_rules</STRONG> (Thumb, 296 bytes, Stack size 24 bytes, odsp_api.o(i.chn_tx_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = chn_tx_rules_to_fw_rules &rArr; ODSP_MEMCPY &rArr; memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lut_mode_to_fw
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fll_mode_to_fw
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;driver_type_to_fw
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_impedance_to_fw
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMCPY
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1a8]"></a>delegate_coder</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, odsp_api.o(i.delegate_coder))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delegate_coder
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_delegate_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_send
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_load
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_inbox_check
</UL>

<P><STRONG><a name="[14]"></a>deserialize</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, odsp_api.o(i.deserialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = deserialize &rArr; coder_uint8s_decode
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_get
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8s_decode
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8_decode
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint32_decode
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[1a5]"></a>driver_type_to_fw</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, odsp_api.o(i.driver_type_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1dc]"></a>dsp_mode_to_fw</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, odsp_api.o(i.dsp_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_to_fw_rules
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1dd]"></a>dtl_mode_to_fw</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, odsp_api.o(i.dtl_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_to_fw_rules
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1b5]"></a>fec_cfg_api_to_fw</STRONG> (Thumb, 66 bytes, Stack size 40 bytes, odsp_api.o(i.fec_cfg_api_to_fw))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = fec_cfg_api_to_fw &rArr; odsp_fec_api_rules_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_intf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[1b8]"></a>fec_cfg_fw_to_api</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, odsp_api.o(i.fec_cfg_fw_to_api))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = fec_cfg_fw_to_api &rArr; odsp_fec_fw_rules_to_api_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
</UL>

<P><STRONG><a name="[1ba]"></a>fec_cfg_query_req_api_to_fw</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.fec_cfg_query_req_api_to_fw))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = fec_cfg_query_req_api_to_fw &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_intf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
</UL>

<P><STRONG><a name="[1bb]"></a>fec_lock_inst_to_fecl</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, odsp_api.o(i.fec_lock_inst_to_fecl))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = fec_lock_inst_to_fecl &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_write
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[257]"></a>fec_mode_api_to_fw</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.fec_mode_api_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[267]"></a>fec_mode_needed_rx_channels</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, odsp_api.o(i.fec_mode_needed_rx_channels))
<BR><BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
</UL>

<P><STRONG><a name="[1bf]"></a>fec_mon_params_check</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, odsp_api.o(i.fec_mon_params_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = fec_mon_params_check &rArr; odsp_bundle_fec_mode_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_intf_is_valid
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_fec_mode_get
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_channel_to_bundle
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[258]"></a>fec_nom_data_rate_api_to_fw</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, odsp_api.o(i.fec_nom_data_rate_api_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1c2]"></a>fec_nom_data_rate_from_stream_get</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, odsp_api.o(i.fec_nom_data_rate_from_stream_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = fec_nom_data_rate_from_stream_get &rArr; odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_stream_fec_rules_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_rsdec_aligned
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[25f]"></a>fec_nom_data_rate_fw_to_api</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, odsp_api.o(i.fec_nom_data_rate_fw_to_api))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>

<P><STRONG><a name="[176]"></a>fec_nom_data_rate_to_num_channel_options</STRONG> (Thumb, 142 bytes, Stack size 0 bytes, odsp_api.o(i.fec_nom_data_rate_to_num_channel_options))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_num_fec_streams
</UL>

<P><STRONG><a name="[264]"></a>fec_norm_data_rate_to_num_fecls</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, odsp_api.o(i.fec_norm_data_rate_to_num_fecls))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[1c3]"></a>fec_protocol_mode_from_hw_get</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, odsp_api.o(i.fec_protocol_mode_from_hw_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = fec_protocol_mode_from_hw_get &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[261]"></a>fec_rules_is_identical</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, odsp_api.o(i.fec_rules_is_identical))
<BR><BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_mon_cfg
</UL>

<P><STRONG><a name="[265]"></a>fec_slice_to_fec_lock_inst</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, odsp_api.o(i.fec_slice_to_fec_lock_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = fec_slice_to_fec_lock_inst
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[1c8]"></a>fec_stream_idist_clear</STRONG> (Thumb, 82 bytes, Stack size 28 bytes, odsp_api.o(i.fec_stream_idist_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = fec_stream_idist_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_num_slices_per_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_clear
</UL>

<P><STRONG><a name="[1c9]"></a>fec_stream_idist_set</STRONG> (Thumb, 80 bytes, Stack size 28 bytes, odsp_api.o(i.fec_stream_idist_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = fec_stream_idist_set
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_num_slices_per_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_set
</UL>

<P><STRONG><a name="[1ca]"></a>fec_stream_rsdec_aligned</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, odsp_api.o(i.fec_stream_rsdec_aligned))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = fec_stream_rsdec_aligned &rArr; fec_nom_data_rate_from_stream_get &rArr; odsp_stream_fec_rules_status_get &rArr; sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_nom_data_rate_from_stream_get
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_to_fec_slices
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[1cc]"></a>fec_tp_stream_broadcast_is_valid</STRONG> (Thumb, 110 bytes, Stack size 40 bytes, odsp_api.o(i.fec_tp_stream_broadcast_is_valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 2552<LI>Call Chain = fec_tp_stream_broadcast_is_valid &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_tp_stream_value
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
</UL>
<BR>[Called By]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
</UL>

<P><STRONG><a name="[259]"></a>fec_type_api_to_fw</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, odsp_api.o(i.fec_type_api_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[260]"></a>fec_type_fw_to_api</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, odsp_api.o(i.fec_type_fw_to_api))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_fw_rules_to_api_rules
</UL>

<P><STRONG><a name="[1a7]"></a>fll_mode_to_fw</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, odsp_api.o(i.fll_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1d1]"></a>force_un_self_init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, odsp_api.o(i.force_un_self_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = force_un_self_init &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>

<P><STRONG><a name="[1d4]"></a>free_bundle_id_get</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, odsp_api.o(i.free_bundle_id_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = free_bundle_id_get &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[1f8]"></a>fw_addr</STRONG> (Thumb, 40 bytes, Stack size 20 bytes, odsp_api.o(i.fw_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fw_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_direct_download_image
</UL>

<P><STRONG><a name="[18f]"></a>fw_chn_mask_to_api_chn_mask_convert</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, odsp_api.o(i.fw_chn_mask_to_api_chn_mask_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = fw_chn_mask_to_api_chn_mask_convert &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_chn_to_api_chn_table
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_get_type
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
</UL>

<P><STRONG><a name="[1d6]"></a>gen_rules_check</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, odsp_api.o(i.gen_rules_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = gen_rules_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_pat_is_supported
</UL>
<BR>[Called By]<UL><LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
</UL>

<P><STRONG><a name="[1d7]"></a>host_chn_rules_to_fw_rules</STRONG> (Thumb, 210 bytes, Stack size 56 bytes, odsp_api.o(i.host_chn_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = host_chn_rules_to_fw_rules &rArr; odsp_htx_xbar_api_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_to_fw_rules
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[1da]"></a>hrx_check_rules</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, odsp_api.o(i.hrx_check_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = hrx_check_rules &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[269]"></a>hrx_chn_mask_from_rules_get</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, odsp_api.o(i.hrx_chn_mask_from_rules_get))
<BR><BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_chn_mask_from_rules
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
</UL>

<P><STRONG><a name="[1db]"></a>hrx_rules_default_set</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, odsp_api.o(i.hrx_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = hrx_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[1d9]"></a>hrx_rules_to_fw_rules</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, odsp_api.o(i.hrx_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = hrx_rules_to_fw_rules
</UL>
<BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dtl_mode_to_fw
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dsp_mode_to_fw
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[27e]"></a>hw_package_type_to_api_convert</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, odsp_api.o(i.hw_package_type_to_api_convert))
<BR><BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_query_efuse
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_enabled
</UL>

<P><STRONG><a name="[1e0]"></a>is_channel_valid</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, odsp_api.o(i.is_channel_valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = is_channel_valid
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channels
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
</UL>

<P><STRONG><a name="[1e2]"></a>line_chn_rules_to_fw_rules</STRONG> (Thumb, 312 bytes, Stack size 56 bytes, odsp_api.o(i.line_chn_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = line_chn_rules_to_fw_rules &rArr; odsp_ltx_xbar_api_to_fw_rules &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_to_fw_rules
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_api_rules_to_fw_rules
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[1e6]"></a>lrx_check_rules</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, odsp_api.o(i.lrx_check_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = lrx_check_rules &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[268]"></a>lrx_chn_mask_from_rules_get</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, odsp_api.o(i.lrx_chn_mask_from_rules_get))
<BR><BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_chn_mask_from_rules
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
</UL>

<P><STRONG><a name="[1e7]"></a>lrx_rules_default_set</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, odsp_api.o(i.lrx_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = lrx_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[1e4]"></a>lrx_rules_to_fw_rules</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, odsp_api.o(i.lrx_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lrx_rules_to_fw_rules
</UL>
<BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dtl_mode_to_fw
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dsp_mode_to_fw
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1a4]"></a>lut_mode_to_fw</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, odsp_api.o(i.lut_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[1f2]"></a>mcu_direct_download_image</STRONG> (Thumb, 360 bytes, Stack size 80 bytes, odsp_api.o(i.mcu_direct_download_image))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = mcu_direct_download_image &rArr; odsp_mcu_download_start &rArr; mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fw_addr
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;burst_write_verify
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atom_checksum_start
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_download_start
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_write
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_lock
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_wait_done
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_stop
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_mcu_dma0_ch0_start
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_runstall
</UL>
<BR>[Called By]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_download_firmware
</UL>

<P><STRONG><a name="[1f9]"></a>mcu_download_start</STRONG> (Thumb, 304 bytes, Stack size 40 bytes, odsp_api.o(i.mcu_download_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = mcu_download_start &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_un_self_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;self_init_forcing_state_restore
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;self_init_forcing_state_get
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_rmw
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_force_lock
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_rmw
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_download_start
</UL>

<P><STRONG><a name="[1ff]"></a>mcu_reset_check</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, odsp_api.o(i.mcu_reset_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = mcu_reset_check &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_UDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
</UL>

<P><STRONG><a name="[200]"></a>mcu_start_from_iram_into_application_mode</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, odsp_api.o(i.mcu_start_from_iram_into_application_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = mcu_start_from_iram_into_application_mode &rArr; odsp_mcu_vector_set &rArr; odsp_ireg_rmw &rArr; odsp_ireg_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_block_application_mode
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_reset
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_vector_set
</UL>
<BR>[Called By]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_per_mcu_download_firmware
</UL>

<P><STRONG><a name="[245]"></a>monclk_rate_to_fw</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, odsp_api.o(i.monclk_rate_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>

<P><STRONG><a name="[246]"></a>monclk_source_to_fw</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, odsp_api.o(i.monclk_source_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_monclk_set
</UL>

<P><STRONG><a name="[209]"></a>msg_lite_lock</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, odsp_api.o(i.msg_lite_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = msg_lite_lock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[20a]"></a>msg_lite_unlock</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.msg_lite_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = msg_lite_unlock &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[16]"></a>msg_text_deserialize</STRONG> (Thumb, 110 bytes, Stack size 40 bytes, odsp_api.o(i.msg_text_deserialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = msg_text_deserialize &rArr; coder_uint8_decode
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8_decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>msg_text_serialize</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, odsp_api.o(i.msg_text_serialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = msg_text_serialize &rArr; msg_hdr_encode &rArr; coder_uint16_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_encode
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8_encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[18]"></a>msg_uint16_deserialize</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, odsp_api.o(i.msg_uint16_deserialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = msg_uint16_deserialize &rArr; coder_uint16_decode
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[17]"></a>msg_uint16_serialize</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, odsp_api.o(i.msg_uint16_serialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = msg_uint16_serialize &rArr; msg_hdr_encode &rArr; coder_uint16_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_encode
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[20e]"></a>msgr__decoding_reset</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, odsp_api.o(i.msgr__decoding_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = msgr__decoding_reset &rArr; coder_decoding_reset
</UL>
<BR>[Calls]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_decoding_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_load
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr_default_inbox_check
</UL>

<P><STRONG><a name="[11]"></a>msgr_default_inbox_check</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, odsp_api.o(i.msgr_default_inbox_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = msgr_default_inbox_check &rArr; msgr__decoding_reset &rArr; coder_decoding_reset
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr__decoding_reset
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delegate_coder
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_decode
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_rx_avail
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>msgr_default_load</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, odsp_api.o(i.msgr_default_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = msgr_default_load &rArr; msg_padding_strip &rArr; coder_uint8_decode
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msgr__decoding_reset
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delegate_coder
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_padding_strip
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_set
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_deserialize
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>msgr_default_send</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, odsp_api.o(i.msgr_default_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = msgr_default_send &rArr; delegate_coder
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delegate_coder
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_serialize
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_flush
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_encoding_reset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[216]"></a>nom_data_rate_to_num_fec_slices</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, odsp_api.o(i.nom_data_rate_to_num_fec_slices))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = nom_data_rate_to_num_fec_slices
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_num_slices_per_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;num_rx_channels_per_100g_slice
</UL>

<P><STRONG><a name="[217]"></a>num_rx_channels_per_100g_slice</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, odsp_api.o(i.num_rx_channels_per_100g_slice))
<BR><BR>[Stack]<UL><LI>Max Depth = 2552<LI>Call Chain = num_rx_channels_per_100g_slice &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nom_data_rate_to_num_fec_slices
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_stream_rules_query
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_stats_query
</UL>

<P><STRONG><a name="[218]"></a>odsp_ahb__block_read</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb__block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = odsp_ahb__block_read &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
</UL>

<P><STRONG><a name="[219]"></a>odsp_ahb__block_write</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb__block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = odsp_ahb__block_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_write
</UL>

<P><STRONG><a name="[21a]"></a>odsp_ahb__list_read</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_ahb__list_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = odsp_ahb__list_read &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
</UL>

<P><STRONG><a name="[21b]"></a>odsp_ahb__list_write</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_ahb__list_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__addr_write
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write_unsafe
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
</UL>

<P><STRONG><a name="[21c]"></a>odsp_ahb_brdg__addr_write</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_ahb_brdg__addr_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb__list_write
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_write
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg__burst_read
</UL>

<P><STRONG><a name="[21f]"></a>odsp_ahb_brdg__ready_wait</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ahb_brdg__ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = odsp_ahb_brdg__ready_wait &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write_unsafe
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read_unsafe
</UL>

<P><STRONG><a name="[220]"></a>odsp_ahb_brdg__sem</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_ahb_brdg__sem))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = odsp_ahb_brdg__sem &rArr; odsp_hw_sem &rArr; ODSP_MEMSET &rArr; memset
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_hw_sem
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_unlock
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_lock
</UL>

<P><STRONG><a name="[242]"></a>odsp_chn_sfec_rules_default_set</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_chn_sfec_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = odsp_chn_sfec_rules_default_set
</UL>
<BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_protocol_mode_to_sfec_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[238]"></a>odsp_chn_tx_xbar_rules_default_set</STRONG> (Thumb, 938 bytes, Stack size 104 bytes, odsp_api.o(i.odsp_chn_tx_xbar_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = odsp_chn_tx_xbar_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules__xbar_is_used
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_xbar_default_set
</UL>

<P><STRONG><a name="[248]"></a>odsp_efuse_fetch</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_efuse_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + In Cycle
<LI>Call Chain = odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_UDELAY
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_rmw
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_query_efuse
</UL>

<P><STRONG><a name="[24a]"></a>odsp_efuse_is_programmed</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, odsp_api.o(i.odsp_efuse_is_programmed))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = odsp_efuse_is_programmed &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_query_efuse
</UL>

<P><STRONG><a name="[24b]"></a>odsp_eg_fec_rules_default_set</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_eg_fec_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = odsp_eg_fec_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_translate_fec_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
</UL>

<P><STRONG><a name="[263]"></a>odsp_fec_params_check</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_fec_params_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_fec_params_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_check
</UL>

<P><STRONG><a name="[240]"></a>odsp_fec_rules_check</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_fec_rules_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = odsp_fec_rules_check &rArr; odsp_fec_params_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_params_check
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[199]"></a>odsp_get_fec_xbar_sel_from_bump</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_get_fec_xbar_sel_from_bump))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_get_fec_xbar_sel_from_bump &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_channel_intf_to_fec_reg_index
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_to_fec_input_distribution_list
</UL>

<P><STRONG><a name="[266]"></a>odsp_ig_fec_rules_default_set</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, odsp_api.o(i.odsp_ig_fec_rules_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = odsp_ig_fec_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_translate_fec_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_rules_default_set
</UL>

<P><STRONG><a name="[a0]"></a>odsp_mcu__log_text_print</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_mcu__log_text_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_mcu__log_text_print &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_STRNLEN
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[279]"></a>odsp_op_mode_to_intf</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_op_mode_to_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_op_mode_to_intf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_chn_default_set
</UL>

<P><STRONG><a name="[243]"></a>odsp_protocol_mode_to_sfec_cfg</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_protocol_mode_to_sfec_cfg))
<BR><BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_sfec_rules_default_set
</UL>

<P><STRONG><a name="[244]"></a>odsp_rules__xbar_is_used</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_rules__xbar_is_used))
<BR><BR>[Called By]<UL><LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_tx_xbar_rules_default_set
</UL>

<P><STRONG><a name="[235]"></a>odsp_rules_chn_default_set</STRONG> (Thumb, 366 bytes, Stack size 80 bytes, odsp_api.o(i.odsp_rules_chn_default_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = odsp_rules_chn_default_set &rArr; odsp_bundle_rules_xbar_default_set &rArr; odsp_chn_tx_xbar_rules_default_set &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_op_mode_to_intf
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_sfec_rules_default_set
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_rules_default_set
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_rules_default_set
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_default_set
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_xbar_default_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_rules_default_set
</UL>

<P><STRONG><a name="[24e]"></a>odsp_rules_to_fw</STRONG> (Thumb, 376 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_rules_to_fw))
<BR><BR>[Stack]<UL><LI>Max Depth = 2752<LI>Call Chain = odsp_rules_to_fw &rArr; odsp_fec_free_stream_id_get &rArr; fec_tp_stream_broadcast_is_valid &rArr; odsp_fec_stream_rules_query &rArr; odsp_fw_fec_stream_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_chn_rules_to_fw_rules
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;host_chn_rules_to_fw_rules
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bandgap_rules_to_fw
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;api_chn_mask_to_fw_chn_mask_convert
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_chn_mask_from_rules
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operational_mode_to_fw
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_get_die_inst_from_pkg_ch
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_api_rules_to_fw_rules
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[291]"></a>odsp_rx_pam_read_mode</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, odsp_api.o(i.odsp_rx_pam_read_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = odsp_rx_pam_read_mode &rArr; odsp_reg_channel_read &rArr;  odsp_rebase_by_addr (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rx_dsp_pkr_snr_read_db_fixp
</UL>

<P><STRONG><a name="[296]"></a>odsp_sfec_api_rules_cfg_to_fw</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_sfec_api_rules_cfg_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[295]"></a>odsp_sfec_api_rules_mode_to_fw</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_sfec_api_rules_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_api_rules_to_fw_rules
</UL>

<P><STRONG><a name="[23f]"></a>odsp_sfec_params_check</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_sfec_params_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = odsp_sfec_params_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_plus_is_supported
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[297]"></a>odsp_sfec_plus_is_supported</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, odsp_api.o(i.odsp_sfec_plus_is_supported))
<BR><BR>[Called By]<UL><LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sfec_params_check
</UL>

<P><STRONG><a name="[298]"></a>odsp_smem__single_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_smem__single_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = odsp_smem__single_read &rArr; odsp_ahb_brdg_single_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_read
</UL>
<BR>[Called By]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_reg_read
</UL>

<P><STRONG><a name="[299]"></a>odsp_smem__single_write</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.odsp_smem__single_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = odsp_smem__single_write &rArr; odsp_ahb_brdg_single_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_single_write
</UL>
<BR>[Called By]<UL><LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_reg_write
</UL>

<P><STRONG><a name="[254]"></a>odsp_sram_fec_tp_gen_stream_update</STRONG> (Thumb, 128 bytes, Stack size 168 bytes, odsp_api.o(i.odsp_sram_fec_tp_gen_stream_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = odsp_sram_fec_tp_gen_stream_update &rArr; bundle_info_get &rArr; sram_bundle_lkup_bundle_mask_get &rArr; sram_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
<LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_stream_id_update
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_set
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_clear
<LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_fec_rules_update
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[24d]"></a>odsp_teardown</STRONG> (Thumb, 556 bytes, Stack size 512 bytes, odsp_api.o(i.odsp_teardown))
<BR><BR>[Stack]<UL><LI>Max Depth = 2976<LI>Call Chain = odsp_teardown &rArr; per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_id_is_valid
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bdl_lkup_mask_clear
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_lkup_mask_clear
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stream_id_is_valid
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_rules_fetch
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_write
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_bundle_init
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[23e]"></a>odsp_tx_check_rules</STRONG> (Thumb, 320 bytes, Stack size 40 bytes, odsp_api.o(i.odsp_tx_check_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = odsp_tx_check_rules &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_dbg_translate_intf
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_ABS
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_check_rules
</UL>

<P><STRONG><a name="[18e]"></a>operational_mode_from_fw</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, odsp_api.o(i.operational_mode_from_fw))
<BR><BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
</UL>

<P><STRONG><a name="[28d]"></a>operational_mode_to_fw</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, odsp_api.o(i.operational_mode_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[27f]"></a>package_override_cache</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, odsp_api.o(i.package_override_cache))
<BR><BR>[Called By]<UL><LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_override_enabled
</UL>

<P><STRONG><a name="[2a4]"></a>per_bundle_init</STRONG> (Thumb, 88 bytes, Stack size 40 bytes, odsp_api.o(i.per_bundle_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 2464<LI>Call Chain = per_bundle_init &rArr; odsp_fw_bundle_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_bundle_msg_send_and_receive
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
</UL>

<P><STRONG><a name="[202]"></a>per_mcu_block_application_mode</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, odsp_api.o(i.per_mcu_block_application_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = per_mcu_block_application_mode &rArr; per_mcu_fw_mode_query &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_fw_mode_query
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_start_from_iram_into_application_mode
</UL>

<P><STRONG><a name="[275]"></a>per_mcu_fw_mode_query</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, odsp_api.o(i.per_mcu_fw_mode_query))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = per_mcu_fw_mode_query &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;per_mcu_block_application_mode
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
</UL>

<P><STRONG><a name="[276]"></a>per_mcu_loop_count_is_updated</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, odsp_api.o(i.per_mcu_loop_count_is_updated))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = per_mcu_loop_count_is_updated &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
</UL>

<P><STRONG><a name="[230]"></a>pfl_addr</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, odsp_api.o(i.pfl_addr))
<BR><BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_send
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_receive
</UL>

<P><STRONG><a name="[28c]"></a>pkg_chn_mask_from_rules</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, odsp_api.o(i.pkg_chn_mask_from_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pkg_chn_mask_from_rules &rArr; ODSP_MEMSET &rArr; memset
</UL>
<BR>[Calls]<UL><LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lrx_chn_mask_from_rules_get
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hrx_chn_mask_from_rules_get
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_chn_mask_from_rules_get
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_rules_to_fw
</UL>

<P><STRONG><a name="[22e]"></a>pkg_intf_to_hw_value</STRONG> (Thumb, 84 bytes, Stack size 20 bytes, odsp_api.o(i.pkg_intf_to_hw_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = pkg_intf_to_hw_value
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_mapping_lookup
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_chn_to_api_chn_table
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_chn_to_fw_chn_table
</UL>

<P><STRONG><a name="[282]"></a>prbs_chk_rules_apply</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, odsp_api.o(i.prbs_chk_rules_apply))
<BR><BR>[Stack]<UL><LI>Max Depth = 2552<LI>Call Chain = prbs_chk_rules_apply &rArr; rx_prbs_chk_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_prbs_chk_rules_req
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_intf_is_rx
</UL>
<BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_config
</UL>

<P><STRONG><a name="[2a9]"></a>prbs_chk_rules_to_fw_rules</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, odsp_api.o(i.prbs_chk_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prbs_chk_rules_to_fw_rules
</UL>
<BR>[Calls]<UL><LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_pattern_to_fw
</UL>
<BR>[Called By]<UL><LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_prbs_chk_rules_req
</UL>

<P><STRONG><a name="[286]"></a>prbs_gen_rules_apply</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, odsp_api.o(i.prbs_gen_rules_apply))
<BR><BR>[Stack]<UL><LI>Max Depth = 2560<LI>Call Chain = prbs_gen_rules_apply &rArr; tx_prbs_gen_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_prbs_gen_rules_req
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_is_fw_running_ok
</UL>
<BR>[Called By]<UL><LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_gen_config
</UL>

<P><STRONG><a name="[2ac]"></a>prbs_gen_rules_to_fw_rules</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, odsp_api.o(i.prbs_gen_rules_to_fw_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prbs_gen_rules_to_fw_rules
</UL>
<BR>[Calls]<UL><LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_pattern_to_fw
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_prbs_gen_rules_req
</UL>

<P><STRONG><a name="[313]"></a>prbs_pat_hw2sw</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, odsp_api.o(i.prbs_pat_hw2sw))
<BR><BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
</UL>

<P><STRONG><a name="[1a0]"></a>prbs_pat_is_supported</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, odsp_api.o(i.prbs_pat_is_supported))
<BR><BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_rules_check
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_rules_check
</UL>

<P><STRONG><a name="[2aa]"></a>prbs_pattern_to_fw</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, odsp_api.o(i.prbs_pattern_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_to_fw_rules
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_to_fw_rules
</UL>

<P><STRONG><a name="[278]"></a>ram_erase_status_done</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, odsp_api.o(i.ram_erase_status_done))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ram_erase_status_done &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
</UL>
<BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_init_idram
</UL>

<P><STRONG><a name="[270]"></a>receive_res</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, odsp_api.o(i.receive_res))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_receive
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[271]"></a>reset_api_control_bits</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, odsp_api.o(i.reset_api_control_bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = reset_api_control_bits &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[2ad]"></a>ring__avail_count</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, odsp_api.o(i.ring__avail_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_free_count
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_avail_count
</UL>

<P><STRONG><a name="[2af]"></a>ring__cidx_update</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring__cidx_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring__cidx_update &rArr; ring_hal_write
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_write
</UL>
<BR>[Called By]<UL><LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__dequeue
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_flush
</UL>

<P><STRONG><a name="[2b1]"></a>ring__dequeue</STRONG> (Thumb, 218 bytes, Stack size 64 bytes, odsp_api.o(i.ring__dequeue))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__cidx_update
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_block_read
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_avail_count
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>
<BR>[Called By]<UL><LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_dequeue
</UL>

<P><STRONG><a name="[2b5]"></a>ring__enqueue</STRONG> (Thumb, 218 bytes, Stack size 64 bytes, odsp_api.o(i.ring__enqueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__pidx_update
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_block_write
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_free_count
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>
<BR>[Called By]<UL><LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_enqueue
</UL>

<P><STRONG><a name="[2b8]"></a>ring__pidx_update</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring__pidx_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring__pidx_update &rArr; ring_hal_write
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_hal_write
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring__enqueue
</UL>

<P><STRONG><a name="[9b]"></a>ring_coder__alignment_get</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__alignment_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring_coder__alignment_get &rArr; ring_coder__will_access_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[9c]"></a>ring_coder__alignment_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.ring_coder__alignment_set))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[2c1]"></a>ring_coder__cur_us</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, odsp_api.o(i.ring_coder__cur_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ring_coder__cur_us
</UL>
<BR>[Called By]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[2bc]"></a>ring_coder__decode</STRONG> (Thumb, 246 bytes, Stack size 72 bytes, odsp_api.o(i.ring_coder__decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__us_expired
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__has_ts
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__dequeue
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_from_cache
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_cache
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__cur_us
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8s_decode
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8_decode
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint64_decode
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint32_decode
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint16_decode
</UL>

<P><STRONG><a name="[2bd]"></a>ring_coder__decode_cache</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, odsp_api.o(i.ring_coder__decode_cache))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8s_decode
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decoding_reset
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_from_cache
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[2bf]"></a>ring_coder__decode_from_cache</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, odsp_api.o(i.ring_coder__decode_from_cache))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ring_coder__decode_from_cache &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_cache
</UL>
<BR>[Called By]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[9f]"></a>ring_coder__decoding_reset</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, odsp_api.o(i.ring_coder__decoding_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ring_coder__decoding_reset &rArr; ring_flush &rArr; ring__cidx_update &rArr; ring_hal_write
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_cache
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_flush
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[2c0]"></a>ring_coder__dequeue</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, odsp_api.o(i.ring_coder__dequeue))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_dequeue
</UL>
<BR>[Called By]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[2bb]"></a>ring_coder__did_access_notify</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__did_access_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ring_coder__did_access_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__tx_avail
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__rx_avail
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__flush
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__dequeue
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__alignment_get
</UL>

<P><STRONG><a name="[2c5]"></a>ring_coder__encode</STRONG> (Thumb, 240 bytes, Stack size 64 bytes, odsp_api.o(i.ring_coder__encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memmove
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode_cache
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8s_encode
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8_encode
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint64_encode
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint32_encode
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint16_encode
</UL>

<P><STRONG><a name="[2c6]"></a>ring_coder__encode_cache</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, odsp_api.o(i.ring_coder__encode_cache))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__uint8s_encode
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encoding_reset
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>

<P><STRONG><a name="[9e]"></a>ring_coder__encoding_reset</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, odsp_api.o(i.ring_coder__encoding_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ring_coder__encoding_reset &rArr; ring_flush &rArr; ring__cidx_update &rArr; ring_hal_write
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode_cache
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_flush
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[2c7]"></a>ring_coder__enqueue</STRONG> (Thumb, 138 bytes, Stack size 48 bytes, odsp_api.o(i.ring_coder__enqueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__us_expired
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__has_ts
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode_cache
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__cur_us
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_enqueue
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__flush
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>

<P><STRONG><a name="[9d]"></a>ring_coder__flush</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, odsp_api.o(i.ring_coder__flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = ring_coder__flush &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_alignment_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[2be]"></a>ring_coder__has_ts</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, odsp_api.o(i.ring_coder__has_ts))
<BR><BR>[Called By]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[99]"></a>ring_coder__rx_avail</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__rx_avail))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ring_coder__rx_avail &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_avail_count
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[9a]"></a>ring_coder__tx_avail</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__tx_avail))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ring_coder__tx_avail &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__will_access_notify
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__did_access_notify
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_avail_count
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[95]"></a>ring_coder__uint16_decode</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint16_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ring_coder__uint16_decode &rArr; ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[90]"></a>ring_coder__uint16_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint16_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ring_coder__uint16_encode &rArr; ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[96]"></a>ring_coder__uint32_decode</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint32_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ring_coder__uint32_decode &rArr; ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[91]"></a>ring_coder__uint32_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint32_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ring_coder__uint32_encode &rArr; ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[97]"></a>ring_coder__uint64_decode</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint64_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ring_coder__uint64_decode &rArr; ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[92]"></a>ring_coder__uint64_encode</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, odsp_api.o(i.ring_coder__uint64_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = ring_coder__uint64_encode &rArr; ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[94]"></a>ring_coder__uint8_decode</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint8_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ring_coder__uint8_decode &rArr; ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[8f]"></a>ring_coder__uint8_encode</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__uint8_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ring_coder__uint8_encode &rArr; ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[98]"></a>ring_coder__uint8s_decode</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, odsp_api.o(i.ring_coder__uint8s_decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ring_coder__uint8s_decode &rArr; ring_coder__decode &rArr; ring_coder__dequeue &rArr; ring_dequeue &rArr; ring__dequeue &rArr; ring_avail_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode_cache
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[93]"></a>ring_coder__uint8s_encode</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, odsp_api.o(i.ring_coder__uint8s_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = ring_coder__uint8s_encode &rArr; ring_coder__encode &rArr; ring_coder__enqueue &rArr; ring_enqueue &rArr; ring__enqueue &rArr; ring_free_count &rArr; ring__avail_count &rArr; ring_hal_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode_cache
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[2c2]"></a>ring_coder__us_expired</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, odsp_api.o(i.ring_coder__us_expired))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ring_coder__us_expired
</UL>
<BR>[Called By]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__enqueue
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__decode
</UL>

<P><STRONG><a name="[2ba]"></a>ring_coder__will_access_notify</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.ring_coder__will_access_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ring_coder__will_access_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__tx_avail
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__rx_avail
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__flush
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__encode
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__dequeue
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_coder__alignment_get
</UL>

<P><STRONG><a name="[8a]"></a>ring_hal_direct_alignment</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.ring_hal_direct_alignment))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[8d]"></a>ring_hal_direct_block_read</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, odsp_api.o(i.ring_hal_direct_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring_hal_direct_block_read &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[8e]"></a>ring_hal_direct_block_write</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, odsp_api.o(i.ring_hal_direct_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring_hal_direct_block_write &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[8b]"></a>ring_hal_direct_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.ring_hal_direct_read))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[8c]"></a>ring_hal_direct_write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, odsp_api.o(i.ring_hal_direct_write))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.constdata)
</UL>
<P><STRONG><a name="[b]"></a>ring_hal_pif_alignment</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.ring_hal_pif_alignment))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>ring_hal_pif_block_read</STRONG> (Thumb, 60 bytes, Stack size 40 bytes, odsp_api.o(i.ring_hal_pif_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ring_hal_pif_block_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>ring_hal_pif_block_write</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, odsp_api.o(i.ring_hal_pif_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ring_hal_pif_block_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>ring_hal_pif_read</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, odsp_api.o(i.ring_hal_pif_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ring_hal_pif_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>ring_hal_pif_write</STRONG> (Thumb, 22 bytes, Stack size 32 bytes, odsp_api.o(i.ring_hal_pif_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring_hal_pif_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[284]"></a>rx_core_gc_prbs_chk_status_query</STRONG> (Thumb, 414 bytes, Stack size 40 bytes, odsp_api.o(i.rx_core_gc_prbs_chk_status_query))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = rx_core_gc_prbs_chk_status_query &rArr; odsp_reg_channel_write &rArr; odsp_rebase_by_addr &rArr; odsp_channel_adjust &rArr; odsp_package_get_type &rArr; odsp_package_query_efuse &rArr; odsp_efuse_fetch &rArr;  odsp_reg_channel_rmw (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[312]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_gc_chk_reg_offset
<LI><a href="#[313]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_pat_hw2sw
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_unlock
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_write
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_channel_read
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_prbs_chk_status
</UL>

<P><STRONG><a name="[312]"></a>rx_gc_chk_reg_offset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, odsp_api.o(i.rx_gc_chk_reg_offset))
<BR><BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_core_gc_prbs_chk_status_query
</UL>

<P><STRONG><a name="[2a8]"></a>rx_prbs_chk_rules_req</STRONG> (Thumb, 112 bytes, Stack size 96 bytes, odsp_api.o(i.rx_prbs_chk_rules_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 2528<LI>Call Chain = rx_prbs_chk_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_to_fw_rules
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_api2fw_convert
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_chk_rules_apply
</UL>

<P><STRONG><a name="[1fb]"></a>self_init_forcing_state_get</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, odsp_api.o(i.self_init_forcing_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = self_init_forcing_state_get &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>

<P><STRONG><a name="[1fe]"></a>self_init_forcing_state_restore</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, odsp_api.o(i.self_init_forcing_state_restore))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = self_init_forcing_state_restore &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_download_start
</UL>

<P><STRONG><a name="[26f]"></a>send_req</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, odsp_api.o(i.send_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = send_req &rArr; odsp_api_fw_send &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_fw_send
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_write
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[13]"></a>serialize</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, odsp_api.o(i.serialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = serialize &rArr; msg_hdr_encode &rArr; coder_uint16_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_get
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;msg_hdr_encode
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint8s_encode
<LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint32_encode
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;coder_uint16_encode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>smem_ring_hal_alignment</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, odsp_api.o(i.smem_ring_hal_alignment))
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>smem_ring_hal_block_read</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.smem_ring_hal_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = smem_ring_hal_block_read &rArr; odsp_smem_burst_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_burst_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>smem_ring_hal_block_write</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, odsp_api.o(i.smem_ring_hal_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = smem_ring_hal_block_write &rArr; odsp_smem_burst_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_burst_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>smem_ring_hal_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, odsp_api.o(i.smem_ring_hal_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = smem_ring_hal_read &rArr; odsp_smem_reg_read &rArr; odsp_smem__single_read &rArr; odsp_ahb_brdg_single_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_reg_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>smem_ring_hal_write</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, odsp_api.o(i.smem_ring_hal_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = smem_ring_hal_write &rArr; odsp_smem_reg_write &rArr; odsp_smem__single_write &rArr; odsp_ahb_brdg_single_write &rArr; odsp_ahb_brdg_single_write_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_smem_reg_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> odsp_api.o(.data)
</UL>
<P><STRONG><a name="[18d]"></a>sram_bdl_info_fetch</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, odsp_api.o(i.sram_bdl_info_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = sram_bdl_info_fetch &rArr; sram_bdl_status_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_status_fetch
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_fw2pfl_addr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
</UL>

<P><STRONG><a name="[317]"></a>sram_bdl_lkup_addr</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.sram_bdl_lkup_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sram_bdl_lkup_addr &rArr; sram_dev_info_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_write
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_fetch
</UL>

<P><STRONG><a name="[22c]"></a>sram_bdl_lkup_chn_index</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, odsp_api.o(i.sram_bdl_lkup_chn_index))
<BR><BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_channel_to_bundle
</UL>

<P><STRONG><a name="[22b]"></a>sram_bdl_lkup_fetch</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, odsp_api.o(i.sram_bdl_lkup_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sram_bdl_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_addr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_api_channel_to_bundle
</UL>

<P><STRONG><a name="[253]"></a>sram_bdl_lkup_write</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, odsp_api.o(i.sram_bdl_lkup_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sram_bdl_lkup_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_addr
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[319]"></a>sram_bdl_rules_host_addr</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, odsp_api.o(i.sram_bdl_rules_host_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = sram_bdl_rules_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_fw2pfl_addr
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_fw_bdl_rules_fill
</UL>

<P><STRONG><a name="[31a]"></a>sram_bdl_status_addr</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, odsp_api.o(i.sram_bdl_status_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = sram_bdl_status_addr &rArr; sram_dev_info_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_status_fetch
</UL>

<P><STRONG><a name="[316]"></a>sram_bdl_status_fetch</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, odsp_api.o(i.sram_bdl_status_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = sram_bdl_status_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[31a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_status_addr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_info_fetch
</UL>

<P><STRONG><a name="[256]"></a>sram_bundle_fec_mode_update</STRONG> (Thumb, 40 bytes, Stack size 144 bytes, odsp_api.o(i.sram_bundle_fec_mode_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = sram_bundle_fec_mode_update &rArr; sram_stream_lkup_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[18c]"></a>sram_bundle_lkup_bundle_mask_get</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, odsp_api.o(i.sram_bundle_lkup_bundle_mask_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = sram_bundle_lkup_bundle_mask_get &rArr; sram_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[31b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_read
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
</UL>

<P><STRONG><a name="[318]"></a>sram_dev_info_host_addr</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, odsp_api.o(i.sram_dev_info_host_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = sram_dev_info_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_mcu_fw2pfl_addr
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_unlock
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_read
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ireg_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[31c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_addr
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_rules_fetch
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_rules_addr
<LI><a href="#[31a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_status_addr
<LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_lkup_addr
</UL>

<P><STRONG><a name="[18b]"></a>sram_dev_rules_addr</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, odsp_api.o(i.sram_dev_rules_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sram_dev_rules_addr &rArr; sram_dev_info_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bundle_info_get
</UL>

<P><STRONG><a name="[2a3]"></a>sram_dev_rules_fetch</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, odsp_api.o(i.sram_dev_rules_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sram_dev_rules_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
</UL>

<P><STRONG><a name="[24f]"></a>sram_fw_bdl_rules_fill</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, odsp_api.o(i.sram_fw_bdl_rules_fill))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = sram_fw_bdl_rules_fill &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bdl_rules_host_addr
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_enter_operational_state
</UL>

<P><STRONG><a name="[31b]"></a>sram_read</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, odsp_api.o(i.sram_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = sram_read &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bundle_lkup_bundle_mask_get
</UL>

<P><STRONG><a name="[31c]"></a>sram_stream_lkup_addr</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, odsp_api.o(i.sram_stream_lkup_addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sram_stream_lkup_addr &rArr; sram_dev_info_host_addr &rArr; odsp_ireg_read &rArr; odsp_ahb_brdg_single_read_unsafe &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_dev_info_host_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_write
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_fetch
</UL>

<P><STRONG><a name="[233]"></a>sram_stream_lkup_fetch</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, odsp_api.o(i.sram_stream_lkup_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sram_stream_lkup_fetch &rArr; odsp_ahb_brdg_burst_read &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[31c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_addr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_read
</UL>
<BR>[Called By]<UL><LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bundle_fec_mode_update
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_bundle_fec_mode_get
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_stream_fec_rules_status_get
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_package_channel_to_fec_stream
</UL>

<P><STRONG><a name="[2a2]"></a>sram_stream_lkup_write</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, odsp_api.o(i.sram_stream_lkup_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sram_stream_lkup_write &rArr; odsp_ahb_brdg_burst_write &rArr; odsp_ahb_brdg__burst_write &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[31c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_stream_lkup_addr
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ahb_brdg_burst_write
</UL>
<BR>[Called By]<UL><LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sram_bundle_fec_mode_update
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>

<P><STRONG><a name="[25d]"></a>stream_id_is_valid</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, odsp_api.o(i.stream_id_is_valid))
<BR><BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_free_stream_id_get
</UL>

<P><STRONG><a name="[2a0]"></a>stream_lkup_fec_rules_update</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, odsp_api.o(i.stream_lkup_fec_rules_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = stream_lkup_fec_rules_update
</UL>
<BR>[Called By]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>

<P><STRONG><a name="[2a1]"></a>stream_lkup_mask_clear</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, odsp_api.o(i.stream_lkup_mask_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = stream_lkup_mask_clear &rArr; fec_stream_idist_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_idist_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_teardown
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>

<P><STRONG><a name="[29e]"></a>stream_lkup_mask_set</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, odsp_api.o(i.stream_lkup_mask_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = stream_lkup_mask_set &rArr; fec_stream_idist_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_idist_set
</UL>
<BR>[Called By]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>

<P><STRONG><a name="[29f]"></a>stream_lkup_stream_id_update</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, odsp_api.o(i.stream_lkup_stream_id_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = stream_lkup_stream_id_update
</UL>
<BR>[Called By]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_tp_gen_stream_update
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_sram_fec_stream_update
</UL>

<P><STRONG><a name="[1cb]"></a>stream_to_fec_slices</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, odsp_api.o(i.stream_to_fec_slices))
<BR><BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fec_stream_rsdec_aligned
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fec_pcs_bundle_status_query
</UL>

<P><STRONG><a name="[2a7]"></a>tx_chn_mask_from_rules_get</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, odsp_api.o(i.tx_chn_mask_from_rules_get))
<BR><BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pkg_chn_mask_from_rules
</UL>

<P><STRONG><a name="[1a6]"></a>tx_impedance_to_fw</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, odsp_api.o(i.tx_impedance_to_fw))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chn_tx_rules_to_fw_rules
</UL>

<P><STRONG><a name="[2ab]"></a>tx_prbs_gen_rules_req</STRONG> (Thumb, 112 bytes, Stack size 104 bytes, odsp_api.o(i.tx_prbs_gen_rules_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 2536<LI>Call Chain = tx_prbs_gen_rules_req &rArr; odsp_fw_channel_msg_send_and_receive &rArr; odsp_fw_msg_send_and_receive &rArr; receive_res &rArr; odsp_api_fw_receive &rArr; odsp_ahb_brdg__burst_read &rArr; odsp_ahb_brdg__addr_write &rArr; odsp_ahb__list_write &rArr; odsp_reg_write &rArr; odsp_reg_set &rArr; mdio_write &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_to_fw_rules
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_channel_msg_send_and_receive
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_chn_api2fw_convert
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MEMSET
</UL>
<BR>[Called By]<UL><LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_gen_rules_apply
</UL>

<P><STRONG><a name="[272]"></a>tx_xbar_api_input_intf_convert</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, odsp_api.o(i.tx_xbar_api_input_intf_convert))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_ltx_xbar_api_to_fw_rules
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_htx_xbar_api_to_fw_rules
</UL>

<P><STRONG><a name="[26e]"></a>wait_fw_reset_control_bits</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, odsp_api.o(i.wait_fw_reset_control_bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = wait_fw_reset_control_bits &rArr; odsp_reg_read &rArr; odsp_reg_get &rArr; mdio_read &rArr; MDIO_Reg_Addr_Data_Write &rArr; MDC_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODSP_MDELAY
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_reg_read
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;odsp_fw_msg_send_and_receive
</UL>

<P><STRONG><a name="[172]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[170]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[179]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[178]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
