#ifndef __ODSP_API_H__
#define __ODSP_API_H__

#ifdef __cplusplus
extern "C" {
#endif

/** @file odsp.h
 ****************************************************************************
 *
 * @brief
 *     This module describes the high level API methods provided by the API.
 *
 ****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ***************************************************************************/
#ifndef __ODSP_H__
#define __ODSP_H__

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

#include "odsp_rtos.h"
#include "odsp_types.h"
#include "odsp_rules.h"
#include "odsp_registers.h"

// MACROS - START
#define ODSP_MASK(NAME)            (NAME##__MASK << NAME##__OFFSET)
#define ODSP_OFFSET(NAME)          (NAME##__OFFSET)
#define ODSP_VAL(NAME, val)        (((val) & NAME##__MASK) << NAME##__OFFSET)
#define ODSP_ADDRESS(NAME)         (NAME##__ADDRESS)
#define ODSP_MASK_UNALIGNED(NAME)  (NAME##__MASK)
#define ODSP_ADDR_DIFF(A0, A1)     (A1##__ADDRESS - A0##__ADDRESS)
//MACROS - END

/// To broadcast a register write to all channels, do not use in API methods, just in direct register accesses
#define ODSP_BROADCAST_CHANNEL 0xff

// Maximum time to wait for a ACK response from the FW, units are seconds
#define ODSP_ACK_WAIT_MAX      2 // seconds

// Timeout to get histogram data
#define ODSP_HIST_REQ_TIMEOUT_MS 2000

#define ODSP_ESTIMATOR_PRE_CURSOR_NUM    5
#define ODSP_ESTIMATOR_POST_CURSOR_NUM   24
#define ODSP_PULSE_RESP_SAMPLE_NUM (ODSP_ESTIMATOR_PRE_CURSOR_NUM + ODSP_ESTIMATOR_POST_CURSOR_NUM + 1)

/**
 * The maximum number of devices that the API can support in the same
 * system. This is used to manage caching the package type associated
 * with a particular die to handle mapping of API channel numbers to the external
 * pins of the ASIC.
 *
 * I2C frame details for PHY/DEV address:
 *
 * First of all a note: DEV address and MMD are synonimes, so they will be called indifferently in the two ways.
 * The frame composition depend on how the block is parameterized. We will see that there are parameters to choise the number of MMD to be used and aldo which MMD has to be used.
 *
 * Here the implementation for spica5n where there are 5 different MMD:
 * - MMD0 = 5'd08
 * - MMD1 = 5'd30
 * - MMD2 = 5'd20
 * - MMD3 = 5'd21
 *
 * In this case in the frame there will be 2 address bits to decode the 4 MMD:
 * - MMD0 dev address = 2'b00
 * - MMD1 dev address = 2'b01
 * - MMD2 dev address = 2'b10
 * - MMD3 dev address = 2'b11
 *
 * As consequence there will be just 4 bits for PHY address, so just 16 devices can be used with different PHY address.
 */
#ifndef ODSP_MAX_NUM_DEVICES
#define ODSP_MAX_NUM_DEVICES 16
#endif // ODSP_MAX_NUM_DEVICES
#define ODSP_MAX_FEC_DECODERS 16
#define ODSP_FEC_STATS_MAX_TIME 14000

#define ODSP_LOCK(die) {if(odsp_lock(die) != ODSP_OK) return ODSP_ERROR;}
#define ODSP_UNLOCK(die) {if(odsp_unlock(die) != ODSP_OK) return ODSP_ERROR;}

/**
 * @h2 Package and Channel Utilities
 * =======================================================
 * 
 * @brief
 * Convenience macro for iterating through all channels on an interface.
 * Iterator will be called "channel"
 *
 * @example
 * ODSP_FOR_CHANNEL_IN_CHANNELS(die, ODSP_INTF_LRX)
 * {
 *      printf("ch %d\n", channel);
 * }
 */
#define ODSP_FOR_CHANNEL_IN_CHANNELS(die, intf) \
    for(uint32_t channel = 1; channel <= ODSP_MAX_CHANNELS; channel ++)

/**
 * This method is used to query the package type from the efuse
 *
 * @since *******
 *
 * @private
 */
e_odsp_package_type odsp_package_query_efuse(
    uint32_t device);

/**
 * This method is used to determine the number of dies
 * inside the package.
 *
 * @param device [I] - The device being accessed.
 *
 * @return The number of dies in the package.
 *
 * @since *******
 */
uint32_t odsp_package_get_num_dies(
    uint32_t device);

/**
 * This method is called to get the base die inside
 * the package. This is used for iterating through multiple dies
 * in an ASIC package that has multiple dies.
 *
 * @param device [I] - The device being accessed.
 *
 * @return The base die number in the package.
 *
 * @since *******
 */
uint32_t odsp_package_get_base_die(
    uint32_t device);

/**
 * This method is used to query the ASIC package type
 * from a given die.
 *
 * @param device [I] - The device being accessed.
 *
 * @return
 * The ASIC package type defined in the MMD30_CHIP_ID
 * register.
 *
 * @since *******
 */
e_odsp_package_type odsp_package_get_type(
    uint32_t device);

/**
 * This is a private method that get the minumum and maximum channel 
 * numbers for the package. It's mostly used for the python wrapper.
 *
 * @param device  [I] - The device being accessed.
 * @param intf    [I] - The interface.
 * @param min     [O] - Pointer to the minumum channel number.
 * @param max     [O] - Pointer to the maximum channel number.
 *
 * @return The number of dies in the package.
 *
 * @since *******
 */
void odsp_package_get_channels(
    uint32_t     device, 
    e_odsp_intf  intf, 
    uint32_t*    min, 
    uint32_t*    max);

/**
 * This method is used to determine the die associated with a particular channel
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel being accessed.
 * @param intf    [I] - The interface.
 *
 * @since *******
 */
uint32_t odsp_reg_channel_to_die(
    uint32_t device, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method is called to dump the cache used to map a particular
 * die parameter to the associated ASIC package type. The package type
 * is important so that the API knows how to map channels to the external
 * pins of the ASIC.
 *
 * @since *******
 */
void odsp_package_cache_dump(void);

/**
 * This method is called to clear the cache used to map a particular
 * die parameter to the associated ASIC package type. The package type
 * is important so that the API knows how to map channels to the external
 * pins of the ASIC.
 *
 * @since *******
 */
void odsp_package_cache_clear(void);

/**
 * Override the package type for debugging purposes. Note
 * this will only override the channel mapping of the package
 * and won't change supported hardware features.
 *
 * @param device  [I] - The device being accessed.
 * @param package [I] - Package type to use for the override.
 */
void odsp_package_override(
    uint32_t device, 
    e_odsp_package_type package);

/**
 * Disable any package override
 *
 * @param device [I] - The device being accessed.
 */
void odsp_package_override_disable(
    uint32_t device);

/**
 * Determine whether a package override has been enabled
 *
 * @param device [I] - The device being accessed.
 *
 * @return true if the package is overridden, false otherwise.
 */
bool odsp_package_override_enabled(
    uint32_t device);

/**
 * If the package has been overriden this method can be called
 * to determine what the package was forced to.
 *
 * @param device [I] - The device being accessed.
 *
 * @return The overridden package type.
 */
e_odsp_package_type odsp_package_override_type(
    uint32_t device);

/**
 * This method may be called to query then print the
 * Efuse, bundle and channel mapping info.
 *
 * @param device [I] - The device being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since *******
 *
 */
void odsp_package_info_dump(
    uint32_t device);

/**
 * This method may be called to get the Driver Type of the package
 *
 * @param device [I] - The device being accessed.
 *
 * @return e_odsp_tx_driver_type
*
 * @since *******
 *
 */
e_odsp_tx_driver_type odsp_package_get_driver_type(
    uint32_t device);

/**
 * @h2 Register Access Methods
 * =======================================================
 * The following methods must be defined by the customer
 * to provide access to the underlying register interface
 * of the ASIC depending on the customer platform.
 *
 * @h3 Low Level Interface Methods
 * ================================
 * The odsp_reg_get/odsp_reg_set methods provide access to
 * the ASIC registers. They must be implemented outside the
 * API in the customers software as the interface may be
 * different for each user. The API provides the following
 * protoypes for these methods.
 *
 * @brief
 * Lowest level register get function, must be implemented
 * by the end user for 16bit-only accesses.
 *
 * NOTE: Do not use odsp_reg_get directly in your code, use
 * odsp_reg_read instead.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being accessed.
 * @param data [O] - The data read from the register.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.0.1 
 *
 */
odsp_status_t odsp_reg_get(
    uint32_t  die,
    uint32_t  addr,
    uint16_t* data);

/**
 * @brief
 * Lowest level register set function, must be implemented
 * by the end user for 16bit-only accesses.
 *
 * NOTE: Do not use odsp_reg_set directly in your code, use
 * odsp_reg_write instead.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being
 *                   accessed.
 * @param data [I] - The data to write to the register.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.0.1 
 *
 */
odsp_status_t odsp_reg_set(
    uint32_t die,
    uint32_t addr,
    uint16_t data);

/**
 * @h3 API Register Access Methods
 * ===============================
 * These are higher layer methods that build upon the
 * odsp_reg_get/odsp_reg_set methods. These should be used
 * when accessing the registers in the event that any special
 * handling needs to be implemented when accessing particular
 * registers.
 *
 * @brief
 * This method is used to write a register on the device. The
 * registers are actually only 16 bits but 32b is used for
 * internal validation purposes. The extra bits about 0xffff
 * are ignored.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being accessed.
 * @param data [I] - The data to write to the register.
 *
 * @since *******
 */
void odsp_reg_write(
    uint32_t die, 
    uint32_t addr, 
    uint32_t data);

/**
 * This method is called to read an ASIC register.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being accessed.
 * 
 * @return The data read back from the register.
 *
 * @since *******
 */
uint32_t odsp_reg_read(
    uint32_t die, 
    uint32_t addr);

/**
 * This method is called to perform a read/modify/write operation
 * on an ASIC register. This is used to modify bitfields within
 * a register.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being accessed.
 * @param data [I] - The data to write to the register.
 * @param mask [I] - A mask to ignore unused bits.
 * 
 * @return The modified register value.
 *
 * @since *******
 */
uint32_t odsp_reg_rmw(
    uint32_t die, 
    uint32_t addr, 
    uint32_t data, 
    uint32_t mask);

/**
  * This method is called to read a register following an indirect sequence.
  *
  * @param die        [I] - The physical ASIC die being accessed.
  * @param addr       [I] - Register address.
  *
  * @return           Register value.
  *
  * @since *******
  */
uint32_t odsp_ireg_read(
    uint32_t die, 
    uint32_t addr);

/**
  * This method is called to write a value to a register following an indirect sequence.
  *
  * @param die        [I] - The physical ASIC die being accessed.
  * @param addr       [I] - Register address.
  * @param data       [I] - Data to write.
  *
  * @since *******
  */
void odsp_ireg_write(
    uint32_t die, 
    uint32_t addr, 
    uint32_t data);

/**
 * This method is called to perform a read/modify/write operation
 * on an ASIC register following an indirect sequence. This is used to modify bitfields within
 * a register.
 *
 * @param die  [I] - The ASIC die being accessed.
 * @param addr [I] - The address of the register being accessed.
 * @param data [I] - The data to write to the register.
 * @param mask [I] - A mask to ignore unused bits.
 *
 * @return The modified register value.
 *
 * @since *******
 */
uint32_t odsp_ireg_rmw(
    uint32_t die, 
    uint32_t addr, 
    uint32_t data, 
    uint32_t mask);

#if defined(ODSP_HAS_MULTI_REGS_ACCESS) && (ODSP_HAS_MULTI_REGS_ACCESS==1)

/**
 * This method is called to read a list of ASIC registers
 *
 * @param die       [I] - The ASIC die being accessed.
 * @param addrs     [I] - List of register addresses
 * @param datas     [O] - Returned data
 * @param num_addrs [I] - Number of registers to read
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_reg_list_read(
    uint32_t die,
    uint32_t *addrs,
    uint16_t *datas,
    uint16_t num_addrs);

/**
 * This method is called to write a list of ASIC registers
 *
 * @param die       [I] - The ASIC die being accessed.
 * @param addrs     [I] - List of register addresses
 * @param datas     [I] - List of register data
 * @param num_addrs [I] - Number of registers to write
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_reg_list_write(
    uint32_t die,
    uint32_t *addrs,
    uint16_t *datas,
    uint16_t num_addrs);

/**
 * @brief Address mode in block access
 */
typedef enum
{
    ODSP_BLOCK_ADDR_MODE_INC,  /**< Auto increase address on every read/write */
    ODSP_BLOCK_ADDR_MODE_SAME, /**< Read/write happen on same address */
}e_odsp_block_addr_mode;

/**
 * This method is called to read a block of contiguous ASIC registers
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param addr_mode  [I] - Address increase mode, increased or not increased.
 *                         + 0 (ODSP_BLOCK_ADDR_MODE_INC): auto increase
 *                         + 1 (ODSP_BLOCK_ADDR_MODE_SAME): same address
 * @param start_addr [I] - Start address
 * @param num_addrs  [I] - Number of address
 * @param datas      [O] - Return data
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_reg_block_read(
    uint32_t die,
    uint8_t  addr_mode,
    uint32_t start_addr,
    uint16_t num_addrs,
    uint16_t *datas);

/**
 * This method is called to write a block of contiguous ASIC registers
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param addr_mode  [I] - Address increase mode, increased or not increased.
 *                         + 0 (ODSP_BLOCK_ADDR_MODE_INC): auto increase
 *                         + 1 (ODSP_BLOCK_ADDR_MODE_SAME): same address
 * @param start_addr [I] - Start address
 * @param num_addrs  [I] - Number of address
 * @param datas      [I] - Data to write
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_reg_block_write(
    uint32_t die,
    uint8_t  addr_mode,
    uint32_t start_addr,
    uint16_t num_addrs,
    uint16_t *datas);

#endif /* ODSP_HAS_MULTI_REGS_ACCESS */

/**
 * @h3 Per-Channel Register Access Methods
 * =======================================
 * These methods are used to access a particular channel through the
 * ASIC. They automatically map the channel to the correct register instance
 * based on the ASIC type.
 *
 * @brief
 * This method is called to read a register for a particular channel through
 * the ASIC.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel being accessed.
 * @param addr    [I] - The address of the register being accessed.
 * 
 * @return The data read back from the register.
 *
 * @since *******
 */
uint32_t odsp_reg_channel_read(
    uint32_t device, 
    uint32_t channel, 
    uint32_t addr);

/**
 * This method is used for writing to a register associated with a particular
 * channel. The registers are actually only 16 bits but 32b is
 * used for internal validation purposes. The extra bits about 0xffff
 * are ignored.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel being accessed.
 * @param addr    [I] - The address of the register being accessed.
 * @param data    [I] - The data to write to the register.
 *
 * @since *******
 */
void odsp_reg_channel_write(
    uint32_t device, 
    uint32_t channel, 
    uint32_t addr, 
    uint32_t data);

/**
 * This method is called to perform a read/modify/write operation
 * on a register for a particular channel through the ASIC. This is used to
 * modify bitfields within a register.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel being accessed.
 * @param addr    [I] - The address of the register being accessed.
 * @param data    [I] - The data to write to the register.
 * @param mask    [I] - A mask to ignore unsed bits.
 * 
 * @return The modified register value.
 *
 * @since *******
 */
uint32_t odsp_reg_channel_rmw(
    uint32_t device,
    uint32_t channel,
    uint32_t addr,
    uint32_t data,
    uint32_t mask);

/**
 * This method is used to re-map the address for particular register based
 * on the ASIC.
 *
 * @param die     [I] - The ASIC die being accessed.
 * @param channel [I] - The channel being accessed.
 * @param addr    [I] - The address of the register being accessed.
 *
 * @since *******
 *
 * @private
 * 
 * @return The corrected register address for the target channel.
 */
uint32_t odsp_reg_channel_addr(
    uint32_t die, 
    uint32_t channel,
    uint32_t addr);

/**
 * This method is called to manage re-mapping the channel based on
 * the register address being accessed.
 *
 * @param die     [I/O] - The die being accessed. This may be modified depending
 *                        on which channel in a multi-die package is being
 *                        accessed.
 * @param channel [I/O] - The channel through the device being accessed. This
 *                        may be re-mapped based on the package type.
 *
 * @param addr    [I/O] - The address of the register being accessed. This
 *                        may be re-mapped based on the package type and channel.
 *
 * @returns ODSP_OK on success, ODSP_ERROR on error
 *
 * @since *******
 */
odsp_status_t odsp_rebase_by_addr(
    uint32_t* die,
    uint32_t* channel,
    uint32_t* addr);

/**
 * This method may be called to get the package channel from the die, interface
 * and register instance of the device.
 *
 * This is an internal method
 * 
 * @param die  [I] - The ASIC die being accessed.
 * @param intf [I] - The interface.
 * @param inst [I] - The register instance.
 *
 * @returns the package channel, 0xff on error
 *
 * @private
 * 
 * @since *******
 *
 */
uint32_t odsp_get_pkg_ch_from_die_inst(
    uint32_t     die, 
    e_odsp_intf  intf,
    uint32_t     inst);

/**
 * This method may be called to return the connected die and instance of
 * the selected package channel. 
 *
 * @param die     [I] - The ASIC die being accessed.
 * @param channel [I] - The channel.
 * @param intf    [I] - The interface.
 *
 * @return IP or Die instance
 *
 * @private
 * 
 * @since *******   
 *
 */
uint32_t odsp_get_die_inst_from_pkg_ch(
    uint32_t     die,
    uint32_t     channel,
    e_odsp_intf intf);

typedef odsp_status_t (*odsp_callback_lock)(uint32_t die);
typedef odsp_status_t (*odsp_callback_unlock)(uint32_t die);

/**
 * @h2 Hardware Locking Methods
 * =======================================================
 * The following methods provide support for multi-threading.
 * Because this is optional they are implemented as callback
 * methods that the user may chose to register.
 *
 * @note
 * The locking methods must be implemented as recursive/counting locks
 * or reentrant mutex as the API will attempt to obtain the
 * same lock multiple times in child function calls:
 *   https://en.wikipedia.org/wiki/Reentrant_mutex
 *
 * @brief
 * Setup a callback method to support h/w locking. Setting up hardware
 * locking/multi-threading is optional. It will be disabled
 * by default.
 *
 * @param callback [I] - Pointer to the callback function to
 *                       call to lock access to the h/w.
 *
 * @return None
 *
 * @since *******
 */
void odsp_set_callback_for_lock(
    odsp_callback_lock callback);

/**
 * Setup a callback method to support h/w unlocking. Setting up
 * hardware locking is optional. It will be disabled by default.
 *
 * @param callback [I] - Pointer to the callback function to
 *                       call to unlock access to the h/w.
 *
 * @return None
 *
 * @since *******
 */
void odsp_set_callback_for_unlock(
    odsp_callback_unlock callback);

/**
 * Lock the hardware for exclusive access. If hardware locking
 * has not been enabled then these methods silently return.
 *
 * @{note, Failure to obtain lock (via timeout or some other method)
 * should return ODSP_ERROR, otherwise the API will proceed without
 * obtaining lock}
 *
 * @{note, The locking feature has not been tested}
 *
 * @param die [I] - The ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @see odsp_set_callback_for_lock, odsp_set_callback_for_unlock
 *
 * @since *******
 */
odsp_status_t odsp_lock(uint32_t die);

/**
 * Unlock the hardware for exclusive access. If hardware locking
 * has not been enabled then these methods silently return.
 *
 * @{note, The locking feature has not been tested}
* 
 * @param die [I] - The ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @see odsp_set_callback_for_lock, odsp_set_callback_for_unlock
 *
 * @since *******
 */
odsp_status_t odsp_unlock(uint32_t die);

/**
 * @h2 Firmware Management
 * =======================================================
 * Methods for interacting with the FW running in the device.
 *
 * @h3 Firmware Mode Enumerations
 * =======================================================
 *
 * @brief
 * The following enumeration defines the modes of
 * operation of the firmware.
 */
typedef enum
{
    /** Unknown bootloader mode */
    ODSP_FW_MODE_UNKNOWN           = 0,

    /** Application mode */
    ODSP_FW_MODE_APPLICATION       = 1,

    /** Boot Upgrade mode */
    ODSP_FW_MODE_BOOT_UPGRADE      = 2,

    /** Bootloader mode */
    ODSP_FW_MODE_BOOT_FROM_EEPROM  = 3,
} e_odsp_fw_mode;

/**
 * @h3 Switching Firmware Modes
 * =======================================================
 * The following methods may be used to switch between different
 * firmware modes.
 *
 * @brief
 * This method is called to reset the firmware into application mode. It
 * resets the MCU and switches to the application bank (assuming it
 * has been previously programmed in the IRAM/DRAM). If the
 * @{b,wait_till_started} flag is set it waits for the MCU_FW_MODE register to report
 * 0xACC0 to indicate that the application image has started up.
 *
 * This method does not load the firmware from EEPROM. It assumes that the
 * firmware has been previously downloaded to the IRAM/DRAM.
 *
 * @{note,
 * To avoid blocking forever in the case of a failure or where the
 * firmware image is not yet programmed this method will timeout
 * after 2s and return ODSP_ERROR.}
 *
 * @param die               [I] - The ASIC die being accessed.
 * @param wait_till_started [I] - Wait until the application firmware is started.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0
 */
odsp_status_t odsp_mcu_reset_into_application(
    uint32_t die,
    bool wait_till_started);

/**
 * Reset the firmware into boot upgrade mode. This mode provides
 * an alternative mechanism for upgrading the firmware either
 * directly to the IRAM/DRAM or to the external EEPROM memory.
 *
 * It resets the MCU and switches into the boot upgrade bank. If the
 * @{b,wait_till_started} flag is set it waits for the MCU_FW_MODE register to report
 * 0xB5DD to indicate the bootloader has started in upgrade mode.
 *
 * @{note,
 * To avoid blocking forever in the case of a failure this method will
 * timeout after 2s and return ODSP_ERROR.}
 *
 * @param die               [I] - The ASIC die being accessed.
 * @param wait_till_started [I] - Wait until the bootloader firmware is started.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0
 */
odsp_status_t odsp_mcu_reset_into_boot_upgrade(
    uint32_t die,
    bool wait_till_started);

/**
 * Reset the firmware into boot from EEPROM mode. This mode may be
 * used to boot the application firmware from an external EEPROM
 * image if present.
 *
 * It resets the MCU into boot from EEPROM mode. If the @{b,wait_till_started} flag
 * is set it waits for the MCU_FW_MODE register to report 0xACC0 to indicate that the application
 * has started up with the image from EEPROM.
 *
 * @{note,
 * To avoid blocking forever in the case of a failure this method will
 * timeout after 2s and return ODSP_ERROR.}
 *
 * @param die               [I] - The ASIC die being accessed.
 * @param wait_till_started [I] - True to wait until the application FW has started before returning
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0
 *
 * @example
 * uint32_t die = 0;
 *
 * // Measure the start time
 * uint64_t start = clock();
 *
 * // Jump to boot from EEPROM
 * odsp_mcu_reset_into_boot_from_eeprom(die, true);
 *
 * // Measure the end time
 * uint64_t end = clock();
 *
 * // Print the amount of time it took to boot from EEPROM
 * ODSP_NOTE("Boot from EEPROM took %lld ms\n", end - start);
 */
odsp_status_t odsp_mcu_reset_into_boot_from_eeprom(
    uint32_t die,
    bool wait_till_started);

/**
 * This method is called to block waiting for the f/w to be
 * running in application mode. This is useful when switching
 * f/w modes or when programming the application firmware image.
 *
 * @param die           [I] - The ASIC die being accessed.
 * @param timeout_in_ms [I] - The maximum timeout in milli-seconds
 *                            to block waiting for the application image to
 *                            startup.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_mcu_block_application_mode(
    uint32_t die,
    uint32_t timeout_in_ms);

#if defined(ODSP_HAS_DIRECT_DOWNLOAD) && (ODSP_HAS_DIRECT_DOWNLOAD==1)
#if defined(ODSP_HAS_INLINE_APP_FW) && (ODSP_HAS_INLINE_APP_FW == 1)
/**
 * @h3 Firmware Programming APIs
 * =======================================================
 * There are two ways of programming the embedded application
 * firmware:
 *
 * @table
 * - Method           | Description | Methods
 * - Direct Download  | The firmware image is programmed directly
 *                      to the on-board IRAM/DRAM and the MCU is
 *                      brought out of reset. This download needs
 *                      to happen on every reset. | odsp_mcu_download_firmware,
 *                      odsp_mcu_download_firmware_from_external_memory,
 *                      odsp_mcu_download_firmware_from_file
 *  
 * @brief
 * This method is called to download the firmware inlined
 * with the API directly to the MCUs RAM memory.
 *
 * It will program the microcode on all dies ,
 * jump to the new application image and verify it is
 * running properly.
 *
 * @param die    [I] - The ASIC die being accessed.
 * @param verify [I] - Optionally read back the programmed values
 *                     to verify the results. This is typically
 *                     not required and will slow down the programming
 *                     but is provided for users that want an
 *                     extra integrity check.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must have direct download support. It must be
 * compiled with the following flags set to 1:
 * - ODSP_HAS_DIRECT_DOWNLOAD
 * - ODSP_HAS_INLINE_APP_FW
 *
 * @since *******
 */
odsp_status_t odsp_mcu_download_firmware(
    uint32_t die, 
    bool verify);

/**
 * This method is called to fetch a pointer to the inlined f/w image.
 *
 * @param ptr    [O] - The pointer to the inlined firmware.
 * @param length [O] - The length of the firmware image in 32b words.
 *
 * @requires
 * The API must have direct download support. It must be
 * compiled with the following flags set to 1:
 * - ODSP_HAS_DIRECT_DOWNLOAD
 * - ODSP_HAS_INLINE_APP_FW
 *
 * @since *******
 */
odsp_status_t odsp_mcu_get_inline_firmware(
    const uint32_t** ptr,
    uint32_t* length);

/**
 * Get the inlined f/w version number.
 *
 * @return The inlined f/w version number (if present)
 *
 * @requires
 * The API must be compiled with
 * - ODSP_HAS_INLINE_APP_FW
 *
 * @since *******
 */
uint32_t odsp_mcu_get_inline_firmware_version(void);

#endif //defined(ODSP_HAS_INLINE_APP_FW) && (ODSP_HAS_INLINE_APP_FW == 1)

/**
 * This method is called to download the firmware directly
 * to the MCUs RAM memory.
 * It will program the microcode on all dies,
 * jump to the new application image and verify it is
 * running properly.
 *
 * @param die    [I] - The ASIC die being accessed.
 * @param path   [I] - The path to the application firmware
 *                     to program.
 * @param verify [I] - Optionally performs a verification by reading the programmed
 *                     firmware from RAM memory and compare with the image embedded in the file.
 *                     This is provided for users that want an extra integrity check.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must have file system support. It must be
 * compiled with the following flags set to 1:
 * - ODSP_HAS_DIRECT_DOWNLOAD
 * - ODSP_HAS_FILESYSTEM
 *
 * @since *******
 */
odsp_status_t odsp_mcu_download_firmware_from_file(
    uint32_t    die, 
    const char* path, 
    bool        verify);

#endif //defined(ODSP_HAS_DIRECT_DOWNLOAD) && (ODSP_HAS_DIRECT_DOWNLOAD==1)

#if defined(ODSP_HAS_EEPROM_ACCESS) && (ODSP_HAS_EEPROM_ACCESS==1)
#if defined(ODSP_HAS_FILESYSTEM) && (ODSP_HAS_FILESYSTEM==1)
/**
 * @h2 Downloading EEPROM from a File
 * This section describes the process of programming the EEPROM
 * from an external file. In order to call this you must
 * define ODSP_HAS_EEPROM_ACCESS and have access
 * to the file system (ODSP_HAS_FILESYSTEM)
 *
 * @c
 * uint32_t die = 0;
 * uint32_t checksum_calculated = 0;
 * uint32_t checksum_expected = 0;
 * odsp_status_t status = ODSP_OK;
 *
 * // Set SELF_INIT pin to 0 to enable bootloader upgrade mode
 * // to allow re-programming the EEPROM.
 * customer_hardware_set_self_init_pin(0);
 *
 * // Reset the MCU into bootloader upgrade mode
 * status |= odsp_mcu_reset_into_boot_upgrade(die, true);
 *
 * // Re-program the EEPROM image
 * status |= odsp_mcu_download_eeprom_from_file(die, "/path/to/eeprom/image.txt");
 *
 * // Verify the EEPROM image
 * status |= odsp_mcu_eeprom_verify_with_checksum(die, 0, checksum_calculated, checksum_expected);
 * printf("Checksum (Calculated): %x\n", checksum_calculated);
 * printf("Checksum (Expected):   %x\n", checksum_expected);
 *
 * if(status != ODSP_OK)
 * {
 *     printf("An error occurred re-programming the EEPROM\n");
 * }
 *
 * @brief
 * This method is called to program the EEPROM from an
 * image file.
 *
 * @param die    [I] - The die used to identify which ASIC is being
 *                     accessed.
 * @param path   [I] - The path to the EEPROM image to program.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must have file system support. It must be compiled
 * with the following flags set to 1:
 * - ODSP_HAS_FILESYSTEM
 * - ODSP_HAS_EEPROM_ACCESS
 *
 * @since 0.1.0.0
 */
odsp_status_t odsp_mcu_download_eeprom_from_file(
    uint32_t    die,
    const char* path);

#endif // defined(ODSP_HAS_FILESYSTEM) && (ODSP_HAS_FILESYSTEM==1)

/**
 * This method is called to verify that the EEPROM contains valid data
 * by calculating the checksum of the EEPROM and comparing it to what
 * it should be. The calculated and stored checksum are returned so
 * that they may be displayed in the end user system.
 *
 * @{note, This method will automatically switch the f/w to boot
 *         upgrade mode to verify the EEPROM contents}.
 *
 * @param die                 [I] - The die of the device being accessed.
 * @param eeprom_offset       [I] - The FW's image offset in Eeprom.
 * @param checksum_calculated [O] - If this pointer is not NULL then
 *                                  the EEPROM checksum calculated by
 *                                  the MCU will be stored in it.
 * @param checksum_expected   [O] - If this pointer is not NULL then
 *                                  the EEPROM checksum expected by the
 *                                  MCU will be stored in it.
 *
 * @example
 *   uint32_t die = 0;
 *   uint32_t calc = 0;
 *   ODSP_status_t status = ODSP_OK;
 *
 *   status |= odsp_mcu_eeprom_verify_with_checksum(die, 0, &calc, &exp);
 *   printf("calculated checksum: %x\n", calc);
 *   printf("expected checksum:   %x\n", exp);
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0.0   
 *
 *
 * @requires
 * The API must have EEPROM access support. It must be compiled
 * with the following flags set to 1:
 * - ODSP_HAS_EEPROM_ACCESS
 */
odsp_status_t odsp_mcu_eeprom_verify_with_checksum(
    uint32_t die, 
    uint32_t eeprom_offset,
    uint32_t* checksum_calculated,
    uint32_t* checksum_expected);

/**
 * This method is called to read the metadata from the EEPROM
 * image. The user should allocate an array of 256 bytes to
 * store the metadata image.
 *
 * @{note, In python this method accepts two arguments only (die, metadata_size) and
 * returns (status, metadata)}
 *
 * @param die             [I]   - The die of the device being accessed.
 * @param metadata        [I/O] - The buffer allocated to store the
 *                                EEPROM metadata.
 * @param metadata_size   [I]   - The number of allocated bytes in
 *                                the metadata buffer.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0.0
 * 
 * @requires
 * The API must have EEPROM access support. It must be compiled
 * with the following flags:
 * - ODSP_HAS_EEPROM_ACCESS=1
 */
odsp_status_t odsp_mcu_eeprom_read_metadata(
    uint32_t die,
    char*    metadata,
    uint32_t metadata_size);

/**
 * This method is called to dump EEPROM image into file
 * The user need to input path file
 *
 * @param die    [I] - The die of the device being accessed.
 * @param path_file   [I] - The path to eeprom dump file
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0.0
 * 
 * @requires
 * The API must have EEPROM access support. It must be compiled
 * with the following flags:
 * - ODSP_HAS_EEPROM_ACCESS=1
 */
odsp_status_t odsp_mcu_eeprom_dump_file(
    uint32_t die,
    char* path_file);

/**
 * @h2 SPI Interface
 * =======================================================
 *
 * @brief
 * This method is used to read an block of data from the
 * external EEPROM (if it is present).
 *
 * @param die         [I] - The physical ASIC die being accessed.
 * @param eeprom_addr [I] - The EEPROM address to access.
 * @param words       [I] - The buffer to read the SPI
 *                          data into.
 * @param num_words   [I] - The number of 32 bit words to read
 *                          from EEPROM.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must have EEPROM access support. It must be compiled
 * with the following flags:
 * - ODSP_HAS_EEPROM_ACCESS=1
 */
odsp_status_t odsp_spi_read_data_block(
    uint32_t            die,
    uint32_t            eeprom_addr,
    uint32_t            *words,
    uint32_t            num_words);

/**
 * This method is used to write a block of data into the
 * external EEPROM (if it is present)
 *
 * @param die         [I] - The physical ASIC die being accessed.
 * @param eeprom_addr [I] - The EEPROM address to access.
 * @param words       [I] - The array of 32 bit words to write
 *                          to the EEPROM.
 * @param num_words   [I] - The number of 32 bit words to write
 *                          to the EEPROM.
 * @param clkdiv      [I] - The SPI clock divide ratio.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have EEPROM access support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_EEPROM_ACCESS=1
 */
odsp_status_t odsp_spi_write_data_block(
    uint32_t            die,
    uint32_t            eeprom_addr,
    uint32_t            *words,
    uint32_t            num_words);

/**
 * This method is called to erase the EEPROM
 *
 * Note: The EEPROM erase operation can take up to 5 seconds to 
 *       complete. EEPROM API read/write methods must not be 
 *       called during this period.
 *
 * @param die         [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have EEPROM access support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_EEPROM_ACCESS=1
 */
odsp_status_t odsp_spi_eeprom_erase(uint32_t die);

#endif // defined(ODSP_HAS_EEPROM_ACCESS) && (ODSP_HAS_EEPROM_ACCESS==1)

/**
 * @h2 Firmware MCU Debug
 * =======================================================
 *
 * @brief
 * This structure is used to gather status information from the MCU
 * for debugging purposes.
 */
typedef struct
{
    /** The firmware mode of operation */
    e_odsp_fw_mode fw_mode;
    /** The firmware mode (human readable string) */
    const char*    fw_mode_str;
    /** Is the firmware stalled? */
    bool           runstall;
    /** An array of program counter values */
    uint32_t       pc_trace[10];
    /** The loop counter value */
    uint32_t       loop_count;
    /** Any MDIO address errors that may have been flagged */
    uint32_t       mdio_addr_err;
    
    /** The application firmware version code */
    uint32_t       app_version;
    /** The application firmware major version */
    uint8_t        app_version_major;
    /** The application firmware minor version */
    uint8_t        app_version_minor;
    /** The application firmware patch revision number */
    uint8_t        app_version_revision;
    /** The application firmware build id */
    uint16_t       app_version_build;
    
    /** The API version code (if programmed) */
    uint32_t       api_version;
    /** The API major version (if programmed) */
    uint8_t        api_version_major;
    /** The API minor version (if programmed) */
    uint8_t        api_version_minor;
    /** The API patch revision number (if programmed) */
    uint8_t        api_version_revision;
    /** The API build ID (if programmed) */
    uint16_t       api_version_build;
}odsp_mcu_status_t;

/**
 * Query the current firmware mode
 *
 * @param die     [I]   - The physical ASIC die being accessed.
 * @param fw_mode [I/O] - The firmware mode.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since *******
 */
odsp_status_t odsp_mcu_fw_mode_query(
    uint32_t         die,
    e_odsp_fw_mode* fw_mode);

/**
 * This method may be called to verify that the FW status
 * is ok. If not, a print of the FW status is performed.
 *
 * To determine if the FW is ok, the following is checked:
 *
 * - Check that it's in application mode (ACC0)
 * - Check that there is no exception
 * - Check that the HL_STATE (FW high-level state) reg is not 0xffff (stuck in startup)
 * - Check that the loop counter is incrementing
 * - Read, wait 10ms, read loop counter again
 *
 * @param die         [I] - The ASIC die being accessed.
 *
 * @return true when FW ok, false on FW not ok.
 *
 * @since *******
 */
bool odsp_is_fw_running_ok(uint32_t die);

/**
 * This method is used to query the loop counter multi-times
 *
 * @param die                       [I] - The ASIC die being accessed.
 * @param entries                   [O] - The loop counter values buffer.
 * @param num_entries               [I] - The number of entries to capture.
 * @param us_delay_between_captures [I] - The amount of time to wait between captures.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 */
odsp_status_t odsp_mcu_loop_count_query(
    uint32_t  die,
    uint16_t* entries,
    uint32_t  num_entries,
    uint32_t  us_delay_between_captures);

/**
 * This method is used to query status information from the on-board
 * MCU for debug purposes
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param mcu_status [I] - The MCU status queried from the hardware.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_mcu_status_query(
    uint32_t           die,
    odsp_mcu_status_t* mcu_status);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method is used to query and display status information from the on-board
 * MCU for debug purposes
 *
 * @param mcu        [I] - The ASIC die being accessed, this method is per-die.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 * @requires
 * The API must be compiled with ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_mcu_status_query_print(
    uint32_t mcu);

/**
 * This method is called to fetch the current setting of the f/w debug log
 * filter. The filter is used to restrict which log messages get displayed
 * in the firmware log
 *
 * @param die [I] - The physical ASIC die being accessed.
 *
 * @return The filter log message or 0xffffffff if it couldn't be read.
 *
 * @since 0.7
 */
uint32_t odsp_mcu_debug_log_filter_get(
    uint32_t die);

/**
 * This method is called to update the current setting of the f/w debug log
 * filter. The filter is used to restrict which log messages get displayed
 * in the firmware log.
 *
 * - bit20: LOG_API_FW
 * - bit17: LOG_PMD_QC_DEBUG
 * - bit16: LOG_FEC
 * - bit15: LOG_XBAR
 * - bit14: LOG_CLK
 * - bit13: LOG_SDT
 * - bit12: LOG_LTX_DEBUG
 * - bit11: LOG_PLL
 * - bit10: LOG_HTX_DEBUG
 * - bit9:  LOG_VREG
 * - bit8:  LOG_RX_HIST
 * - bit7:  LOG_TIMESTAMP
 * - bit6:  LOG_PMD_QC
 * - bit5:  LOG_LRX_DEBUG
 * - bit4:  LOG_HRX_DEBUG
 * - bit3:  LOG_INT
 * - bit2:  LOG_WARN
 * - bit1:  LOG_ERROR
 * - bit0:  LOG_DEBUG
 *
 * @param die    [I] - The physical ASIC die being accessed.
 * @param filter [I] - The value used to filter log messages. Each bit represents
 * a specific log type, and when it is set, the corresponding log messages
 * will be recorded in the f/w log
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.7
 */

odsp_status_t odsp_mcu_debug_log_filter_set(
    uint32_t die,
    uint32_t filter);

/**
 * This method is used to dump the firmware trace log into a buffer,
 * for debug purposes.
 *
 * @param die         [I] - The physical ASIC being accessed.
 * @param buff        [I] - Buffer to store logs.
 * @param buff_size   [I] - Buffer size.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with ODSP_HAS_DIAGNOSTIC_DUMPS flag
 *
 * @since 0.7
 *
 */
odsp_status_t odsp_mcu_debug_log_query(
    uint32_t die,
    char*    buff,
    uint32_t buff_size);

/**
 * This method is used to dump the firmware trace log
 * for debug purposes.
 *
 * @param die         [I] - The physical ASIC being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with ODSP_HAS_DIAGNOSTIC_DUMPS flag
 *
 * @since 0.7
 *
 */
odsp_status_t odsp_mcu_debug_log_query_dump(uint32_t die, const char *ld_file);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h2 Version Information
 * =======================================================
 *
 * @brief
 * This method is used to retreive the API version string describing
 * the version of the API in use. The user must allocate
 * a buffer of at least 256 bytes to retrieve the version information.
 * 
 * @param buffer     [O] - The output buffer where the version string will
 *                         be stored.
 * @param buffer_len [I] - The length of the allocated buffer. Units are bytes.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure 
 *
 * @since *******
 */
odsp_status_t odsp_version(
    char*    buffer, 
    uint32_t buffer_len);

/**
 * @brief
 * This method is used to retreive the version string describing
 * the version of the firmware in use. The user must allocate
 * a buffer of at least 256 bytes to retrieve the version information.
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param buffer     [O] - The output buffer where the version string will
 *                         be stored.
 * @param buffer_len [I] - The length of the allocated buffer. Units are bytes.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure 
 *
 * @since *******
 */
odsp_status_t odsp_version_firmware(
    uint32_t die,
    char*    buffer, 
    uint32_t buffer_len);

/**
 * @h2 Device Configuration
 * =======================================================
 *
 * @brief
 * This method sets up the default rules for all the enabled bundles
 * to the desired operating mode to simplify the implementation for the user.
 *
 * @{note,This method initializes the rules data-structure, it does
 * not write to any registers.
 *
 * After running this helper method, channel_enable field of all
 * channel rules are disabled.
 * }
 *
 * @{note,
 * This method has been superseded by the bundle methods, although it will
 * still behave as usual.
 * }
 *
 * @param device        [I] - The device being accessed.
 * @param op_mode       [I] - The main operational mode of the ASIC.
 * @param protocol_mode [I] - The desired protocol modes which will describe the rate,
 *                              number of channels on each interface, and indirectly the signalling.
 * @param fec_mode      [I] - The FEC mode of the ASIC.
 * @param rules         [O] - The default rules for the application.
 *
 * @return ODSP_OK on success, ODSP_ERROR otherwise
 *
 * @since *******
 *
 * @deprecated see odsp_bundle_rules_default_set
 */
odsp_status_t odsp_rules_default_set(
    uint32_t                device,
    e_odsp_operational_mode op_mode,
    e_odsp_protocol_mode    protocol_mode,
    e_odsp_fec_mode         fec_mode,
    odsp_rules_t*           rules);

/**
 * This method is used to check the rules for possible errors in configuration.
 * This gets called automatically by odsp_enter_operational_state.
 *
 * @param device  [I] - The device being accessed.
 * @param rules   [I] - The default rules for the application.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @private
 *
 * @since 0.3.0.0
 */
odsp_status_t odsp_check_rules(uint32_t device, odsp_rules_t* rules);

/**
 * Reset the device to have it/them ready for any user config,
 * and then call odsp_enter_operational_state.
 *
 * This method can be called after de-asserting the reset pin;
 * it will wait for the bootloader to load an EEPROM and the
 * application firmware to be loaded. You can configure the
 * maximum time to wait in rules.fw_dwld_timeout (default 500ms).
 *
 * If all channels are disabled in rules, all existing bundles
 * will be torn down.
 *
 * @{note,
 * This method is multi-die aware on devices that have
 * multiple dies}
 *
 * @{note,
 * This method has been superseded by the bundle methods, although it will
 * still behave as usual.
 * }
 *
 * @param device     [I] - The device being accessed.
 * @param rules      [I] - The configuration rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since *******
 *
 * @deprecated see odsp_bundle_init
 */
odsp_status_t odsp_init(uint32_t device, odsp_rules_t* rules);

/**
 * This method is used to put the device into operational
 * state. This method MUST be preceded by a call to odsp_init.
 *
 * @{note,
 * This method is multi-die aware on packages that have
 * multiple dies}
 *
 * @{note,
 * This method has been superseded by the bundle methods, although it will
 * still behave as usual.
 * }
 *
 * @param device  [I] - The device being accessed.
 * @param rules   [I] - The device initialization rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @deprecated see odsp_bundle_enter_operational_state
 */
odsp_status_t odsp_enter_operational_state(
    uint32_t      device,
    odsp_rules_t* rules);

/**
 * This method is called to retrieve the first bundle configuration.
 * Application should use odsp_bundle_rules_query to query
 * all bundles in device instead
 *
 *
 * @param device  [I]   - The device being accessed.
 * @param p_rules [I/O] - The pointer to bundle rules structure
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @deprecated see odsp_bundle_rules_query and odsp_bundles_info_query
 */
odsp_status_t odsp_rules_query(
    uint32_t     device,
    odsp_rules_t *p_rules);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

/**
 * This is a debug method used to print the configuration rules for a bundle.
 *
 * @param device  [I] - The device being accessed.
 * @param rules   [I] - The rules structure to print.
 *
 * @since *******
 * @private
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
void odsp_rules_print(uint32_t device, odsp_rules_t *rules);

/**
 * This is a debug method used to query then print the configuration rules.
 *
 * @param device  [I] - The device being accessed.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
void odsp_bundles_rules_query_print(uint32_t device);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

/**
 * @h2 Bundle Configuration
 * =======================================================
 *
 * @brief
 * This method sets up the default rules for new bundle to the desired operating
 * mode to simplify the implementation for the user.
 *
 * See the BundleManagement section of the user guide for detailed information.
 *
 * @{note,This method initializes the rules data-structure, it does
 * not write to any registers.}
 *
 * @{note,The Operational Mode of the bundle must be the same as all the other bundles
 * used on this device.}
 *
 * @param device        [I] - The device being accessed.
 * @param first_channel [I] - This is the first channel of the line receive in the bundle when interface
 *                            is ony Ingress, otherwise, this is the first host receive channel in the bundle.
 * @param intfs         [I] - Ingress/Egress or both.
 * @param op_mode       [I] - The main operational mode of the bundle.
 * @param protocol_mode [I] - The desired protocol modes which will describe the rate,
 *                             number of channels on each interface, and indirectly the signaling.
 * @param fec_mode      [I] - The FEC mode to decide how data path routed through the chip
 * @param p_rules         [O] - Pointer to a rules structure, will be populated with default rules for the bundle.
 *
 * @return ODSP_OK on success, ODSP_ERROR otherwise
 */
odsp_status_t odsp_bundle_rules_default_set(
    uint32_t                device,
    uint32_t                first_channel,
    e_odsp_intf             intfs,
    e_odsp_operational_mode op_mode,
    e_odsp_protocol_mode    protocol_mode,
    e_odsp_fec_mode         fec_mode,
    odsp_rules_t            *p_rules);

/**
 * This method sets up the default rules for Data and Clock XBARs.
 *
 * @{note,This method update the rules data-structure , it does
 * not write to any registers.}
 *
 * @param device        [I] - The ASIC die being accessed.
 * @param rules         [O] - The rules to update XBAR.
 *
 * @return ODSP_OK on success, ODSP_ERROR otherwise
 */
odsp_status_t odsp_bundle_rules_xbar_default_set(uint32_t device, odsp_rules_t *rules);

/**
 * Tear-down existing bundle by putting it into initialized state.
 *
 * This methods will tear-down all existing bundles if input channel is 
 * ODSP_BROADCAST_CHANNEL and intf is ODSP_INTF_ALL.
 *
 * See the BundleManagement section of the user guide for detailed information.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - Any channel belongs to bundle
 * @param intf        [I] - Bundle interface.
 *
 * @return ODSP_OK if the bundle is ready, ODSP_ERROR if the bundle is not ready.
 */
odsp_status_t odsp_bundle_init(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

/**
 * This method is used to put the bundle into operational state.
 *
 * See the BundleManagement section of the user guide for detailed information.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - Any channel belongs to bundle.
 * @param intf    [I] - Ingress/Egress or both
 * @param p_rules   [I] - Pointer to the bundle rules structure.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_bundle_enter_operational_state(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    odsp_rules_t *p_rules);

/**
 * This method should be used to enable/disable a bundle of traffic on the chip independently of other bundles (if any exist).
 * This puts the channels/intfs in the bundle in low power and does NOT clear the rules or re-init the bundle.
 * It operates on the whole bundle in both ingress and egress directions.
 *
 * For example, if the part is configured for: ODSP_MODE_MISSION_MODE, 4 bundles with protocol mode ODSP_PROT_MODE_100G_2Px26p6_TO_1Px53p1
 * and the clk_xbar is set as below table:
 *
 * @{pre,
 * +---------+-------------------------------+
 * |         |     Bundles                   |
 * | INTF    |   1   |   2   |   3   |   4   |
 * +---------+-------+-------+-------+-------+
 * |HRX      |  1,2  |  3,4  |  5,6  |  7,8  |
 * |LTX      |   1   |   2   |   3   |   4   |
 * +---------+-------+-------+-------+-------+
 * |LRX      |   1   |   2   |   3   |   4   |
 * |HTX      |  1,2  |  3,4  |  5,6  |  7,8  |
 * +---------+-------+-------+-------+-------+
 * }
 *
 * Setting enable/disable for any intf/channel pair above will disable/enable ALL other intf/channel pairs
 * in the same bundle in both directions. ie. Calling odsp_bundle_enable(die, 1, ODSP_INTF_LTX, false) will take down
 * LRX 1, HTX 1&2, HRX 1&2, LTX 1.
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - Channel.
 * @param intf      [I] - Interface.
 * @param enable    [I] - True to enable the bundle, false otherwise.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1.0.0
 */
odsp_status_t odsp_bundle_enable(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    bool        enable);

/**
 * Check if bundle is enabled
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - Any channel belongs to bundle
 * @param intf        [I] - Bundle interface(ODSP_INTF_LRX or ODSP_INTF_HRX or boths)
 * @param enable      [O] - Bundle enabling
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5
 */
odsp_status_t odsp_bundle_is_enabled(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    bool        *enable);

/**
 * This method should be used to enable/disable specific ingress or egress path of a bundle.
 * For example, if the part is configured for: ODSP_MODE_MISSION_MODE, 4 bundles with protocol mode ODSP_PROT_MODE_100G_2Px26p6_TO_1Px53p1
 * and the clk_xbar is set as below table:
 *
 * @{pre,
 * +---------+-------------------------------+
 * |         |     Bundles                   |
 * | INTF    |   1   |   2   |   3   |   4   |
 * +---------+-------+-------+-------+-------+
 * |HRX      |  1,2  |  3,4  |  5,6  |  7,8  |
 * |LTX      |   1   |   2   |   3   |   4   |
 * +---------+-------+-------+-------+-------+
 * |LRX      |   1   |   2   |   3   |   4   |
 * |HTX      |  1,2  |  3,4  |  5,6  |  7,8  |
 * +---------+-------+-------+-------+-------+
 * }
 *
 * Setting enable/disable for any intf/channel pair above will disable/enable ALL other intf/channel pairs
 * in the same bundle. ie. Calling odsp_clock_group_enable(die, 2, ODSP_INTF_HTX, false) will take down LRX 1, HTX 1&2.
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - Channel.
 * @param intf      [I] - Interface.
 * @param enable    [I] - True to enable the clock group, false otherwise.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.1
 */
odsp_status_t odsp_clock_group_enable(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    bool        enable);

/**
 * Check if Ingress/Egress direction of a bundle is enabled or not
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - Any channel belongs to bundle
 * @param intf        [I] - Bundle interface(ODSP_INTF_LRX or ODSP_INTF_HRX)
 * @param enable      [O] - Bundle enabling
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5
 */
odsp_status_t odsp_clock_group_is_enabled(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    bool        *enable);

/**
 * Query bundle configurations
 *
 * See the BundleManagement section of the user guide for detailed information.
 *
 * @param device       [I] - The device being accessed.
 * @param channel      [I] - Any channel belongs to bundle
 * @param intf         [I] - Interface
 * @param p_rules      [O] - The pointer to bundle rules structure
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5
 */
odsp_status_t odsp_bundle_rules_query(
    uint32_t       device,
    uint32_t       channel,
    e_odsp_intf    intf,
    odsp_rules_t   *p_rules);

/**
 * @brief Bundle link status
 */
typedef struct {
    /** HRX lock, with one bit per package channel */
    uint16_t hrx_lock;

    /** LTX lock, with one bit per package channel */
    uint16_t ltx_lock;

    /** LRX lock, with one bit per package channel */
    uint16_t lrx_lock;

    /** HTX lock, with one bit per package channel */
    uint16_t htx_lock;

    /**
     * Bundle enable state. Note this is not whether the bundle has
     * been configured or torn down; a torn down bundle ceases to exist.
     */
    bool enable;

    /** Overall bundle lock status */
    bool lock;
} odsp_bundle_link_status_t;

/**
 * Query bundle link status
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - Any channel belongs to bundle.
 * @param intf        [I] - Bundle interface.
 * @param link_status [O] - Bundle link status.
 *
 * @return ODSP_OK if the bundle is ready, ODSP_ERROR if the bundle is not ready.
 */
odsp_status_t odsp_bundle_link_status_query(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_bundle_link_status_t *link_status);

/**
 * This method is called to wait for bundle ready.
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - Any channel belongs to bundle.
 * @param intf       [I] - Bundle interface.
 * @param timeout_us [I] - The timeout in microseconds to wait for the bundle ready.
 *
 * @return ODSP_OK if the bundle is ready, ODSP_ERROR if the bundle is not ready.
 */
odsp_status_t odsp_bundle_wait_for_link_ready(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t    timeout_us);

/**
 * @brief Bundle information
 */
typedef struct odsp_bundle_info_s
{
    /**
     * State (active/inactive) of bundle
     */
    uint8_t  active;

    /**
     * Bundle operational mode
     */
    uint8_t  op_mode;

    /**
     * Bundle protocol mode
     */
    uint8_t  pro_mode;

    /** Bit mask of which channels are included in the bundle */
    uint16_t hrx_mask;

    /** Bit mask of which channels are included in the bundle */
    uint16_t ltx_mask;

    /** Bit mask of which channels are included in the bundle */
    uint16_t lrx_mask;

    /** Bit mask of which channels are included in the bundle */
    uint16_t htx_mask;
} odsp_bundle_info_t;

/**
 * Query bundle basic information like its interfaces and channels belongs to it
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - Any channel belongs to bundle
 * @param intf        [I] - Bundle interface.
 * @param bundle_info [O] - Bundle information
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_bundle_info_query(
    uint32_t           device,
    uint32_t           channel,
    e_odsp_intf        intf,
    odsp_bundle_info_t *bundle_info);

/**
 * Query information of all provisioned bundles.
 *
 * @param device       [I] - The device being accessed.
 * @param bundles_info [O] - Information of all provisioned bundles.
 * @param num_bundles  [O] - Number of provisioned bundles.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_bundles_info_query(
    uint32_t           device,
    odsp_bundle_info_t bundles_info[ODSP_MAX_BUNDLES],
    uint8_t            *num_bundles);

/**
 * This function provides the list of configurations and states that GUI
 * requires to display. The full configuration of channel is provided in
 * API odsp_bundle_rules_query
 *
 * @param device          [I] - The device being accessed.
 * @param interfaces_info [O] - Configuration and status of all channels
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_interfaces_info_query(
    uint32_t device,
    odsp_interfaces_info_t *interfaces_info);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

/**
 * This is a debug method used to query then print a bundle rules.
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - Any channel belongs to bundle.
 * @param intf       [I] - Interface
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5.0.0
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_bundle_rules_query_print(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

/**
 * Query information of all provisioned bundles and print them
 *
 * @param device [I] - The device being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5.0.0
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_bundles_info_query_dump(uint32_t device);

/**
 * Query and show all bundle rules directly from hardware without asking FW
 *
 * @param device  [I] - The device being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 * @private
 */
odsp_status_t odsp_bundles_rules_direct_query_dump(uint32_t device);

/**
 * This function query and print out the list of configurations and states
 * that GUI requires to display
 *
 * @param device          [I] - The device being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
void odsp_interfaces_info_query_dump(uint32_t device);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

/**
 * @h2 Link Status Methods
 * =======================================================
 * @brief
 *
 *
 * This method is used to wait for all channels in the package
 * to be in the receive ready state where they are ready to receive
 * traffic. This is state information is polled from the on-board
 * firmware.
 *
 * @{note,
 *   The timeout may be longer than the input specified as the
 *   overhead of accessing registers will increase the time}
 *
 * @{note,
 * This method is multi-die aware on packages that have
 * multiple dies so it will check the status across all dies inside
 * the package}
 *
 * @param device           [I] - The device being accessed.
 * @param timeout_in_usecs [I] - The amount of time to wait for all
 *                               channels to be ready in micro-seconds.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_wait_for_link_ready(
    uint32_t device,
    uint32_t timeout_in_usecs);

/**
 * This method is called to wait for link ready from a particular channel.
 *
 * @param die             [I] - The ASIC die being accessed.
 * @param channel         [I] - The channel to poll for link up.
 * @param intf            [I] - One or more interfaces to check. Note the intf
 *                              identifiers can be OR'd together.
 * @param timeout_in_usecs [I] - The timeout in microseconds to wait for the channel.
 *
 * @return ODSP_OK if the link is ready, ODSP_ERROR if the link is not ready.
 *
 * @since *******
 */
odsp_status_t odsp_channel_wait_for_link_ready(
    uint32_t     die,
    uint32_t     channel,
    e_odsp_intf  intf,
    uint32_t     timeout_in_usecs);

/**
* This method is called to wait for link ready from a particular
* channel via the function odsp_channel_is_link_ready().
* This method checks the channel interfaces to ensure the
* link is up.
*
* @param device           [I] - The device being accessed.
* @param ch_mask          [I] - The channel bit-mask to poll for link up.
* @param intf             [I] - One or more interfaces to check. Note the intf
*                              identifiers can be OR'd together.
* @param timeout_in_usecs [I] - The timeout in microseconds to wait for the channel.
*
* @return ODSP_OK if the link is ready, ODSP_ERROR if the link is not ready.
*
*/
odsp_status_t odsp_channel_mask_wait_for_link_ready(
    uint32_t   device,
    uint32_t   ch_mask,
    e_odsp_intf intf,
    uint32_t   timeout_in_usecs);

/**
* This method is called to determine whether particular interfaces or all
* interfaces of a channel link is up.
*
* @param device   [I] - The device being accessed.
* @param channel  [I] - The channel.
* @param intf     [I] - One or more interfaces to check. Note the intf
*                       identifiers can be OR'd together.
*
* @return true if the link is up, false if the link is down.
*
*/
bool odsp_channel_is_link_ready(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

/**
 * Link status structure
 */
typedef struct
{
    /** Signal detect status
     * - true if there is a signal present on the Rx
     * - false otherwise.
     *
     * The FW will not attempt to acquire lock unless this is true.
     */
    bool hrx_sdt[ODSP_MAX_CHANNELS+1];

    /** DSP status
     * - true if the DSP is locked and running adaptation
     * - false if the DSP is attempting to lock or is unable to maintain lock
     *
     * Like the LRX, the HRX has a HW FSM DSP that runs continuously in the background. This will be set
     * when the HW is locked to the incoming signal and is running the continuous adaptation FSMs.
     * Only use this for debugging, see hrx_fw_lock below.
     */
    bool hrx_dsp_ready[ODSP_MAX_CHANNELS+1];

    /** FW has determined that the channel is locked and ready to pass traffic.
     * - true if FW is in data/mission mode.
     * - false otherwise
     *
     * The FW will only set this when all the above status signals are true (SDT, VCO lock, CDR lock) and when
     * data/mission mode adaptation has stabilized. This status will be valid in all modes and configurations,
     * including refless where the VCO/CDR lock aren't used.
     */
    bool hrx_fw_lock[ODSP_MAX_CHANNELS+1];

    /** PLL lock
     *
     * The HRX PLL Lock status.
     *
     */
    bool hrx_pll_lock[ODSP_MAX_CHANNELS+1];

    /** PLL FSM state
     *
     * This is the current state of the HRX PLL FSM.
     *
     */
    uint8_t hrx_pll_fsm_state[ODSP_MAX_CHANNELS+1];

    /** FSM state
     *
     * This is current state of the HRX FSM state machine. This is a lower
     * level HW indicator and should be used for debugging only.
     *
     */
    uint8_t hrx_fsm_state[ODSP_MAX_CHANNELS+1];

    /** Number of times this channel has had to re-acquire lock.
     *
     * This is only incremented when FW has achieved lock but was interrupted for some reason (HW/FW faults,
     * loss of lock, loss of SDT, etc.). This will not continue to increment unless there is a signal present
     * on the interface.
     */
    uint8_t hrx_reset_cnt[ODSP_MAX_CHANNELS+1];

    // HTX
    /** FW has determined that the channel is locked and ready to pass traffic.
     * - true if FW is in data/mission mode.
     * - false otherwise
     *
     * The FW will only set this when the Tx DSP is ready and when the upstream traffic source (LRX, HRX,
     * PRBS generator) are ready as well.
     */
    bool htx_fw_lock[ODSP_MAX_CHANNELS+1];
    /** PLL lock
     *
     * The HTX PLL lock status.
     */
    bool htx_pll_lock[ODSP_MAX_CHANNELS+1];

    /** PLL FSM state
     *
     * This is the current state of the HTX PLL FSM.
     *
     */
    uint8_t htx_pll_fsm_state[ODSP_MAX_CHANNELS+1];

    /** FSM state
     *
     * This is the current state of the HTX FSM state machine. This is a lower
     * level HW indicator and should be used for debugging only.
     *
     */
    uint8_t htx_fsm_state[ODSP_MAX_CHANNELS+1];

    /** Number of times this channel has had to re-acquire lock.
     *
     * This is only incremented when FW has achieved lock but was interrupted for some reason (HW/FW faults,
     * loss of lock, loss of SDT, etc.). This will not continue to increment unless there is a signal present
     * on the interface.
     */
    uint8_t  htx_reset_cnt[ODSP_MAX_CHANNELS+1];

    // LTX
    /** FW has determined that the channel is locked and ready to pass traffic.
     * - true if FW is in data/mission mode.
     * - false otherwise
     *
     * The FW will only set this when the Tx DSP is ready and when the upstream traffic source (LRX, HRX,
     * PRBS generator) are ready as well.
     */
    bool ltx_fw_lock[ODSP_MAX_CHANNELS+1];

    /** PLL lock
     *
     * The LTX PLL Lock status.
     *
     */
    bool ltx_pll_lock[ODSP_MAX_CHANNELS+1];

    /** PLL FSM state
     *
     * This is the current state of the LTX PLL FSM.
     *
     */
    uint8_t ltx_pll_fsm_state[ODSP_MAX_CHANNELS+1];

    /** FSM state
     *
     * This is current state of the LTX FSM state machine. This is a lower
     * level HW indicator and should be used for debugging only.
     *
     */
    uint8_t ltx_fsm_state[ODSP_MAX_CHANNELS+1];

    /** Number of times this channel has had to re-acquire lock.
     *
     * This is only incremented when FW has achieved lock but was interrupted for some reason (HW/FW faults,
     * loss of lock, loss of SDT, etc.). This will not continue to increment unless there is a signal present
     * on the interface.
     */
    uint8_t  ltx_reset_cnt[ODSP_MAX_CHANNELS+1];

    // LRX
    /** Signal detect status
     * - true if there is a signal present on the Rx
     * - false otherwise.
     *
     * The FW will not attempt to acquire lock unless this is true.
     */
    bool lrx_sdt[ODSP_MAX_CHANNELS+1];

    /** DSP status
     * - true if the DSP is locked and running adaptation
     * - false if the DSP is attempting to lock or is unable to maintain lock
     *
     * Unlike the HRX, the LRX has a HW FSM DSP that runs continuously in the background. This will be set
     * when the HW is locked to the incoming signal and is running the continuous adaptation FSMs.
     * Only use this for debugging, see lrx_fw_lock below.
     */
    bool lrx_dsp_ready[ODSP_MAX_CHANNELS+1];

    /** FW has determined that the channel is locked and ready to pass traffic.
     * - true if FW is in data/mission mode.
     * - false otherwise
     *
     * The FW will only set this when all the above status signals are true (SDT, DSP ready) and when
     * data/mission mode adaptation has stabilized. This status will be valid in all modes and configurations,
     * including refless where the LRX relies on a clock from the HRX.
     */
    bool lrx_fw_lock[ODSP_MAX_CHANNELS+1];

    /** PLL lock
     *
     * The LRX PLL Lock status.
     *
     */
    bool lrx_pll_lock[ODSP_MAX_CHANNELS+1];

    /** PLL FSM state
     *
     * This is the current state of the LRX PLL FSM.
     *
     */
    uint8_t lrx_pll_fsm_state[ODSP_MAX_CHANNELS+1];

    /** FSM state
     *
     * This is current state of the LRX FSM state machine. This is a lower
     * level HW indicator and should be used for debugging only.
     *
     */
    uint8_t lrx_fsm_state[ODSP_MAX_CHANNELS+1];

    /** Number of times this channel has had to re-acquire lock.
     *
     * This is only incremented when FW has achieved lock but was interrupted for some reason (HW/FW faults,
     * loss of lock, loss of SDT, etc.). This will not continue to increment unless there is a signal present
     * on the interface.
     */
    uint8_t  lrx_reset_cnt[ODSP_MAX_CHANNELS+1];
} odsp_link_status_t;

/**
 * This method may be called to query the current link status
 * of the interfaces of the device.
 *
 * @param device      [I] - The device being accessed.
 * @param link_status [O] - Pointer to the link status. Elements are indexed by package channel.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******   
 */
odsp_status_t odsp_link_status_query(
    uint32_t device,
    odsp_link_status_t* link_status);

/**
 * This method may be called to query the current link status
 * of the specific channels based on input channel mask
 *
 * @param device       [I] - The device being accessed.
 * @param channel_mask [I] - The channel mask of channels being accessed
 * @param p_link_status  [O] - Pointer to the link status. Elements are indexed by package channel.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_multi_link_status_query(
    uint32_t device,
    uint32_t channel_mask,
    odsp_link_status_t *p_link_status);

/**
 * This is a diagnostic method that is called to toggle the current ieee demap
 * status of the RX/TX channel after it has been configured.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The physical channel to toggle the ieee_demap for.
 * @param intf    [I] - The interface to toggle.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_ieee_demap_toggle(
    uint32_t         device,
    uint32_t         channel,
    e_odsp_intf      intf);

/**
 * This is a diagnostic method that is called to toggle the current gray mapping
 * status of the RX/TX channel after it has been configured.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The physical channel to toggle the gray mapping for.
 * @param intf    [I] - The interface to toggle.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_gray_mapping_toggle(
    uint32_t          device,
    uint32_t          channel,
    e_odsp_intf       intf);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method may be called to print the current link status
 * of the interfaces of the device.
 *
 * @param device      [I] - The device being accessed.
 * @param link_status [I] - Pointer to the link status. Note that the link_status elements are
 *                          all 0-based regardless of the package type.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since *******   
 *
 */
odsp_status_t odsp_link_status_print(
    uint32_t device,
    odsp_link_status_t* link_status);

/**
 * This method may be called to query then print the current link status
 * of the interfaces of the device.
 *
 * @param device [I] - The device being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since *******
 *
 */
odsp_status_t odsp_link_status_query_print(
    uint32_t device);

/**
 * This method may be called to print the current link status
 * of the specific channels based on input channel mask
 *
 * @param device        [I] - The device being accessed.
 * @param channel_mask  [I] - The channel mask of channels being accessed
 * @param p_link_status [I] - Pointer to the link status. Elements are indexed by package channel.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since *******
 *
 */
odsp_status_t odsp_multi_link_status_print(
    uint32_t device,
    uint32_t channel_mask,
    odsp_link_status_t *p_link_status);

/**
 * This method may be called to query then print the current link status
 * of the specific channels based on input channel mask
 *
 * @param device       [I] - The device being accessed.
 * @param channel_mask [I] - The channel mask of channels being accessed
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since *******
 *
 */
odsp_status_t odsp_multilink_status_query_print(
    uint32_t device, 
    uint32_t channel_mask);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h2 Quality-Check (QC) Query
 * =======================================================
 *
 *
 * @brief
 * This method is used to query the QC rules for the specified channel.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to query QC
 * @param intf        [I] - The interface, see e_odsp_intf enum
 * @param qc_rules    [O] - The pointer of the QC rules structure to be returned
 *
 * @since 0.2
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_channel_rx_qc_rules_query(
    uint32_t            device,
    uint32_t            channel,
    e_odsp_intf         intf,
    odsp_rx_qc_rules_t *qc_rules);

/**
 * This method is used to query the QC status for the specified channel.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to query QC
 * @param intf        [I] - The interface, see e_odsp_intf enum
 * @param qc_status   [O] - The pointer of the QC status structure to be returned
 *
 * @since 0.2
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_channel_rx_qc_status_query(
    uint32_t            device,
    uint32_t            channel,
    e_odsp_intf         intf,
    odsp_rx_qc_status_t *qc_status);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method is used to query and print the QC rules and the QC status
 * for the specified channel.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to query QC
 * @param intf        [I] - The interface, see e_odsp_intf enum
 *
 * @since 0.2
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_channel_rx_qc_print(
    uint32_t           device,
    uint32_t           channel,
    e_odsp_intf        intf);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h2 Temperature Query
 * =======================================================
 * Supports temperature reporting.
 *
 * @brief
 * This method is used to query the temperature from the device. The
 * temperature is reported in degrees Celsius.
 *
 *  @{note, In python this method accepts one arguments only (die) and
 * returns (status, temperature)}
 * 
 * @param device      [I] - The device being accessed.
 * @param temperature [O] - The output temperature read from the device in
 *                          degress Celsius.
 *
 * @since 0.1
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_temperature_query(
    uint32_t device, 
    int32_t* temperature);

/**
 * @h2 LOS Query
 *
 * @brief
 * This method is used to query RX LOS status real-time.
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel to access.
 * @param intf      [I] - The Rx interface to access, ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return true if there is LOS otherwise false.
 *
 * @since 0.1.0.0
 */
bool odsp_rx_los_get(
    uint32_t device, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method is used to query RX LOS latched status
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel to access.
 * @param intf      [I] - The Rx interface to access, ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return true if there is latched LOS otherwise false.
 *
 * @since 0.1.0.0
 */
bool odsp_rx_latched_los_get(
    uint32_t device, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method is used to query then clear RX LOS latched interrupt status
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel to access.
 * @param intf      [I] - The Rx interface to access, ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return true if there is latched LOS otherwise false.
 *
 * @since 0.3.0.0
 */
bool odsp_rx_latched_los_clear(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);

/**
 * @h2 TX Management
 *
 * @brief
 * Query the TX FIR configuration.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to query.
 * @param intf        [I] - The interface to query.
 * @param p_fir_rules [O] - The queried TX FIR rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since *******
 */
odsp_status_t odsp_tx_fir_query(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    odsp_tx_fir_rules_t *p_fir_rules);

/**
 * Update the TX FIR configuration of the channel
 * 
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to update.
 * @param intf        [I] - The interface to update.
 * @param p_fir_rules [I] - The FIR rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since *******
 */
odsp_status_t odsp_tx_fir_set(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    odsp_tx_fir_rules_t *p_fir_rules);

/**
 * Query the TX ADV Tune parameters.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The channel to query.
 * @param p_adv_tune  [O] - The queried TX ADV tune parameters.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since 0.1.0.0
 * @private
 */
odsp_status_t odsp_ltx_adv_tune_query(
    uint32_t    device,
    uint32_t    channel,
    odsp_tx_adv_tune_t *p_adv_tune);

/**
 * Update the TX ADV Tune parameters of the channel
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel to update.
 * @param p_adv_tune [I] - The TX ADV tune parameters.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since 0.1.0.0
 * @private
 */
odsp_status_t odsp_ltx_adv_tune_set(
    uint32_t     device,
    uint32_t     channel,
    odsp_tx_adv_tune_t *p_adv_tune);

/**
 * Squelch the transmit output for the specified channel
 * 
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel to access.
 * @param intf       [I] - The interface to access.
 * @param squelch    [I] - Flag, True or False.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since 0.1.0.0
 */
odsp_status_t odsp_tx_squelch(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    bool         squelch);

/**
 * Get current squelching state of a specific channel
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel to access.
 * @param intf       [I] - The interface to access.
 *
 * @return true if channel is squelched, false if channel is un-squelched
 *
 * @since 0.1.0.0
 */
bool odsp_tx_is_squelched(
    uint32_t device, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method may be called to enable or disable the squelch lock for the
 * output transmitter, apply for a specific channel
 *
 * Setting squelch_lock to true/false does not have an immediate effect on the transmitter's state.
 * This setting will only be used by the FW the next time the Transmitter is brought up to determine
 * if the Tx is brought up dark (ie. Tx squelched), or as usual (Tx unsquelched)
 *
 * @param device       [I] - The device being accessed.
 * @param channel      [I] - The channel to access.
 * @param intf         [I] - The TX interface to squelch (ODSP_INTF_LTX or ODSP_INTF_HTX)
 * @param squelch_lock [I] - True to enable the transmit squelch lock, false to disable the
 *                      transmit squelch lock.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_tx_squelch_lock(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    bool         squelch_lock);

/**
 * This method may be called to enable or disable the squelch lock for the
 * output transmitter, apply for a list of channels
 *
 * Setting squelch_lock to true/false does not have an immediate effect on the transmitter's state.
 * This setting will only be used by the FW the next time the Transmitter is brought up to determine
 * if the Tx is brought up dark (ie. Tx squelched), or as usual (Tx unsquelched)
 *
 * @param device       [I] - The device being accessed.
 * @param channel_mask [I] - The channel mask through the device to change,
 *                           value range is 0x2-0x1FE
 * @param intf         [I] - The TX interface to squelch (ODSP_INTF_LTX or ODSP_INTF_HTX)
 * @param squelch_lock [I] - True to enable the transmit squelch lock, false to disable the
 *                      transmit squelch lock.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_tx_channels_squelch_lock(
                                            uint32_t     device,
                                            uint32_t     channel_mask,
                                            e_odsp_intf  intf,
                                            bool         squelch_lock);

/**
 * This is a diagnostic method that is called to toggle the current invert
 * status of the TX channel after it has been configured. This is sometimes
 * useful when trying to determine the hardware inversions.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The physical channel to toggle the invert for.
 * @param intf    [I] - The interface to toggle.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_tx_invert_toggle(
    uint32_t          device,
    uint32_t          channel,
    e_odsp_intf       intf);

/**
 * This method is called to run monclk transmitter
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param p_rules    [I] - The MONCLK rules.
 * @since Alpha 0.4
 */
odsp_status_t odsp_dbg_monclk_set(
    uint32_t          die,
    odsp_monclk_rules_t *p_rules);

/**
 * This method is called to query  MONCLK rules
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param p_rules    [O] - The MONCLK rules.
 * @since Alpha 0.4
 */
odsp_status_t odsp_dbg_monclk_query(
    uint32_t          die,
    odsp_monclk_rules_t *p_rules);

/** 
 * @h2 RX Management
 * =======================================================
 * The following APIs are used to manage RX interface.
 * 
 * @h3 RX Control
 *
 * @brief
 * This is a diagnostic method that is called to toggle the current invert
 * status of the RX channel after it has been configured. This is sometimes
 * useful when trying to determine the hardware inversions.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The physical channel to toggle the invert for.
 * @param intf    [I] - The interface to toggle.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_rx_invert_toggle(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

/**
 * @h3 FFE Taps
 * =======================================================
 * 
 * This section contains routines used to query the FFE
 * tap values for each of the 16 interleaves
 *
 * @brief
 * 
 * Query the FFE taps
 *
 * To get all 16 floating point value for each of the ffe_taps, divide by 64.0
 * - Supported interface: ODSP_INTF_LRX and ODSP_INTF_HRX
 * - Index 0-14 represent 15 FFE taps
 *
 * @param device   [I] - The device being accessed.
 * @param channel  [I] - The channel through the device to query.
 * @param intf     [I] - The interface.Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param ffe_taps [O] - The array of taps to populate (S7.6 format).
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 */
odsp_status_t odsp_rx_dsp_ffe_taps_query(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    int16_t     ffe_taps[ODSP_FFE_TAP_COUNT]);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * Query then print the FFE taps for a particular channel
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 *
 * @since 0.1.0.0
 *
 */
odsp_status_t odsp_rx_dsp_ffe_taps_query_print(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h3 Reflection Canceller Taps
 * =======================================================
 *
 * This section contains routines used to query the reflection canceler
 * tap values
 *
 * @brief
 *
 * Query the RC taps
 *
 * - Supported interface: ODSP_INTF_LRX and ODSP_INTF_HRX
 * - Index 0-15 represent 16 RC taps
 *
 * @param device          [I] - The device being accessed.
 * @param channel         [I] - The channel through the device to query.
 * @param intf            [I] - The interface.Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param rc_taps         [O] - The array of taps to populate (S7.6 format).
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.5.0.0
 *
 */
odsp_status_t odsp_rx_dsp_rc_taps_query(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    int16_t     rc_taps[ODSP_RC_TAP_COUNT]);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * Query then print the RC taps for a particular channel
 *
 * @param device          [I] - The device being accessed.
 * @param channel         [I] - The channel through the device to query.
 * @param intf            [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 *
 * @since 0.5.0.0
 *
 */
odsp_status_t odsp_rx_dsp_rc_taps_query_print(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h3 DFE Taps
 * =======================================================
 *
 * @brief
 * DFE Coefficient Values. These are stored as fixed point values
 * to minimize the use of floating point numbers. To convert to floating
 * point:
 *
 *    dfe_f1    = (dfe_f1    * 100000)/64
 *    dfe_nlfb0 = (dfe_nlfb0 * 100000)/64
 *    dfe_nlfb1 = (dfe_nlfb0 * 100000)/64
 */
typedef struct
{
    /** The main DFE tap */
    int32_t dfe_f1;

    /** Non Linear Feedback */
    int32_t dfe_nlfb0;

    /** Non Linear Feedback */
    int32_t dfe_nlfb1;
}odsp_rx_dsp_dfe_coefficients_t;

/**
 * This method extracts the DFE F1 tap coefficient
 * and non-linear feedback terms for each of 8 sub-channels of
 * the selected channel.
 *
 * @param device      [I] - The device being accessed.
 * @param channel     [I] - The RX channel to extract the DFE tap from.
 * @param intf        [I] - The RX interface to query
 * @param sub_channel [I] - The DFE sub-channel.
 * @param dfe_coeffs  [O] - The DFE coefficients
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.4
 */
odsp_status_t odsp_rx_dsp_dfe_get_coefficients(
    uint32_t                       device,
    uint32_t                       channel,
    e_odsp_intf                     intf,
    uint32_t                       sub_channel,
    odsp_rx_dsp_dfe_coefficients_t* dfe_coeffs);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method extracts then prints the DFE F1 tap coefficient
 * and non-linear feedback terms for each of 8 sub-channels of
 * the selected channel.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The RX channel to extract the DFE tap from.
 * @param intf    [I] - The RX interface to query
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 *
 * @since 0.4
 */
odsp_status_t odsp_rx_dsp_dfe_coefficients_print(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * @h3 LTP Monitor
 * The following methods are used to query
 * the Level Transition Parameter (LTP).
 *
 * @note Precondition to run LTP and MPI measurements is QC need to be running. If QC is disabled API will report LTP = 0 and mpi_metric =0.
 *
 * @brief 
 * Read the fixed-point minimum LTP (Level Transition Parameter) on an interface/channel.
 * LTP is defined in the CMIS 4-0 spec as:
 *
 * @{pre,
 * LTP = 10*log10(min(ltp0, ltp1, ltp2))
 *   where ltp(i) = (P(i+1) + P(i))/(2*V(i))
 * }
 *
 * Returned as a 16.8 value (unsigned, 8 fractional bits) in units of 1/256dB.
 *
 * Special values:
 * - 0 is an error value, or LTP unavailable
 * - 0xFFFF is +infinity, where V(i)=0
 * - 0xFFFE is a large dB value > 255.996dB
 *
 * CMIS - OIF-CMIS-05.2
 * - 7.1.4.2 PAM Level Transition Parameter (LTP)
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface (ODSP_INTF_LRX, ODSP_INTF_HRX only for now)
 * @param ltp     [O] - Minimum LTP
 *
 * @return ODSP_OK on success, ODSP_ERROR otherwise
 *
 * @see
 * odsp_rx_dsp_ltp_format to convert to a double value.
 *
 * @requires
 * LTP can be read on a per channel basis any time after that channel obtains FW lock.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_rx_dsp_ltp_read_fixp(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint16_t    *ltp);

/** MPI estimation structure */
typedef struct {
    /** Indexes 0-3 correspond to levels -3/-1/+1/+3 respectively. Format is u16.12. Variance is normalized to 10 bins histogram .
     * Conversion: \sigma^2= variance[i]/2^12 */
    uint16_t variances[4];
    /** MPI metric (Intercept/4 + slope). Format is u16.12. Conversion:   mpi = mpi_metric/2^12  */
    uint16_t mpi_metric;
    /** Slope. Format is s16.12. Conversion: slope = (int16)slope/2^12  */
    int16_t slope;
    /** Intercept. Format is u16.12. Conversion: intercept = intercept/2^12 */
    uint16_t intercept;
} odsp_mpi_metric_t;

/**
 * Read the MPI estimation metrics on an interface/channel.
 *
 * @note
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param channel    [I] - The channel through the device to query.
 * @param intf       [I] - The interface (ODSP_INTF_LRX).
 * @param mpi_metric [O] - MPI metric structure. See odsp_mpi_metric_t.
 *
 * @return ODSP_OK on success, ODSP_ERROR otherwise
 *
 * @since 0.7.0.0
 */
odsp_status_t odsp_rx_dsp_get_mpi_est(
                                    uint32_t die,
                                    uint32_t channel,
                                    e_odsp_intf intf,
                                    odsp_mpi_metric_t *mpi_est);

#if defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)
#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)
/**
 * Convert fixed-point 16.8 LTP value to a floating point double in dB.
 *
 * @param ltp [I] - LTP in 16.8 format
 *
 * @return LTP as a double in dB, INFINITY if ltp=0xffff.
 *
 * @see
 * odsp_rx_dsp_ltp_read_fixp to read the fixed-point ltp value.
 *
 * @requires
 * The API must be compiled with ODSP_HAS_MATH_DOT_H and ODSP_HAS_FLOATING_POINT flags.
 * This will pull in the <math.h> library which will increase the size of
 * the generated image.
 *
 * @since 0.6.0.0
 */
double odsp_rx_dsp_ltp_format(uint16_t ltp);

#endif // defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)
#endif // defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)

/**
 * @h3 SNR Monitor Methods
 * 
 * @brief
 * This method is used to determine whether or not the SNR monitor
 * is enabled or not.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface, see e_odsp_intf enum.
 *
 * @return true if the monitor is enabled, false otherwise.
 *
 * @since *******   
 */
bool odsp_rx_dsp_snr_mon_enabled(
    uint32_t device, 
    uint32_t channel,
    e_odsp_intf intf);

/**
 * This method is called to read the raw SNR monitor value from
 * the hardware for the input channel.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface, see e_odsp_intf enum.
 *
 * @return The raw SNR value read from the hardware.
 *
 * @since *******   
 */
uint16_t odsp_rx_dsp_snr_read_value(
    uint32_t   device, 
    uint32_t   channel,
    e_odsp_intf intf);

/**
 * This method is called to read the SNR monitor value from the hardware
 * and translate it to a decimal (fixed-point) dB value.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface, see e_odsp_intf enum.
 *                      Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return The (fixed-point) SNR value in dB
 *
 * @see
 * odsp_rx_dsp_snr_read_value() to return the raw SNR value reported
 * by the hardware that has not been converted to dB.
 *
 * @since *******
 */
uint32_t odsp_rx_dsp_snr_read_db_fixp(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);

#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)
/**
 * This method is called to read the SNR monitor value from the hardware
 * and translate it to a decimal dB value.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface.
 *
 * @return The SNR value in dB
 *
 * @see
 * odsp_rx_dsp_snr_read_value() to return the raw SNR value reported
 *
 * by the hardware that has not been converted to dB.
 *
 * @requires
 * The API must be compiled with ODSP_HAS_MATH_DOT_H and ODSP_HAS_FLOATING_POINT flags.
 * This will pull in the <math.h> library which will increase the size of
 * the generated image.
 *
 * @since *******   
 */
double odsp_rx_dsp_snr_read_db(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);

#endif // defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)

/**
 * @h3 Histogram
 * =======================================================
 * The following methods are used to manage capturing and
 * displaying the histogram.
 *
 * @brief
 *
 * Initialize then capture the RX DSP histogram for a given die, channel.
 * Note that this method captures the histogram data from the HW.
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel through the device to query.
 * @param intf       [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param histo      [O] - This must be a buffer of [80] entries of uint32_t
 *
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @see ODSP_HIST_DATA_SIZE_MAX
 *
 * @since *******
 */
odsp_status_t odsp_rx_dsp_hist_get(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t*   histo);

#if defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)
/**
 * ASCII plot the DSP histogram data for a given die, channel.
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel through the device to query.
 * @param intf       [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param histo      [I] - Pointer to the histogram data of exactly [80] entries of uint32_t.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with math.h support:
 * - ODSP_HAS_MATH_DOT_H=1
 *
 * @see ODSP_HIST_DATA_SIZE_MAX
 * @since *******
 */
odsp_status_t odsp_rx_dsp_hist_ascii_plot(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t*   histo);

/**
 * Capture then plot the RX DSP histogram data for a given channel
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with math.h support:
 * - ODSP_HAS_MATH_DOT_H=1
 *
 * @since *******
 */
odsp_status_t odsp_rx_dsp_hist_query_dump(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)

/**
 * Initialize then capture the RX DSP histogram for a given die, channel.
 * Note that this method captures the histogram data from the HW.
 *
 * @param device     [I] - The device being accessed.
 * @param channel    [I] - The channel through the device to query.
 * @param intf       [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param hist_data      [O] - This must be a buffer of [80] entries of uint32_t, will be populated with the histogram
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @see ODSP_HIST_DATA_SIZE_MAX
 *
 * @since Alpha 0.3
 */
odsp_status_t odsp_rx_dsp_cdf_hist_get(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t   *hist_data);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
/**
 * Capture then plot the RX DSP histogram data for a given channel
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with math.h support:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since Alpha 0.3
 */
odsp_status_t odsp_rx_dsp_cdf_hist_query_dump(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

/**
 * Capture the maximum 64 words of RX ADC histogram data for a given channel
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel through the device to query.
 * @param intf      [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param hist_data [O] - An array of the histogram data of exactly [64] entries of uint32_t
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since Alpha 0.2
 */
odsp_status_t odsp_rx_adc_hist_get(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    uint32_t*    hist_data);

/**
 * Capture the maximum 64 words of RX ADC histogram data for a given channel
 * based on CDF threshold
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel through the device to query.
 * @param intf      [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param hist_data [O] - An array of the histogram data of exactly [64] entries of uint32_t.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since Alpha 0.3
 */
odsp_status_t odsp_rx_adc_cdf_hist_get(
    uint32_t     device,
    uint32_t     channel,
    e_odsp_intf  intf,
    uint32_t*    hist_data);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
/**
 * Plot RX ADC histogram data in ascii for a given channel
 *
 * @param device    [I] - The device being accessed.
 * @param channel   [I] - The channel through the device to query.
 * @param intf      [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param hist_data [I] - An array of the histogram data of exactly [64] entries of uint32_t.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since Alpha 0.2
 */
odsp_status_t odsp_rx_adc_hist_ascii_plot(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t*   hist_data);

/**
 * Query and plot RX ADC histogram data in ascii for a given channel
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since Alpha 0.2
 */
odsp_status_t odsp_rx_adc_hist_query_dump(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);

/**
 * Query and plot RX ADC histogram data in ascii for a given channel based on
 * CDF threshold
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - The channel through the device to query.
 * @param intf    [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since Alpha 0.3
 */
odsp_status_t odsp_rx_adc_cdf_hist_query_dump(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)
/**
 * @h3 Pulse Response
 * =======================================================
 * The pulse response interface can be used to help tune the transmit taps.
 *
 * @brief
 * This method queries the pulse response values from the FW for a
 * particular HRX channel.
 *
 * @param device              [I] - The device being accessed.
 * @param channel             [I] - Channel that was used in the info request.
 * @param intf                [I] - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param estimator_length    [I] - Number of accumulation to be done = 2^(8+estimator_length). Maximum is 2^20.
 * @param resp_values         [O] - An array of Pulse Response Data of exactly [30] entries of int32_t.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_FLOATING_POINT==1
 * 
 * @since 0.2.0.0
 */
odsp_status_t odsp_rx_pulse_response_query(
    uint32_t    device,
    uint32_t    channel,
    e_odsp_intf intf,
    uint32_t    estimator_length,
    int32_t     resp_values[ODSP_PULSE_RESP_SAMPLE_NUM]);

/**
 * This method is called to query the pulse response for specific HRX channels
 * based on input channel mask
 *
 * @param device           [I] - The device being accessed.
 * @param ch_mask          [I]   - Channel mask corresponds to channels that was used in
 *                                 the info request(1-based, value from 0x2-0x1FE)
 * @param intf             [I]   - The interface. Valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param estimator_length [I]   - Number of accumulation to be done = 2^(8+estimator_length). Maximum is 2^20.
 * @param resp_values      [I/O] - Pulse Response Data (1-based channel index)
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 * - ODSP_HAS_FLOATING_POINT==1
 *
 * @since 0.2.0.0
 */
odsp_status_t odsp_rx_channel_mask_pulse_response_query(
    uint32_t    device,
    uint32_t    ch_mask,
    e_odsp_intf intf,
    uint32_t    estimator_length,
    int32_t     resp_values[ODSP_MAX_CHANNELS + 1][ODSP_PULSE_RESP_SAMPLE_NUM]);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method is called to print the pulse response for a channel on
 * the specified interface.
 *
 * @param device  [I] - The device being accessed.
 * @param channel [I] - Channel that was used in the info request.
 * @param intf    [I] - The interface.
 * @param resp_values [I/O] - Pulse Response Data.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 * - ODSP_HAS_FLOATING_POINT==1
 *
 * @since 0.2.0.0
 */
odsp_status_t odsp_rx_pulse_response_dump(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf,
    int32_t resp_values[ODSP_PULSE_RESP_SAMPLE_NUM]);

/**
 * This method is called to query the pulse response for a channel on
 * the specified interface and prints them.
 *
 * @param device           [I] - The device being accessed.
 * @param channel          [I] - Channel that was used in the info request.
 * @param intf             [I] - The interface.
 * @param estimator_length [I] - Number of accumulation to be done = 2^(8+estimator_length). Maximum is 2^20.
 * 
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 * - ODSP_HAS_FLOATING_POINT==1
 *
 * @since 0.2.0.0
 */
odsp_status_t odsp_rx_pulse_response_query_dump(
    uint32_t device,
    uint32_t channel,
    e_odsp_intf intf,
    uint32_t estimator_length);

/**
 * This method is called to query the pulse response for channels based on
 * input channel mask on the specified interface and prints them.
 *
 * @param device           [I] - The device being accessed.
 * @param chn_mask         [I] - Channel mask corresponds to channels that was used in
 *                               the info request(1-based, value from 0x2-0x1FE)
 * @param intf             [I] - The interface.
 * @param estimator_length [I] - Number of accumulation to be done = 2^(8+estimator_length). Maximum is 2^20.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * The API must be compiled with the following compilation define:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS == 1
 * - ODSP_HAS_FLOATING_POINT==1
 *
 * @since 0.2.0.0
 */
odsp_status_t odsp_rx_channel_mask_pulse_response_query_dump(
    uint32_t    device,
    uint32_t    chn_mask,
    e_odsp_intf intf,
    uint32_t    estimator_length);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
#endif //defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)

/**
 * This method is called to specify a bundle to monitor CDR frequency.
 *
 * @param device           [I] - The device being accessed.
 * @param channel          [I] - Any channel belongs to a bundle
 * @param intf             [I] - The interface.
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 1.0.0.0
 */
odsp_status_t odsp_cdr_freq_mon_bundle_set(
    uint32_t device, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method is called to query CDR frequency PPM offset
 * which is monitored and updated by Firmware.
 *
 * @param device           [I] - The device being accessed.
 * @param intf             [I] - The interface to query frequency, 
 *                               valid value is ODSP_INTF_LRX, ODSP_INTF_HRX.
 * @param freq_ppm         [O] - The CDR frequency PPM offset in S16.6 format.
 *                               To get floating point value, divide by 64.0
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 1.0.0.0
 */
odsp_status_t odsp_cdr_freq_ppm_get_fixp(
    uint32_t device, 
    e_odsp_intf intf, 
    int16_t* freq_ppm);

#if defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)
#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)
/**
 * Convert fixed-point 16.6 PPM offset value to a floating point float.
 *
 * @param freq_ppm [I] - ppm in 16.8 format
 *
 * @return ppm as a floating point float
 *
 * @see
 * odsp_cdr_freq_ppm_get_fixp to read the fixed-point PPM offset value.
 *
 * @requires
 * The API must be compiled with ODSP_HAS_MATH_DOT_H and ODSP_HAS_FLOATING_POINT flags.
 * This will pull in the <math.h> library which will increase the size of
 * the generated image.
 *
 * @since 1.0.0.0
 */
float odsp_cdr_freq_ppm_float_format(int16_t freq_ppm);

#endif // defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT == 1)
#endif // defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)

/**
 * Get bundle rules directly from hardware without asking FW
 *
 * @param device  [I] - The device being accessed.
 * @param bundle  [I] - The bundle ID.
 * @param p_rules [O] - The bundle rules
 * 
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 * @private
 */
odsp_status_t odsp_bundle_rules_direct_get(
    uint32_t     device,
    uint32_t     bundle,
    odsp_rules_t *p_rules);

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif //__ODSP_H__

/** @file odsp_fec.h
 ******************************************************************************
 * @brief
 *  Spica5 FEC API definition and prototypes
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2023 Marvell Corp. All rights reserved.
 ******************************************************************************/

#ifndef __ODSP_FEC_H__
#define __ODSP_FEC_H__
 
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @h2 FEC Utilities
 * =======================================================
 * 
 * @h3 FW FEC stats polling (CMIS VDM)
 *
 * The following structures are used for polling stats inside the FW and pulling the min/max/accumulated/current
 * stats out of the FW and into the API.
 *
 * @h4 Poll timing
 *
 * accumulation_time is the time the stats is accumulated into the firmware counters periodically.
 * At the end of the accumulation_time, the HW counters are read by the FW and saved to the FWs internal
 * stats counters. These FW stats counters are large enough to accumulate ~1 year of stats (64bit ints).
 *
 * @note
 * interval_time is obsolete and should be set to zero.
 *
 * @note
 * The FW stats counter sizes allow for a considerable amount of accumulation time (~1 year), however you must
 * design your VDM system software to clear the internal FW stats at some point. Catching a stats overflow after
 * a year of operation will be difficult to debug!
 *
 * @brief
 * Rules for configuring the FEC stats polling in the FW.
 */
typedef struct
{
    /**
     * Enable FW FEC stats collection.
     *
     * Note your main configuration rules (ie the rules used for odsp_enter_operational_state) need
     * to have the FEC block enabled in order for stats collection to work. You do not need to be
     * in ODSP_FEC_MODE_REGEN mode; ODSP_FEC_MODE_BYPASS will work for stats monitoring.
     */
    bool en;

    /**
     * This field is obsolete and should be set to zero
     */
    uint32_t interval_time;

    /**
     * Period for FW fec collect FEC stats
     */
    uint32_t accumulation_time;
} odsp_fec_stats_poller_rules_t;

/**
 * Generate the FEC rules for monitor
 *
 * @param die           [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel belong to a bundle which entered operational state
 * @param intf          [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param enable         [I] - Enable/disable FEC rules.
 *
 * @param p_rules       [O] - The FEC rules being used to configure the FEC processor.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_mon_rules_default_set(
    uint32_t          die,
    uint16_t          channel,
    e_odsp_intf       intf,
    bool              enable,
    odsp_fec_rules_t  *p_rules);

/**
 * Enable/disable FEC monitoring for a FEC stream that RX PMD channels belong to.
 * These channels can belong to different bundles so that user can setup 8x100G PMD bundles,
 * then setup FEC monitor for 100G/200G/400G FEC on the fly. This function only used in ODSP_FEC_MODE_BYPASS fec mode.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed. It is one of the channels indicated in the stream_chans of the fec rules.
 *                           It is representative for its FEC stream.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param fec_rules    [I] - The rules being used to configure the FEC monitor.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_mon_cfg(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_rules_t *fec_rules);

/**
 * Query the FEC rules by channel which has been enabled for a FEC channel (stream).
 * When the channel is not enable for any FEC channel, the fec_rules.enable field is disabled.
 * To query fec rules associated with a FEC channel, users can use odsp_fec_stream_rules_query()
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed which is used to lookup the FEC channel/stream this channel belong to.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param fec_rules    [O] - The rules being used to configure the FEC monitor.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_rules_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_rules_t *fec_rules);

/**
 * This method is to lookup a FEC stream (FEC channel) from its RX PMD channel at an interface
 * of the device. This method is software lookup.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param channel     [I] - The channel being accessed.
 * @param intf        [I] - The interface being accessed. ODSP_INTF_IG/ODSP_INTF_EG is acceptable
 *
 * @return HW FEC stream or HW FEC channel on success, 0xFF when this PMD channel has not set in any FEC stream.
 * @since 0.6.0.0
 */
uint8_t odsp_package_channel_to_fec_stream(uint32_t die, uint32_t channel, e_odsp_intf intf);

/**
 * Query the FEC rules by FEC channel (stream)
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param stream      [I] - The FEC channel or stream being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param fec_rules    [O] - The rules being used to configure the FEC monitor.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_stream_rules_query(uint32_t die,
                               uint32_t stream,
                               e_odsp_intf intf,
                               odsp_fec_rules_t *fec_rules);

/**
 * This method figures out the FEC stream IDX and the die that it resides on.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param sanity_check [I] - Enable/disable checking if FEC monitoring is enabled on input interface
 * @param fec_stream_idx    [O] - The HW fec channel or FEC stream.
 * @param fec_stream_die    [O] - The current die.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_get_fec_stream(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    bool sanity_check,
    uint32_t *fec_stream_idx,
    uint32_t *fec_stream_die);

/**
 * Check to see of the FW FEC monitor is ready for stats polling.
 *
 * @param die              [I] - The ASIC die being accessed.
 * @param channel          [I] - The channel being accessed.
 * @param intf             [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param timeout_in_usecs [I] - The timeout in units of usecs.
 *
 * @return true on fec lock, false on fec has not lock.
 *
 * @since 0.6.0.0
 *
 */
bool odsp_fec_mon_is_ready(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    uint32_t timeout_in_usecs);

/**
 * Configure the internal FW FEC stats polling.
 *
 * This can be done any time after odsp_init. If a previous call has enabled the FEC stats polling in the FW
 * a call to this method will simply change the timer durations and trigger a new polling cycle, it will not
 * clear the already accumulated FEC stats saved by the FW.
 *
 * @{note, As soon as the FW FEC stats polling is enabled, you MUST NOT use the
 * snapshot_all or snapshot_intf methods on the FEC intf being polled by the
 * FW. Otherwise the FW may miss some FEC stats.}
 *
 * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param stats_rules    [I] - The rules being used to configure the poller.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_stats_poller_cfg(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    const odsp_fec_stats_poller_rules_t *stats_rules);

/**
 * Query rules for the internal FW FEC stats polling.
 *
  * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param stats_rules    [I] - The rules being used to configure the poller.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_stats_poller_rules_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_stats_poller_rules_t *stats_rules);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * Query and print FEC stats poller rules.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param channel     [I] - Channel indicate its bundle.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_stats_poller_rules_query_dump(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * FEC stats, pre-formatted in the correct CMIS F16 format. Can be presented to the CMIS interface directly
 * without conversion.
 *
 * F16 is a 16bit floating point format with:
 * - bits[15:11] as exponent (e), biased by o
 * - bits[10:0] as mantissa
 *
 * Where the resulting value is defined as
 *   value = m*10^(e+o)
 * And o = -24. If e=31, m=2047 then the value is +infinity. If e=31, m=0 then the value is NaN.
 *
 * There is an example conversion function (float_2_cmis_f16) in odsp_api.c included in this API package, as well
 * as the inverse function (cmis_f16_to_float) used for odsp_fec_stats_poller_calc_ber.
 *
 * This structure is defined in the order it is retrieved from FW memory.
 */
typedef struct {
    //preamble
    /** Poll count, gets incremented every time the poller snapshots the FEC HW registers
     * and cleared along with the rest of the saved/accumulated FEC stats.
     */
    uint32_t poll_count;

    //block 0
    /** Average Bit Error Rate */
    uint16_t ber_avg;
    /** Average number of errored Frames (FERC) */
    uint16_t ferc_avg;

    //block 1
    /** Current Bit Error Rate */
    uint16_t ber_curr;
    /** Current number of errored Frames (FERC) */
    uint16_t ferc_curr;

    //block 2
    /** Max Bit Error Rate */
    uint16_t ber_max;
    /** Max number of errored Frames (FERC) */
    uint16_t ferc_max;

    //block 3
    /** Min Bit Error Rate */
    uint16_t ber_min;
    /** Min number of errored Frames (FERC) */
    uint16_t ferc_min;

    //block 4
    /** Corrected CW rate for N number of bits */
    uint16_t corr_cw_hist_15_curr;
    uint16_t corr_cw_hist_15_avg;
    uint16_t corr_cw_hist_15_max;
    uint16_t corr_cw_hist_14_curr;
    uint16_t corr_cw_hist_14_avg;
    uint16_t corr_cw_hist_14_max;
    uint16_t corr_cw_hist_13_curr;
    uint16_t corr_cw_hist_13_avg;
    uint16_t corr_cw_hist_13_max;
    uint16_t corr_cw_hist_12_curr;
    uint16_t corr_cw_hist_12_avg;
    uint16_t corr_cw_hist_12_max;

    //block 5
    uint16_t corr_cw_hist_11_curr;
    uint16_t corr_cw_hist_11_avg;
    uint16_t corr_cw_hist_11_max;
    uint16_t corr_cw_hist_10_curr;
    uint16_t corr_cw_hist_10_avg;
    uint16_t corr_cw_hist_10_max;
    uint16_t corr_cw_hist_9_curr;
    uint16_t corr_cw_hist_9_avg;
    uint16_t corr_cw_hist_9_max;
    uint16_t corr_cw_hist_8_curr;
    uint16_t corr_cw_hist_8_avg;
    uint16_t corr_cw_hist_8_max;
    uint16_t corr_cw_hist_7_curr;
    uint16_t corr_cw_hist_7_avg;
    uint16_t corr_cw_hist_7_max;
    /** Current Symbol Error Rate */
    uint16_t ser_curr;
    /** Average Symbol Error Rate */
    uint16_t ser_avg;
    /** Max Symbol Error Rate */
    uint16_t ser_max;

    //block 6
    /** Corrected CW rate for N number of bits */
    uint16_t corr_cw_hist_6_curr;
    uint16_t corr_cw_hist_6_avg;
    uint16_t corr_cw_hist_6_max;
    uint16_t corr_cw_hist_5_curr;
    uint16_t corr_cw_hist_5_avg;
    uint16_t corr_cw_hist_5_max;
    uint16_t corr_cw_hist_4_curr;
    uint16_t corr_cw_hist_4_avg;
    uint16_t corr_cw_hist_4_max;
    uint16_t corr_cw_hist_3_curr;
    uint16_t corr_cw_hist_3_avg;
    uint16_t corr_cw_hist_3_max;
    uint16_t corr_cw_hist_2_curr;
    uint16_t corr_cw_hist_2_avg;
    uint16_t corr_cw_hist_2_max;
    uint16_t corr_cw_hist_1_curr;
    uint16_t corr_cw_hist_1_avg;
    uint16_t corr_cw_hist_1_max;

    //block 7
    /** Min Symbol Error Rate */
    uint16_t ser_min;
    /** Corrected CW rate for N number of bits */
    uint16_t corr_cw_hist_15_min;
    uint16_t corr_cw_hist_14_min;
    uint16_t corr_cw_hist_13_min;
    uint16_t corr_cw_hist_12_min;
    uint16_t corr_cw_hist_11_min;
    uint16_t corr_cw_hist_10_min;
    uint16_t corr_cw_hist_9_min;
    uint16_t corr_cw_hist_8_min;
    uint16_t corr_cw_hist_7_min;
    uint16_t corr_cw_hist_6_min;
    uint16_t corr_cw_hist_5_min;
    uint16_t corr_cw_hist_4_min;
    uint16_t corr_cw_hist_3_min;
    uint16_t corr_cw_hist_2_min;
    uint16_t corr_cw_hist_1_min;

    //block 8
    /** CWs processed */
    uint16_t codewords_processed;
    /** Reserved/unused, we add this to make block8 an even number of uint32_t's */
    uint16_t reserved;

    /** Housekeeping state, let's us easily grab blocks out of sequence.
     * Please do NOT modify this.
     */
    uint32_t _state;
} odsp_fec_stats_cp_block_t;

/** Number of read blocks in odsp_fec_stats_cp_block_t */
#define ODSP_FEC_STATS_CP_BLOCKS 9

/**
 * Request that the FW populate the FEC stats copy buffer. This function is non-blocking and will not wait for the FW.
 *
 * The counters used by the FW are large enough to accumulate FEC stats for many days (~1yr), the interval they
 * are polled/cleared should not matter.
 *
 * Optionally stats can be cleared atomically after they are copied by the FW. If polling continuously
 * (ie interval_time=0) then no stats/events will be lost during the clear.
 *
 * stats structure will be initialized when calling this function. All values are initialized to the F16 equivalent of
 * NaN (see odsp_fec_stats_cp_block_t).
 *
 * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param clear_on_read  [I] - True to clear all stats counters in the FW after reading. Atomic.
 * @param stats          [IO] - The stats structure that will later be used by odsp_fec_stats_poller_get.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_stats_poller_request(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    bool clear_on_read,
    odsp_fec_stats_cp_block_t *stats);

/** Return type for odsp_fec_stats_poller_get.
 * Communicates whether we're waiting for the FW to perform some operation or not.
 */
typedef enum {
    /** Returned successfully, not waiting for the FW, same as ODSP_OK */
    ODSP_POLLER_OK = 0,
    /** Timed out waiting for the FW to complete the request. Keep calling the same method */
    ODSP_POLLER_WAITING = 1,
    /** API hit an error, you must re-request the stats from the FW. Same as ODSP_ERROR.
     * Note the documentation has a bug, this should be negative 1 (-1). */
    ODSP_POLLER_ERROR = -1,
} e_odsp_poller_status;

/**
 * Read FW stats copy buffer and populate the provided stats structure. This can be done all at once or in
 * blocks of somewhat-related stats.
 * Blocks are defined/mentioned in the odsp_fec_stats_cp_block_t definition, set each bit of blocks_to_read
 * that you wish to copy from the FW. Ex set bit n (1<<n) to populate block n.
 *
 * You must call odsp_fec_stats_poller_request prior to this function. This function will not block waiting
 * for the FW to populate the copy buffer, instead it will return ODSP_POLLER_WAITING while waiting for the
 * FW. Simply call this method again (with the same arguments) until the return status is ODSP_POLLER_OK.
 *
 * The stats structure used for odsp_fec_stats_poller_request MUST be the same structure used for this call!
 *
 * If this method returns ODSP_POLLER_ERROR, then the stats are either invalid (poll_count = 0) or an API
 * method was unable to copy the data from the copy buffer (register read error, etc.) In either case another
 * call MUST be made to odsp_fec_stats_poller_request prior to calling this method again.
 *
 * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param blocks_to_read [I] - Bitmask of each block to read. ie. set Bit 0 = 1 to read the first block.
 * @param stats          [O] - The stats structure copied from the FW.
 *
 * @return ODSP_POLLER_OK (success), ODSP_POLLER_WAITING (waiting on FW), ODSP_POLLER_ERROR (error/failure).
 *
 * @since 0.6.0.0
 *
 * @requires
 * stdlib for offsetof
 *
 * @example

        // Note that this is just an example of how to design a non-blocking FSM. If you're using 25MHz MDIO
        // or 1MHz I2C then the IO won't be a bottleneck and a complicated FSM is not required.

        // make sure the same stats data is used for BOTH the request and the get
        static odsp_fec_stats_cp_block_t stats;
        // incrementally grab each block
        static int block = 0;
        // track our VDM state
        static e_vdm_state state = VDM_START;

        // ... whatever other stuff ...

        switch(state) {
            case VDM_START:
                ODSP_NOTE("VDM request\n");
                // request stats from the FW, clearing right afterwards
                status |= odsp_fec_stats_poller_request(die, channel, intf, fec, true, &stats);
                // non-blocking, exit right away
                state += 1;
                // reset our block too
                block = 0;
                break;

            case VDM_POLL:
                // see if the FW has copied the stats, don't copy the buffer yet
                poll_status = odsp_fec_stats_poller_get(die, channel, intf, fec, 0, &stats);
                if(poll_status == ODSP_POLLER_ERROR) {
                    //some error, go back and try again
                    state = VDM_START;
                }
                else if(poll_status == ODSP_POLLER_WAITING) {
                    //stay here, FW isn't ready yet
                }
                else {
                    //FW has copied, move on
                    ODSP_NOTE("VDM captured\n");
                    state += 1;
                }
                break;

            case VDM_GET:
                // FW has finished copying the stats, now pull them out of the FW copy buffer one-by-one
                ODSP_NOTE("VDM get block %d\n", block);
                poll_status = odsp_fec_stats_poller_get(die, channel, intf, fec, 1<<block, &stats);
                if(poll_status == ODSP_POLLER_ERROR) {
                    //some error, go back and try again
                    state = VDM_START;
                }
                else if(poll_status == ODSP_POLLER_WAITING) {
                    //stay here, FW isn't ready yet
                    // (technically this can't happen as of 1.10)
                }
                else {
                    //block was grabbed, proceed to the next one
                    block += 1;
                    if(block >= ODSP_FEC_STATS_CP_BLOCKS) {
                        //done all the blocks, reset
                        block = 0;
                        state += 1;
                    }
                }
                break;

            case VDM_POST:
                // Normally you will want to populate the CMIS register space with the captured VDM
                // stats. In this example we're just going to print to the console log.
                // NOTE: There is no need to call odsp_fec_stats_poller_calc_ber in module FW; this is for debug only!
                {
                    odsp_fec_stats_poller_t avg_rates;
                    odsp_fec_stats_poller_t min_rates;
                    odsp_fec_stats_poller_t max_rates;
                    odsp_fec_stats_poller_t cur_rates;
                    status |= odsp_fec_stats_poller_calc_ber(die, channel, intf, fec, &stats, &avg_rates, &min_rates, &max_rates, &cur_rates);

                    // this one is a "helper" stat which can be used to get the total number of processed codewords
                    ODSP_PRINTF( "Total CWs processed  : %7.2e\n", cmis_f16_to_float(stats.codewords_processed));

                    ODSP_PRINTF("===> Average <========\n");
                    ODSP_PRINTF( "  ber  : %7.2e\n", avg_rates.ber);
                    ODSP_PRINTF( "  ser  : %7.2e\n", avg_rates.ser);
                    ODSP_PRINTF( "  ferc : %7.2e\n", avg_rates.ferc);
                    ODSP_PRINTF( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        ODSP_PRINTF( "%d-%7.2e  ", i+1, avg_rates.corrected_ratio_hist[i]);
                    ODSP_PRINTF("\n");
                    ODSP_PRINTF("===> Min. <========\n");
                    ODSP_PRINTF( "  ber  : %7.2e\n", min_rates.ber);
                    ODSP_PRINTF( "  ser  : %7.2e\n", min_rates.ser);
                    ODSP_PRINTF( "  ferc : %7.2e\n", min_rates.ferc);
                    ODSP_PRINTF( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        ODSP_PRINTF( "%d-%7.2e  ", i+1, min_rates.corrected_ratio_hist[i]);
                    ODSP_PRINTF("\n");
                    ODSP_PRINTF("===> Max. <========\n");
                    ODSP_PRINTF( "  ber  : %7.2e\n", max_rates.ber);
                    ODSP_PRINTF( "  ser  : %7.2e\n", max_rates.ser);
                    ODSP_PRINTF( "  ferc : %7.2e\n", max_rates.ferc);
                    ODSP_PRINTF( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        ODSP_PRINTF( "%d-%7.2e  ", i+1, max_rates.corrected_ratio_hist[i]);
                    ODSP_PRINTF("\n");
                    ODSP_PRINTF("===> Current <========\n");
                    ODSP_PRINTF( "  ber  : %7.2e\n", cur_rates.ber);
                    ODSP_PRINTF( "  ser  : %7.2e\n", cur_rates.ser);
                    ODSP_PRINTF( "  ferc : %7.2e\n", cur_rates.ferc);
                    ODSP_PRINTF( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        ODSP_PRINTF( "%d-%7.2e  ", i+1, cur_rates.corrected_ratio_hist[i]);
                    ODSP_PRINTF("\n");

                }
                state += 1;
                break;

            default:
                return ODSP_ERROR;
        }
 */
e_odsp_poller_status odsp_fec_stats_poller_get(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    uint32_t blocks_to_read,
    odsp_fec_stats_cp_block_t *stats);

/**
 * Clear the internal FW FEC stats counters. Clearing the FW stats counters does not
 * affect the currently accumulating HW stats counters; these will be read the next time the accumulation_time
 * expires.
 *
 * The counters used by the FW are large enough to accumulate FEC stats for many days (~1yr), the interval they
 * are polled/cleared should not matter.
 *
 * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_stats_poller_clear(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf);

//floating point is needed for the FEC stats
#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)
#if defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)

/**
 * @h3 FEC/PCS status & statistics
 *
 * @brief
 * FEC/PCS status structure of a device
 */
typedef struct
{
    /**
     * FEC/PCS interface, 32-Egress, 64-Ingress
     */
    e_odsp_intf intf;

    /** Array of stream channels, format as bit mask bit position = channel. mask = 0 for unavailable stream */
    uint16_t stream_chans[ODSP_MAX_FEC_STREAMS];

    /** Nominal data rate of a stream (bundle) through the FEC (100/200/etc, not exact rates) */
    e_odsp_nom_data_rate nom_data_rate[ODSP_MAX_FEC_STREAMS];

    /** stream slice mapping. Get the HW fec slice mask at a stream index, bit 0 for slice 0, bit 1 for slice 1, .. bit7 for slice 7.
     *  then use the slice to lookup lanes and FECLs.
     * To identify FECLs of a stream:
     * - In 100G/200G/400G/800G FEC stream, each fec slice have 4 hw fec lanes: lane = slice*4, slice*4 +1, slice*4 +2, slice*4 + 3.
     * - In 50G FEC stream, its fec slice has 2 fec lanes: lane = slice*4, slice*4 +1
     * - In 25G FEC stream, its fec slice, the slice has 1 fec lanes: lane = slice*4
     * then use lane to lookup in the lane_id[lane] to get corresponding FECL
     * value 0x00 indicates this stream has not HW FEC slice mapped */
    uint8_t stream_slices[ODSP_MAX_FEC_STREAMS];
    
    /** HW FEC LANE - channel mapping. each HW FEC lane instance is an index, channel value is at the index.
     * Use the HW FEC lane instance to lookup lane_id (FECL), users will have the channel and FECL relation.
     */
    uint32_t hw_fec_lane_channel[ODSP_MAX_FEC_LANES];
    
    /**
     * Set when the PCS/AM/FEC is locked and ready to pass traffic
     */
    bool fw_lock[ODSP_MAX_FEC_STREAMS];

    /**
     * Number of times this interface has had to reacquire lock
     */
    uint8_t fw_reset_count[ODSP_MAX_FEC_STREAMS];

    /**
     * Goes High when 64B/66B block lock has been achieved on every
     * valid lane. Each index is HW FEC lane instance. Each element is a detected FECL.
     */
    uint8_t lane_id[ODSP_MAX_FEC_LANES];

    /**
     * High when the specified lane is AM locked.
     * Each index is HW FEC lane instance. Each element is AM lock status of FECL indicated in the lane_id[].
     */
    bool am_lock[ODSP_MAX_FEC_LANES];

    /**
     * Unlatched source of fp_align_status interrupt status
     */
    bool align_status[ODSP_MAX_FEC_STREAMS];

} odsp_fec_pcs_status_t;

/**
 * This method may be called to trigger a capture of the current FEC statistics
 * of a fec stream identify by the channel.
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param channel    [I] - The channel being accessed.
 * @param intf       [I] - The interface being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_snapshot_intf(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf);

/**
 * This method may be called to query the current FEC status
 * of the device.
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param intf       [I] - The interface being accessed.
 * @param fec_status [O] - Pointer to the FEC status.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_status_query(
    uint32_t die,
    e_odsp_intf intf,
    odsp_fec_pcs_status_t* fec_status);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method may be called to print the current FEC status
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param fec_status  [I] - Pointer to the FEC status.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_status_print(
    uint32_t die,
    e_odsp_intf intf,
    odsp_fec_pcs_status_t* fec_status);

/**
 * This method may be called to query then print the current FEC status
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_status_query_dump(
    uint32_t die,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/** FEC status per stream identifying by the input channel */
typedef struct
{
    /**
     * FEC/PCS interface, 32-Egress, 64-Ingress
     */
    e_odsp_intf intf;

    /**
     * Set when the PCS/AM/FEC is locked and ready to pass traffic
     */
    bool fw_lock;

    /**
     * Number of times this interface has had to reacquire lock
     */
    uint8_t fw_reset_count;

    /**
     * High when the specified lane is AM locked corresponding to fecl[].
     */
    bool am_lock[ODSP_MAX_FEC_LANES];

    /** number of FECLs */
    uint8_t num_fecls;

    /** mask of related channels belong this stream which the channel is present */
    uint16_t channel_mask;

    /**
     * Unlatched source of fp_align_status interrupt
     */
    bool align_status;

} odsp_fec_pcs_bundle_status_t;

/**
 * This method may be called to query the current FEC status
 * of a stream identify by the channel.
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param channel    [I] - The channel being accessed.
 * @param intf       [I] - The interface being accessed.
 * @param fec_status [O] - Pointer to the FEC status.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_bundle_status_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_pcs_bundle_status_t* fec_status);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

odsp_status_t odsp_fec_pcs_bundle_status_print(uint32_t die, uint32_t channel, e_odsp_intf intf,odsp_fec_pcs_bundle_status_t* fec_pcs_status);
/**
 * This method may be called to query then print the current FEC status
 * of a stream.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param channel     [I] - The channel being accessed.
 * @param intf        [I] - The interface being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_bundle_status_query_dump(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf);

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

/**
 * FEC/PCS stats
 *
 * @requires
 * ODSP_HAS_FLOATING_POINT=1
 */
typedef struct
{
    /**
     * FEC/PCS interface: 32-Egress, 64-Ingress
     */
    e_odsp_intf intf;

    /**
     * Selects whether the input format is PCS or RS-FEC: 0-FEC, 1-PCS
     */
    uint8_t pcs_format;

    /**
     * stream index: 0, .., 7
     * */
    uint8_t stream;

    /**
     * number of symbol per codeword: KP: 544, KR: 528
     */
    uint32_t num_symbol_per_cw;

    /**
     * Describes the interface nominal data rate (not exact bits/second).
     */
    e_odsp_nom_data_rate nom_data_rate;

    /**
     *  Number of symbol errors of FECLs. index by FECL value.
     */
    uint32_t num_symbol_errors[ODSP_MAX_FEC_LANES];

    /**
     * Number of corrected codewords
     */
    uint32_t codewords_corrected;

    /**
     * Number of uncorrected codewords
     */
    uint32_t codewords_uncorrected;

    /**
     * Number of processed codewords
     */
    uint32_t codewords_processed;

    /**
     * Number of corrected codewords with 1-15 errors, ie:
     *
     * num_corrected_cw_histogram[n] = number of corrected codewords with n error(s)
     */
    uint32_t num_corrected_cw_histogram[16];

    /**
     * Number of ones corrected to zeroes
     */
    uint32_t num_ones_to_zeroes;

    /**
     * Number of zeroes corrected to ones
     */
    uint32_t num_zeroes_to_ones;

    /**
     * BIP error count.  Only applies to PCS mode.
     * Only valid for formats which have a BIP to monitor.
     * -1 when this should be ignored.
     */
    int32_t bip_error_count[ODSP_MAX_FEC_LANES];

    /**
     * Skew indicator.  Applies to PCS and FEC modes.
     * Only valid when RSDEC_ALIGN_INT/RSDEC_ALIGN_INTS is High (assuming the interrupt bit has not yet been cleared).
     * Higher numbers are later lanes and lower number are earlier lanes. Maximum static skew between lanes is
     * based off the largest value minus the smallest value.  For FEC input multiply this difference by 80 to
     * get the static skew in bits; divide that by 26.5625 for RS(544,514) or 25.78125 for RS(528,514) to get
     * the static skew in ns.  For PCS input multiply the difference by 66 to get the static skew in bits;
     * to get the static skew in ns divide that by 5.15625 for 100G or 12.890625 for 50G.
     * Each element is index by a FECL
     */
    int8_t skew[ODSP_MAX_FEC_LANES];

    /** number of FECLs */
    uint8_t num_fecls;

    // Calculated Members

    /**
     * Sum of all num_symbol_errors bins
     */
    uint64_t num_symbol_errors_total;

    /**
     * Equal to num_ones_to_zeroes + num_zeroes_to_ones
     */
    uint64_t num_bit_errors;

    /**
     * SERi = num_symbol_errors / (544 * code_words_processed)
     */
    double sym_error_rate;

    /**
     * BERi = (num_ones_to_zeroes + num_zeroes_to_ones) / (5440 * code_words_processed)
     */
    double bit_error_rate;

    /**
     * Frame Error Rate = code_words_uncorrected / code_words_processed
     */
    double frame_error_rate;

    /** mask of related channels belong this stream which the channel is present */
    uint16_t channel_mask;
    /** Window error per physical lane. User can access the win_error base on the channel_mask */
    int32_t win_error[ODSP_MAX_CHANNELS+1];

} odsp_fec_pcs_stats_t;

/**
 * This method may be called to query the current FEC stats
 * from a FEC stream identified by the channel belonging to this stream.
 *
 * Either odsp_fec_pcs_snapshot_intf() must
 * be called to update the counters prior to querying the stats
 *
 * @param die        [I] - The ASIC die being accessed.
 * @param channel    [I] - The channel being accessed.
 * @param intf       [I] - The interface being accessed.
 * @param fec_stats  [O] - Pointer to the FEC stats.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_FLOATING_POINT=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_stats_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_pcs_stats_t* fec_stats);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

odsp_status_t odsp_fec_pcs_stats_print(uint32_t die, uint32_t channel, e_odsp_intf intf, odsp_fec_pcs_stats_t* fec_pcs_stats);
/**
 * This method may be called to print the current FEC status
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param fec_status  [I] - Pointer to the FEC status.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_status_print(
    uint32_t die,
    e_odsp_intf intf,
    odsp_fec_pcs_status_t* fec_status);

/**
 * This method may be called to query then print the current FEC status
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_status_query_dump(
    uint32_t die,
    e_odsp_intf intf);

/**
 * This method may be called to print the current FEC stats
 * from a stream.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param channel     [I] - The channel being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param fec_stats   [I] - Pointer to the link stats.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 * ODSP_HAS_FLOATING_POINT=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_stats_print(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_pcs_stats_t* fec_stats);

/**
 * This method may be called to print the current pam symbol window errors
 * from a fec stream identified by the channel.
 *
 * * WARNING: This API method should not be made available to customers since
 * there are complexities associated with properly reading and accumulating
 * these histogram statistics.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param channel     [I] - The channel being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param fec_stats   [I] - Pointer to the link stats.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 * ODSP_HAS_FLOATING_POINT=1
 *
 * @since 0.6.0.0
 *
 * @private
 */
odsp_status_t odsp_fec_pcs_stats_fecl_print(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_pcs_stats_t* fec_pcs_stats);

/**
 * This method may be called to query then print the current FEC stats
 * from a stream.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param channel     [I] - The channel being accessed.
 * @param intf        [I] - The interface being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @requires
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 * ODSP_HAS_FLOATING_POINT=1
 *
 * @since 0.6.0.0
 */
odsp_status_t odsp_fec_pcs_stats_query_dump(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
#endif // defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)
#endif // defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)

#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)
#if defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)

#if !defined(ODSP_HAS_RATE_T)
//fallback in case someone doesn't update their odsp_config.h
typedef double odsp_rate_t;
#endif

/**
 * Structure to hold the accum/min/max rates.
 *
 * @{pre,10 bits / symbol
 * 544 symbols / cw (KP)
 * 528 symbols / cw (KR)}
 *
 * @{note, Use the typedef odsp_rate_t in odsp_config.h to change the type from double (default) to another type (like float)}
 *
 * @requires
 * ODSP_HAS_FLOATING_POINT=1
 */
typedef struct {
    /**
     * Symbol error rate into the FEC.
     *
     * SERi = num_symbol_errors_total / (544 * code_words_processed)
     */
    odsp_rate_t ser;
    /**
     * Bit error rate into the FEC.
     *
     * BERi = (num_ones_to_zeroes + num_zeroes_to_ones) / (5440 * code_words_processed)
     */
    odsp_rate_t ber;
    /**
     * Frame error count, not rate
     */
    odsp_rate_t ferc;
    /**
     * Corrected codeword rate = codewords_corrected[n] / codewords_processed
     * Where n is the number of errors -1.
     * ie. codewords_corrected[3-1] is the number of CW that had 3 corrected errors, so
     * corrected_ratio_hist[3-1] is the rate for 3 corrected errors per CW.
     */
    odsp_rate_t corrected_ratio_hist[15];
} odsp_fec_stats_poller_t;

/**
 * Uses the stats returned by the FW to calculate the rates for min/max/accumulated/current. This is simply
 * converting the CMIS F16 format to an ordinary float/double.
 *
 * @param die            [I] - The ASIC die being accessed.
 * @param channel        [I] - The channel being accessed.
 * @param intf           [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param stats          [I] - The stats structure copied from the FW.
 * @param avg_rates      [O] - The output average/accumulated rates. Can be NULL to ignore.
 * @param min_rates      [O] - The output minimum rates. Can be NULL to ignore.
 * @param max_rates      [O] - The output maximum rates. Can be NULL to ignore.
 * @param cur_rates      [O] - The output current rates. Can be NULL to ignore.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 *
 * @requires
 * ODSP_HAS_FLOATING_POINT=1
 */
odsp_status_t odsp_fec_stats_poller_calc_ber(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_fec_stats_cp_block_t *stats,
    odsp_fec_stats_poller_t *avg_rates,
    odsp_fec_stats_poller_t *min_rates,
    odsp_fec_stats_poller_t *max_rates,
    odsp_fec_stats_poller_t *cur_rates);

/**
 * Convert a CMIS F16 to a floating point value.
 *
 * @param value [I] - Input value in CMIS F16 format
 *
 * @return odsp_rate_t floating point value.
 *
 * @since 1.1.0
 *
 * @requires
 * - ODSP_HAS_FLOATING_POINT=1
 * - ODSP_HAS_MATH_DOT_H=1
 */
odsp_rate_t cmis_f16_to_float(uint16_t value);
#endif // defined(ODSP_HAS_MATH_DOT_H) && (ODSP_HAS_MATH_DOT_H == 1)
#endif // defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)

/**
 * Generate default FEC rules relying on input bundle rules
 *
 * @param die           [I] - The ASIC die being accessed.
 * @param p_rules       [I/O] - The bundle rules being used to configure the FEC processor.
 * @param fec_mode      [I] - The fec mode set for the interface (IG/EG)
 * @param intf          [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param enable         [I] - Enable/disable FEC rules.
 *
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_fec_rules_default_set(
    uint32_t                die,
    odsp_rules_t            *p_rules,
    e_odsp_intf             intf,
    e_odsp_fec_mode         fec_mode,
    bool                    enable);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * This method is to print FEC rules at an interface
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param intf        [I] - The interface being accessed.
 * @param p_rules  [I] - Pointer to the FEC status.
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
void odsp_bundle_fec_rules_show(
    uint32_t die,
    odsp_fec_rules_t *p_rules,
    e_odsp_intf intf);

/**
 * This method is to print RX channels for a FEC stream at an interface
 * of the device.
 *
 * @param die         [I] - The ASIC die being accessed.
 * @param chans_mask  [I] - The channel mask indicates channels being accessed.
 * @param intf        [I] - The interface being accessed.
 * ODSP_HAS_DIAGNOSTIC_DUMPS=1
 *
 * @since 0.6.0.0
 */
void odsp_dump_fec_stream_chans(
    uint32_t die,
    uint16_t chans_mask,
    e_odsp_intf intf);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_FEC_H__ */
/** @file odsp_sfec.h
 ******************************************************************************
 * @brief
 *  Spica5 SFEC API definition and prototypes
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2023 Marvell Corp. All rights reserved.
 ******************************************************************************/

#ifndef __ODSP_SFEC_H__
#define __ODSP_SFEC_H__

#ifdef __cplusplus
extern "C" {
#endif

/** Number of symbols in PAM4 encoder/decoder */
#define ODSP_MAX_FECL_PER_100G_LANE 4
/** Number of 25G FEC lanes in a 50G physical lane */
#define ODSP_MAX_FECL_PER_50G_LANE  2
/** Number of 25G FEC lanes in a 100G physical lane */
#define ODSP_MAX_PAM4_SYMBOLS 4

/**
 * @h2 SFEC Utilities
 * =======================================================
 *
 * @brief
 * The following enum used to indicate HW SFEC/SFEC+ state machine.
 *
 */
typedef enum
{
    ODSP_SFEC_HW_STATE_INIT = 0x0,
    ODSP_SFEC_HW_STATE_SEARCH = 0x1,
    ODSP_SFEC_HW_STATE_CONFIRM = 0x2,
    ODSP_SFEC_HW_STATE_MONITOR = 0x3
} e_odsp_sfec_framer_state;

/** Structure for capture status of SFEC/SFEC+.
 * It can be used for SFEC status or SFEC+ status */
typedef struct odsp_sfec_status_s
{
    /** SFEF mode */
    e_odsp_sfec_mode mode;
    /** aligned status. in 50G lane, 2 first indexes are used.
     * in 100G lane, 4 first indexes are used. */
    bool aligned[ODSP_MAX_FECL_PER_100G_LANE];

    /**HW state machine per FECL. It is valid for LRX intf. */
    e_odsp_sfec_framer_state hw_state[ODSP_MAX_FECL_PER_100G_LANE];

} odsp_sfec_status_t;

/** SFEC statistics structure */
typedef struct odsp_sfec_stats_s
{
    /** Number of process codewords */
    uint32_t processed_cws;
    /** Number of corrected codewords */
    uint32_t corrected_cws;
    /**24 bit counters of corrected pam4 symbols:
     * first index is pam4 symbol, value: 0,1,2,3.
     * second index is correction pattern, value: 0,1,2,3.
     * One pam4 symbol can be corrected into the other 3 pattern values.
     * Example: pam symbol = 0, then it can be corrected into 1,2 or 3;
     * then valid corrected_pam_symbols is corrected_pam_symbols[0][1],
     * corrected_pam_symbols[0][2], corrected_pam_symbols[0][3].
     * This field is valid in SFEC mode */
    uint32_t corrected_pam4_symbols[ODSP_MAX_PAM4_SYMBOLS][ODSP_MAX_PAM4_SYMBOLS];
} odsp_sfec_stats_t;

/** SFEC+ statistic structure */
typedef struct odsp_sfecplus_stats_s
{
    /** Number of process codewords */
    uint32_t processed_cws;
    /**32 bit counter of 1 bit corrected errors */
    uint32_t one_bit_corrected;
    /**32 bit counter of 2 bits corrected errors */
    uint32_t two_bits_corrected;
    /**32 bit counter of 3 bits corrected errors */
    uint32_t three_bits_corrected;
    /**32 bit counter of 4 bits corrected errors */
    uint32_t four_bits_corrected;
} odsp_sfec_plus_stats_t;

/**
 * Query SFEC mode to know it is either SFEC or SFEC+ which is running per physical lane.
 * If the current mode is not SFEC, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 * @param p_status    [O]  - The status of SFEC or SFEC+ which is running.
 *
 * @return fec mode defined in e_odsp_sfec_mode.
 *
 * @since 0.6.0.0
 *
 */
e_odsp_sfec_mode odsp_sfec_mode_get(uint32_t die, uint32_t channel, e_odsp_intf intf);

/**
 * Query status of SFEC which is running per physical lane.
 * If the current mode is not SFEC, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 * @param p_status    [O]  - The status of SFEC or SFEC+ which is running.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_status_query(uint32_t die, uint32_t channel, e_odsp_intf intf, odsp_sfec_status_t *p_status);

/**
 * Query status of SFEC+ which is running per physical lane.
 * If the current mode is not SFEC+, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_EG or ODSP_INTF_IG.
 * @param p_status    [O]  - The status of SFEC or SFEC+ which is running.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_plus_status_query(uint32_t die, uint32_t channel, e_odsp_intf intf, odsp_sfec_status_t *p_status);

/**
 * Query SFEC mode which is running per physical lane.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 *
 * @return e_odsp_sfec_mode.
 *
 * @since 0.6.0.0
 *
 */
e_odsp_sfec_mode odsp_sfec_mode_get(uint32_t die, uint32_t channel, e_odsp_intf intf);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1
/**
 * Print SFEC status which is running per physical lane.
 * If the current mode is not SFEC, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 * @param p_status    [I]  - The status of SFEC which is running.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_status_print(uint32_t die, uint32_t channel, e_odsp_intf intf, odsp_sfec_status_t *p_status);

/**
 * Query and print SFEC status which is running per physical lane.
 * If the current mode is not SFEC+, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_status_query_print(uint32_t die, uint32_t channel, e_odsp_intf intf);

/**
 * Print SFEC+ status which is running per physical lane.
 * If the current mode is not SFEC+, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 * @param p_status    [I]  - The status of SFEC+ which is running.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_plus_status_print(uint32_t die, uint32_t channel, e_odsp_intf intf, odsp_sfec_status_t *p_status);

/**
 * Query and Print SFEC+ which is running per physical lane.
 * If the current mode is not SFEC+, this function will return ODSP_ERROR.
 *
 * @param die          [I] - The ASIC die being accessed.
 * @param channel      [I] - The channel being accessed.
 * @param intf         [I] - The interface being accessed, must be ODSP_INTF_LRX or ODSP_INTF_LTX.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since 0.6.0.0
 *
 */
odsp_status_t odsp_sfec_plus_status_query_print(uint32_t die, uint32_t channel, e_odsp_intf intf);
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && ODSP_HAS_DIAGNOSTIC_DUMPS == 1

const char* odsp_dbg_translate_sfec_mode(e_odsp_sfec_mode sfec_mode);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_SFEC_H__ */

/** @file odsp_ib.h
 ******************************************************************************
 * @brief
 *  Spica5 IB API definition and prototypes
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2023 Marvell Corp. All rights reserved.
 ******************************************************************************/

#ifndef __ODSP_IB_H__
#define __ODSP_IB_H__

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_IB_H__ */

/** @file odsp_prbs.h
 ******************************************************************************
 * @brief
 *  Spica5 PRBS API definition and prototypes
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2023 Marvell Corp. All rights reserved.
 ******************************************************************************/

#ifndef __ODSP_PRBS_H__
#define __ODSP_PRBS_H__
 
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @h2 PRBS Generator/checker
 * =======================================================
 *
 * @brief
 * PRBS pattern types.
 */
typedef enum
{
    /** PRBS x^7+x^6+1 */
    ODSP_PRBS_PAT_PRBS7,
    /** PRBS x^9+x^5+1 */
    ODSP_PRBS_PAT_PRBS9_5,
    /** PRBS x^9+x^4+1 */
    ODSP_PRBS_PAT_PRBS9_4,
    /** PRBS x^11+x^9+1 */
    ODSP_PRBS_PAT_PRBS11,
    /** PRBS x^13+x^12+x^2+x+1 */
    ODSP_PRBS_PAT_PRBS13,
    /** PRBS x^15+x^4+1 */
    ODSP_PRBS_PAT_PRBS15,
    /** PRBS x^23+x^18+1 */
    ODSP_PRBS_PAT_PRBS23,
    /** PRBS x^31+x^28+1 */
    ODSP_PRBS_PAT_PRBS31,
    /** PRBS x^58+x^39+1*/
    ODSP_PRBS_PAT_PRBS58,
    /** PRBS x^16+x^5+x^3+x^2+1 */
    ODSP_PRBS_PAT_PRBS16,
    /** Invalid PRBS pattern */
    ODSP_PRBS_PAT_NONE
} e_odsp_prbs_pat;

/**
 * Tx PRBS pattern modes
 */
typedef enum
{
    /** PRBS pattern modes */
    ODSP_PRBS_PATTERN_PRBS,
    /** Programmable fixed pattern mode */
    ODSP_PRBS_PATTERN_FIXED,
    /** JP03B test pattern. IEEE 802.3bs Clause **********.2*/
    ODSP_PRBS_PATTERN_JP083B,
    /** Transmitter linearity test pattern. IEEE 802.3bs Clause **********.4 */
    ODSP_PRBS_PATTERN_LIN,
    /** CID jitter tolerance test pattern. OIF-CEI-3.1 Sections 2.1.1.1 and 2.5.1.1 */
    ODSP_PRBS_PATTERN_CJT,
    /** SSPRQ pattern, IEEE 802.3bs Clause 120.5.11.2.3 */
    ODSP_PRBS_PATTERN_SSPRQ,
    /** Arbitrary Pattern Generator with programmable length (up to 384 bits) and content */
    ODSP_PRBS_PATTERN_APG
} e_odsp_prbs_pat_mode;

/**
 * Line Rx PRBS checker/generator modes
 */
typedef enum
{
    /** PRBS mode for individual MSB/LSB bit streams in a PAM-4 symbol */
    ODSP_PRBS_MODE_MSB_LSB,
    /** PRBS mode for a combined PAM-4 symbol */
    ODSP_PRBS_MODE_COMBINED
} e_odsp_prbs_mode;

/**
 * Tx error injection patterns
 */
typedef enum
{
    /** Bit 0 (one MSB). 0x0000_0000_0000_0001 */
    ODSP_PRBS_ERR_PAT_BIT0,
    /** Bit 1 (one LSB). 0x0000_0000_0000_0002 */
    ODSP_PRBS_ERR_PAT_BIT1,
    /** Bits 0 and 1 (one PAM4 symbol). 0x0000_0000_0000_0003 */
    ODSP_PRBS_ERR_PAT_BIT01,
    /** All MSBs. 0x5555_5555_5555_5555 */
    ODSP_PRBS_ERR_PAT_MSBS,
    /** All LSBs. 0xAAAA_AAAA_AAAA_AAAA */
    ODSP_PRBS_ERR_PAT_LSBS,
    /** All bits. 0xFFFF_FFFF_FFFF_FFFF */
    ODSP_PRBS_ERR_PAT_ALL,
    /** One bit per word. The position shifts right each time an error is injected */
    ODSP_PRBS_ERR_PAT_WALK,
    /** One 2-bit PAM4 symbol per word. The position shift right two bits each time an error is injected */
    ODSP_PRBS_ERR_PAT_WALK3,
    /* Invalid mode */
    ODSP_PRBS_ERR_PAT_INVALID
} e_odsp_prbs_err_inj_pat;

/**
 * PRBS checker auto-polarity thresholds
 */
typedef enum
{
    /** More than 9 consecutive 64 bit words with one or more errors each */
    ODSP_PRBS_AUTO_POLARITY_9,
    /** More than 17 consecutive 64 bit words with one or more errors each */
    ODSP_PRBS_AUTO_POLARITY_17,
    /** More than 33  consecutive 64 bit words with one or more errors each */
    ODSP_PRBS_AUTO_POLARITY_33,
    /** More than 65 consecutive 64 bit words with one or more errors each */
    ODSP_PRBS_AUTO_POLARITY_65,

} e_odsp_rx_prbs_auto_pol_thresh;

/**
 * This structure is used to configure one
 * of the pattern generators on the device.
 */
typedef struct 
{
    /**
     * In combined mode this enables
     * the PRBS generator.
     *
     * In MSB/LSB mode this enables the MSB/LSB pattern
     * generation.
     */
    bool en;

    /**
     * If operating in MSB/LSB mode this enables
     * the MSB/LSB pattern. These will generally
     * be enabled at the same time but can be controlled
     * independently if required.
     */
    bool gen_en_lsb;

    /**
     * Setup the pattern generator to operate in
     * either MSB/LSB or Combined mode:
     */
    e_odsp_prbs_mode prbs_mode;

    /** The LSB PRBS pattern to transmit */
    e_odsp_prbs_pat prbs_pattern_lsb;

    /** The PRBS pattern to transmit */
    e_odsp_prbs_pat prbs_pattern;

    /** * Selects the type of test pattern that is generated. */
    e_odsp_prbs_pat_mode pattern_mode;

    /** Specifies the fixed pattern word value that the fixed pattern checker attempts to lock to. 
     *  Bit 63 is expected to be received first. The default corresponds to the JP03A pattern described 
     *  in IEEE 802.3bs Clause **********.1. */

    /** When set each PRBS bit is transmitted in one UI, i.e. NRZ mode.  
     *  When cleared two adjacent PRBS bits are transmitted in one UI, i.e. PAM4 mode.  
     *  Do not attempt to combine dual PRBS mode and NRZ mode. */
    bool nrz_mode;

    /** PRBS Seed (Even) Configuration:
     *
     *  This is the seed used when re-seeding (re-seeding is an optional feature).  The bit positions used for 
     *  each PRBS polynomial order are shown below.  Ensure at least one bit of the seed being used is High.
     *  The seed itself does not come out of the generator, instead the seed represents previously generated bits.  
     *  So the first bit of generator output is based off the seed bits, but does not equal the seed bits.  
     *  The seed is LSB first, meaning the LSB of the seed represents the oldest previously generated bit and 
     *  the MSB of the seed represents the newest previously generated bit.
     *  This even seed is applied when GEN_PRBS_SEED_CFG__reseed_evn is High in two cases:
     *  
     *  1) Rising edge of GEN_PRBS_SEED_CFG__reseed.
     *  2) GEN_CFG__prbs_mode changes and GEN_PRBS_SEED_CFG__reseed is High.
     *  
     *      PRBS order 7  use bits 57:51
     *      PRBS order 9  use bits 57:49
     *      PRBS order 11 use bits 57:47
     *      PRBS order 13 use bits 57:45
     *      PRBS order 15 use bits 57:43
     *      PRBS order 16 use bits 57:42
     *      PRBS order 23 use bits 57:35
     *      PRBS order 31 use bits 57:27
     *      PRBS order 58 use bits 57:0
     *  
     *  Fixed Pattern Value 0 Configuration:
     *
     *  One of the fixed pattern word values. Bit 63 of this pattern is transmitted first. The fixed pattern consists 
     *  of two 64-bit words, each repeated a configurable number of times. The default fixed pattern is the 
     *  JP03A pattern described in IEEE 802.3bs Clause **********.1. */
    uint16_t seed_evn_0;
    uint16_t seed_evn_1;
    uint16_t seed_evn_2;
    uint16_t seed_evn_3;

    /** PRBS Seed (Odd) Configuration:
     *
     *  This is the seed used when re-seeding (re-seeding is an optional feature).  The bit positions used for 
     *  each PRBS polynomial order are shown below.  Ensure at least one bit of the seed being used is High.
     *  The seed itself does not come out of the generator, instead the seed represents previously generated bits.  
     *  So the first bit of generator output is based off the seed bits, but does not equal the seed bits.  
     *  The seed is LSB first, meaning the LSB of the seed represents the oldest previously generated bit and 
     *  the MSB of the seed represents the newest previously generated bit.
     *  This odd seed is applied when GEN_PRBS_SEED_CFG__reseed_odd is High in two cases:
     *  
     *  1) Rising edge of GEN_PRBS_SEED_CFG__reseed.
     *  2) GEN_CFG__prbs_mode changes and GEN_PRBS_SEED_CFG__reseed is High.
     *  
     *  PRBS order 7  use bits 57:51
     *  PRBS order 9  use bits 57:49
     *  PRBS order 11 use bits 57:47
     *  PRBS order 13 use bits 57:45
     *  PRBS order 15 use bits 57:43
     *  PRBS order 16 use bits 57:42
     *  PRBS order 23 use bits 57:35
     *  PRBS order 31 use bits 57:27
     *  PRBS order 58 use bits 57:0
     *  
     *  Fixed Pattern Value 1 Configuration:
     *  One of the fixed pattern word values. Bit 63 of this pattern is transmitted first. The fixed pattern consists 
     *  of two 64-bit words, each repeated a configurable number of times. */
    uint16_t seed_odd_0;
    uint16_t seed_odd_1;
    uint16_t seed_odd_2;
    uint16_t seed_odd_3;

    /** Number of times to repeat the fixed0_pat pattern */
    uint8_t fixed0_pat_repeat;

    /** Number of times to repeat the fixed1_pat pattern */
    uint8_t fixed1_pat_repeat;

} odsp_prbs_gen_rules_t;

typedef struct
{
    /** Enable/disable error injector */
    bool enable;

    /** Number of 64-bit words without errors to insert between words with errors */
    uint8_t gap;

    /** Number of 64-bit words to inject errors on */
    uint8_t duration;

    /** The pattern to inject */
    e_odsp_prbs_err_inj_pat pattern;

}odsp_prbs_err_inj_rules_t;

/**
 * This method is used to set the PRBS generator rules to their default values.
 *
 * @param gen_rules [I/O] - The PRBS generator rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_prbs_gen_rules_default_set(
    odsp_prbs_gen_rules_t* gen_rules);

/**
 * This method is used to configure the PRBS generator.
 *
 * @{note,
 * You may want to squelch the transmitter before disabling the PRBS generator.
 * This will prevent the transmitter from emitting garbage to a downstream device}
 *
 * @param die         [I] - The physical ASIC die being accessed.
 * @param channel     [I] - The channel number.
 * @param intf        [I] - The interface, see e_odsp_intf enum.
 * @param gen_rules   [I] - The generator rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_prbs_gen_config(
    uint32_t      die, 
    uint32_t      channel, 
    e_odsp_intf  intf,
    odsp_prbs_gen_rules_t* gen_rules);

/**
 * This method is called to inject errors into the TX datapath.
 *
 * @param die      [I] - The die of the ASIC being accessed.
 * @param channel  [I] - The channel to inject errors on.
 * @param intf     [I] - The interface, see e_odsp_intf enum.
 * @param inj_rules[I] - The error injection rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @since ******* 
 */
odsp_status_t odsp_prbs_gen_error_inject(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_err_inj_rules_t* inj_rules);

/**
 * Rules specific to the serial PRBS checker
 */
typedef struct
{
    /** Error threshold of PRBS interrupt */
    uint8_t prbs_err_th_irq;

    /** enable PRBS auto lock mode */
    bool prbs_auto_lock;

    /** Threshold of number of errors for PRBS lock Note: Must be non-zero values */
    uint8_t prbs_err_th_lock;

    /** Threshold of number of cycles for PRBS lock */
    uint8_t prbs_cyc_th_lock;

} odsp_prbs_chk_advanced_rules_t;

/**
 * Rx PRBS checker rules
 */
typedef struct
{
    /** Enables the PRBS checker. */
    bool en;

    /** Enables separate PRBS pattern check on MSB and LSB bits (PAM4 MSB and LSB).
     *  In Combined mode, a single PRBS MSB stream will be checked */
    e_odsp_prbs_mode prbs_mode;

    /** Inverts the receive bit pattern ahead of the PRBS checker if auto polarity is not enabled. */
    bool prbs_inv;

    /** Selects the PRBS polynomial for LSB bits (PAM4 symbol LSB) when dual PRBS mode is enabled. */
    e_odsp_prbs_pat prbs_pattern_lsb;

    /** Selects the PRBS polynomial when not in dual PRBS mode or for MSB bits when in dual PRBS mode 
      * (PAM4 symbol MSB) */
    e_odsp_prbs_pat prbs_pattern;

    /** NRZ mode flag, true = NRZ */
    bool nrz_mode;

    /** Selects the type of test pattern that is generated. */
    e_odsp_prbs_pat_mode pattern_mode;

    /** Enables the auto polarity detection feature of the PRBS checker. After a consecutive number of 
      * errors (programmable by auto_polarity_thresh) the polarity is inverted. */
    bool auto_polarity_en;

    /** When out of sync this controls the threshold for toggling the receive data polarity in auto 
     *  polarity mode. Every 64 bits (a "word") is checked for an error and if a certain number of 
     *  consecutive words contain errors then the polarity is toggled. In NRZ mode a word is 64 bits
     *  and in PAM4 mode a word is 32 symbols, i.e. 64 bits too. Caution: the alignment of serial bits
     *  to words is effectively random so, for example, two errors 10 bits apart may look like one
     *  word with two errors or two consecutive words with one error each.
     *      Value     Symbol                              Description
     *      2'd0  AUTO_POLARITY_9  More than 9 consecutive 64 bit words with one or more errors each
     *      2'd1  AUTO_POLARITY_17  More than 17 consecutive 64 bit words with one or more errors each
     *      2'd2  AUTO_POLARITY_33  More than 33 consecutive 64 bit words with one or more errors each
     *      2'd3  AUTO_POLARITY_65  More than 65 consecutive 64 bit words with one or more errors each */
    e_odsp_rx_prbs_auto_pol_thresh auto_polarity_thresh;

    /** Specifies the fixed pattern word value that the fixed pattern checker attempts to lock to. 
     *  Bit 63 is expected to be received first. The default corresponds to the JP03A pattern described 
     *  in IEEE 802.3bs Clause **********.1. */
    uint16_t fixed_pat0;
    uint16_t fixed_pat1;
    uint16_t fixed_pat2;
    uint16_t fixed_pat3;

    /** Controls the number of bit errors in one parallel data bus sample that cause a transition to the 
     * out of sync state. If more errors are found in one cycle than this value the transition occurs. 
     * Applies to the PRBS and fixed pattern checker state machines. Note that the alignment of the 
     * received data to the 64-bit word boundaries is not predictable. So, for example, two adjacent 
     * errors in the incoming serial stream may show up in one 64-bit word (which will declare two mismatches 
     * in that word) or may show up in two consecutive 64-bit words (each of which will declare just one mismatch). */
    uint8_t oos_thresh;

} odsp_prbs_chk_rules_t;

/**
 * Host and Rx PRBS checker status
 */
typedef struct
{
    /** PRBS mode for individual MSB/LSB or combined bit streams */
    e_odsp_prbs_mode prbs_mode;

    /** PRBS lock status */
    bool prbs_lock;

    /** PRBS lock status (LSB) */
    bool prbs_lock_lsb;

    /** Fixed pattern sync */
    uint8_t prbs_fixed_pat_sync;

    /** Received PRBS pattern */
    e_odsp_prbs_pat prbs_pattern;

    /** Received PRBS pattern (LSB) */
    e_odsp_prbs_pat prbs_pattern_lsb;

    /** Flag to indicate if the prbs total bit counter has saturated */
    bool prbs_total_bit_count_saturated;

    /** PRBS bit error counter */
    uint32_t prbs_error_bit_count;

    /** PRBS errored bit counter (LSB) */
    uint32_t prbs_error_bit_count_lsb;

    /** PRBS total bit counter */
    uint64_t prbs_total_bit_count;

    /** Received PRBS invert status */
    uint8_t prbs_inv;

    /** Received PRBS invert status (LSB) */
    uint8_t prbs_inv_lsb;

} odsp_prbs_chk_status_t;
 

/**
 * This method is used to set the PRBS checker rules to their default values.
 *
 * @param chk_rules [I/O] - The PRBS checker rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_prbs_chk_rules_default_set(
    odsp_prbs_chk_rules_t* chk_rules);

/**
 * This method is used to configure the PRBS checker.
 *
 * @param die       [I] - The physical ASIC die being accessed.
 * @param channel   [I] - The channel number.
 * @param intf      [I] - The interface, see e_odsp_intf enum.
 * @param chk_rules [I] - The checker rules.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_prbs_chk_config(
    uint32_t      die, 
    uint32_t      channel, 
    e_odsp_intf  intf,
    odsp_prbs_chk_rules_t*  chk_rules);

/**
 * This method is used to determine whether the PRBS checker is already
 * enabled.
 *
 * @param die     [I] - The physical ASIC die being accessed.
 * @param channel [I] - The physical channel number.
 * @param intf    [I] - The interface, see e_odsp_intf enum.
 *
 * @return true if the checker is enabled, false if it's not
 *
 * @since ******* 
 */
bool odsp_prbs_chk_is_enabled(
    uint32_t      die, 
    uint32_t      channel,
    e_odsp_intf   intf);

/**
 * This method is used to get the PRBS checker status.
 *
 * @param die        [I] - The physical ASIC die being accessed.
 * @param channel    [I] - The channel number.
 * @param intf       [I] - The interface, see e_odsp_intf enum.
 * @param chk_status [O] - The checker status.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 */
odsp_status_t odsp_prbs_chk_status(
    uint32_t      die, 
    uint32_t      channel, 
    e_odsp_intf  intf,
    odsp_prbs_chk_status_t*  chk_status);

#if defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)
/**
 * This method figures out the BER based on the PRBS checker status.
 *
 * @param chk_status    [I] - The checker status.
 * @param ber           [O] - The BER (MSB or combined).
 * @param ber_lsb       [O] - The BER (LSB).
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since ******* 
 *
 * @requires
 * The API must be have floating point support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_FLOATING_POINT=1
 */
odsp_status_t odsp_prbs_chk_ber(
    odsp_prbs_chk_status_t *chk_status,
    double  *ber,
    double  *ber_lsb);
#endif //defined(ODSP_HAS_FLOATING_POINT) && (ODSP_HAS_FLOATING_POINT==1)

/**
 * This method is used to query the generator rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param p_rules       [O] - The generator rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_prbs_gen_rules_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_gen_rules_t* p_rules);
/**
 * This method is used to query the generator rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param p_rules       [O] - The checker rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_prbs_chk_rules_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_chk_rules_t* p_rules);
/**
 * This method is used to query the injection rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param p_rules       [O] - The injection rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_prbs_gen_error_inject_query(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_err_inj_rules_t* p_rules);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
/**
 * This method is used to print the PRBS checker status.
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param chk_status    [O] - The checker status
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_prbs_chk_status_print(
    uint32_t                die,
    uint32_t                channel,
    e_odsp_intf             intf,
    odsp_prbs_chk_status_t* chk_status);

/**
 * This method is used to print the PRBS checker status.
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_prbs_chk_status_query_print(
    uint32_t die, 
    uint32_t channel, 
    e_odsp_intf intf);

/**
 * This method is used to print the generator rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param p_rules       [I] - The generator rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_dbg_chn_prbs_gen_rules_print(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_gen_rules_t* p_rules);

/**
 * This method is used to print the checker rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param channel       [I] - The channel number.
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 * @param p_rules       [I] - The generator rules
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_dbg_chn_prbs_chk_rules_print(
    uint32_t die,
    uint32_t channel,
    e_odsp_intf intf,
    odsp_prbs_chk_rules_t* p_rules);

/**
 * This method is used to query then print out the generator rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param chn_mask      [I] - The channel mask indicates channels being accessed
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_dbg_prbs_gen_rules_print(
    uint32_t die, 
    uint32_t chn_mask, 
    e_odsp_intf intf);

/**
 * This method is used to query then print out the checker rules on LTX/HTX channel
 *
 * @param die           [I] - The physical ASIC die being accessed.
 * @param chn_mask      [I] - The channel mask indicates channels being accessed
 * @param intf          [I] - The interface, see e_odsp_intf enum.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 *
 * @since *******
 *
 * @requires
 * The API must be have diagnostic dump support. It must be
 * compiled with the following flag:
 * - ODSP_HAS_DIAGNOSTIC_DUMPS=1
 */
odsp_status_t odsp_dbg_prbs_chk_rules_print(
    uint32_t die, 
    uint32_t chn_mask, 
    e_odsp_intf intf);

#endif // defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_PRBS_H__ */
/** @file odsp_register_dump.h
 ******************************************************************************
 * @brief
 *  Spica5 diagnostic register dump definition and prototypes
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2023 Marvell Corp. All rights reserved.
 ******************************************************************************/

#ifndef __ODSP_REGISTER_DUMP_H__
#define __ODSP_REGISTER_DUMP_H__
 
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @h2 Register dump
 * =======================================================
 *
 * @brief
 * 
 * The implementation of the register dump method.
 * 
 * @param die      [I] - The ASIC die to dump
 * @param handle   [I] - The file handle to dump to (or NULL to print to stdout)
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */ 
odsp_status_t odsp_diags_register_dump_impl(
    uint32_t die, 
    void* handle);

/** 
 * Print the register dump.
 * 
 * @param die      [I] - The ASIC die to dump
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */ 
odsp_status_t odsp_diags_register_dump(uint32_t die);

#if defined(ODSP_HAS_FILESYSTEM) && ODSP_HAS_FILESYSTEM == 1

/** 
 * Print the register dump to file.
 * 
 * @param die      [I] - The ASIC die to dump
 * @param handle   [I] - The file handle to dump to
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */ 
odsp_status_t odsp_diags_register_dump_file(
    uint32_t die, 
    FILE* handle);

/** 
 * Print the register dump to a particular path.
 * 
 * @param die      [I] - The ASIC die to dump
 * @param path     [I] - The file path to dump to
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */ 
odsp_status_t odsp_diags_register_dump_path(
    uint32_t die, 
    const char* path);

#endif //defined(ODSP_HAS_FILESYSTEM) && ODSP_HAS_FILESYSTEM == 1

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_REGISTER_DUMP_H__ */
/** @file mcu_log.h
 ******************************************************************************
 * @brief
 *  MCU tiny log
 *
 ******************************************************************************
 * <AUTHOR>  This file contains information that is proprietary and confidential to
 *  Marvell Technology Inc.
 *
 *  This file can be used under the terms of the Marvell Software License
 *  Agreement. You should have received a copy of the license with this file,
 *  if not please contact your Marvell support staff.
 *
 *  Copyright (C) 2021 Marvell Technology Inc. All rights reserved.
 * 
 * @note It is not recommended to modify this file as it is generated from 
 *       template file
 ******************************************************************************/

#ifndef __MCU_LOG_H__
#define __MCU_LOG_H__
 
#ifdef __cplusplus
extern "C" {
#endif

#include "odsp_rtos.h"

#define TLOG_FE_PRINT_HOOK ODSP_NOTE

/**
 * @h2 MCU logging
 * =======================================================
 *
 * @brief MCU debug log callback
 */
typedef struct odsp_mcu_log_callback_s
{
    int (*text_print)(void *ctx, const char *text); /**< Called to print a text */
    void (*on_new_entry)(void *ctx, uint8_t *data, uint16_t len); /**< Called on every new log entry */
} odsp_mcu_log_callback_t;

/**
 * @brief Log pop options
 */
typedef enum odsp_mcu_log_pop_opts_e
{
    ODSP_MCU_LOG_POP_ONLY   = 0, /**< Just simply pop entries and notify,
                                     no render is done */
    ODSP_MCU_LOG_RENDER = 1 << 0,/**< Render entries while popping */
}odsp_mcu_log_pop_opts_t;

/**
 * MCU Logger object
 */
typedef struct
{
    uint8_t memory[144];
} odsp_mcu_log_t;

/**
 * MCU Logger object
 */
typedef struct
{
    uint8_t memory[512];
} odsp_mcu_log_render_t;

/**
 * Construct MCU log object
 *
 * @param logger      MCU logger object
 * @param die         The physical ASIC die being accessed.
 * @param buffer      Temporary buffer. Recommend 256 bytes.
 * @param buffer_size Temporary buffer size.
 *
 * @return MCU logger object on success or NULL on failure
 */
odsp_mcu_log_t *odsp_mcu_log(
    odsp_mcu_log_t *logger, uint32_t die, uint8_t *buffer, uint32_t buffer_size);

/**
 * Get versions of both API and FW layers
 *
 * @param logger     [I] - MCU logger object
 * @param api_ver    [O] - API logger version. Pass NULL to ignore.
 * @param fw_ver     [O] - FW logger version. Pass NULL to ignore.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_mcu_log_version(odsp_mcu_log_t *logger, uint32_t *api_ver, uint32_t *fw_ver);

/**
 * Show brief information about the logger back-end such as back-end addresses,
 * PIDX/CIDX, counters, ... No log messages are fetched.
 *
 * @param logger     [I] - MCU logger object
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_mcu_log_brief(odsp_mcu_log_t *logger);

/**
 * Get log dictionary CRC.
 *
 * @param logger     [I] - MCU logger object
 *
 * @return Log dictionary CRC
 */
uint32_t odsp_mcu_log_crc_get(odsp_mcu_log_t *logger);

/**
 * Query all log entries in readable format. All log entries are retrieved and
 * rendered to readable texts into the buffer. This API just read-only, so no
 * log entries are consumed (popped).
 *
 * @note The buffer should be >= 2048 bytes, otherwise, not all of log messages
 * can be fetched. Currently, there is no way to tell how large the buffer size
 * is to contain all messages. Log dictionary must be already setup by
 * odsp_mcu_log_ld_set or odsp_mcu_log_ld_file_set
 *
 * @param logger    [I] - MCU log object
 * @param buff      [I] - Buffer to store log messages (in readable format)
 * @param buff_size [I] - Buffer size.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure (e.g. log dictionary
 * not found, PIF access failures, logger back-end failure)
 *
 * @see odsp_mcu_log_pop
 * @see odsp_mcu_log_ld_set
 * @see odsp_mcu_log_ld_file_set
 */
odsp_status_t odsp_mcu_log_query(odsp_mcu_log_t *logger, char *buff, uint32_t buff_size);

/**
 * Enable/disable log rotation. Implementing log polling with the API
 * odsp_mcu_log_pop() requires log rotation be disabled to avoid conflict
 * between front-end and back-end (they both internally modify the CIDX)
 *
 * @note At the time log rotation is disabled, the FW may be in the middle of
 * producing/consuming the last log entry, so if start consuming log entries
 * immediately after disabling rotation can also cause CIDX conflict. So, it is
 * better to delay 100ms before polling to start.
 *
 * @param logger    [I] - MCU log object
 * @param enabled   [I] - true to enable and false to disable
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @see odsp_mcu_log_rotation_is_enabled
 */
odsp_status_t odsp_mcu_log_rotation_enable(
    odsp_mcu_log_t *logger,
    bool enabled);

/**
 * Check if log rotation is enabled.
 *
 * @param logger    [I] - MCU log object
 * @param enabled   [O] - true if enabled and false if disabled
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @see odsp_mcu_log_rotation_enable
 */
odsp_status_t odsp_mcu_log_rotation_is_enabled(
    odsp_mcu_log_t *logger,
    bool *enabled);

/**
 * Set f/w debug log filter. Log filter is used to restrict which log messages
 * get displayed in the firmware log
 *
 * @param logger [I] - MCU log object
 * @param filter [I] - The filter to use to filter log messages in the f/w log.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_filter_set(odsp_mcu_log_t *logger, uint32_t filter);

/**
 * Get f/w debug log filter. The filter is used to restrict which log messages
 * get displayed in the firmware log
 *
 * @param logger [I] - MCU log object
 *
 * @return The filter log message or 0xffffffff if it couldn't be read.
 */
uint32_t odsp_mcu_log_filter_get(odsp_mcu_log_t *logger);

/**
 * Consume (pop) log entries and display them. The input callback is
 * required to implement functions to return the path to log dictionary file
 * and how to print texts. For fast log collecting, log rendering may not be
 * required, the callback on_new_entry should be specified to copy new entries
 * into application data structures for off-line rendering later.
 *
 * @param logger    [I] - MCU logger object
 * @param opts      [I] - Pop options, whether entries are rendered to readable
 *                        texts while popping or not.
 * @param callback  [I] - Callback that implements functions to return the path
 *                        to log dictionary file, how to print texts and new log
 *                        entry notification.
 * @param ctx       [I] - Application context that will be input when callback
 *                        functions are called.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure (e.g. log dictionary
 * not found, PIF access failures, logger back-end failure)
 */
odsp_status_t odsp_mcu_log_pop(
    odsp_mcu_log_t *logger,
    odsp_mcu_log_pop_opts_t opts,
    const odsp_mcu_log_callback_t *callback,
    void *ctx);

/**
 * @private
 *
 * Ask the FW to flood the logger as many as it can for benchmark purpose
 *
 * @param logger    [I] - MCU logger object
 * @param flood     [I] - true/false to start/stop flooding
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_flood(
    odsp_mcu_log_t *logger,
    bool flood);

/**
 * Check if there are any data available at back-end
 *
 * @param logger      [I] - MCU logger object
 *
 * @retval Number of bytes available at back-end
 */
uint32_t odsp_mcu_log_nb_bytes_avail(odsp_mcu_log_t *logger);

/**
 * Clear all of log entries and back-end statistic counters
 *
 * @param logger     [I] - MCU logger object
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_clear(odsp_mcu_log_t *logger);

/**
 * Set in-memory log dictionary. This is helpful when log dictionary is not in
 * file system, but on memory of application.
 *
 * @param logger  [I] - MCU logger object
 * @param ld      [I] - Log dictionary content
 * @param ld_size [I] - Log dictionary size
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_ld_set(odsp_mcu_log_t *logger, uint8_t *ld, uint32_t ld_size);

/**
 * Get log dictionary content
 *
 * @param logger  [I] - MCU logger object
 * @param ld_size [O] - Log dictionary size
 *
 * @return Log dictionary content
 */
uint8_t *odsp_mcu_log_ld_get(odsp_mcu_log_t *logger, uint32_t *ld_size);

/**
 * Set log dictionary file.
 *
 * @param logger      [I] - MCU logger object
 * @param ld_filepath [I] - Log dictionary file. ODSP_HAS_FILESYSTEM needs to
 *                          be defined to have file system calls.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 *
 * @see odsp_mcu_log_ld_set
 */
odsp_status_t odsp_mcu_log_ld_file_set(odsp_mcu_log_t *logger, const char *ld_filepath);

/**
 * Get log dictionary file
 *
 * @param logger [I] - MCU logger object
 *
 * @return Log dictionary file
 */
const char *odsp_mcu_log_ld_file_get(odsp_mcu_log_t *logger);

/**
 * Construct log render to render log entries raw data into readable texts.
 *
 * @note Once finish rendering all of log entries, the log render need to be
 * cleanup by calling odsp_mcu_log_render_finish() API. All of opened files will
 * be closed
 *
 * @param render Render object to construct
 *
 * @return Log render on success or NULL on failure
 *
 * @see odsp_mcu_log_render_finish
 */
odsp_mcu_log_render_t *odsp_mcu_log_render(odsp_mcu_log_render_t *render);

/**
 * @private
 * Construct log render to render log entries raw data into readable texts and
 * put all texts into the buffer
 *
 * @param render    Render object to construct
 * @param buff      Buffer to contain all texts
 * @param buff_size Buffer size
 *
 * @return Log render on success or NULL on failure
 */
odsp_mcu_log_render_t *odsp_mcu_log_render_with_str_buffer(
    odsp_mcu_log_render_t *render,
    char *buff, uint32_t buff_size);

/**
 * Render a log entry to readable texts
 *
 * @param render Render object
 * @param entry_data Log entry raw data
 * @param len Log entry data length
 * @param callback Callback to print texts
 * @param callback_ctx Context to input when callback is called
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure (e.g. log dictionary
 * not found, ...)
 */
odsp_status_t odsp_mcu_log_render_entry(
    odsp_mcu_log_render_t *render, uint8_t *entry_data, uint16_t len,
    const odsp_mcu_log_callback_t *callback,
    void *callback_ctx);

/**
 * Render raw log entries stored in buffer to readable texts
 *
 * @param render Render object
 * @param buffer Buffer that stores raw log entries
 * @param len Length of buffer
 * @param callback Callback to print texts
 * @param callback_ctx Context to input when callback is called
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure (e.g. log dictionary
 * not found, ...)
 */
odsp_status_t odsp_mcu_log_render_buffer(
    odsp_mcu_log_render_t *render, uint8_t *buffer, uint16_t len,
    const odsp_mcu_log_callback_t *callback,
    void *callback_ctx);

/**
 * Set the log dictionary file object.
 *
 * @param render      [I] - Render object
 * @param ld_filepath [I] - Path to log dictionary file
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_render_ld_file_set(odsp_mcu_log_render_t *render, const char *ld_filepath);

/**
 * Get the log dictionary file object.
 *
 * @param render  [I] - Render object
 *
 * @return Path to log dictionary file
 */
const char *odsp_mcu_log_render_ld_file_get(odsp_mcu_log_render_t *render);

/**
 * Set the log dictionary content. This is helpful when log dictionary is
 * not on file system.
 *
 * @param render  [I] - Render object
 * @param ld      [I] - Log dictionary content
 * @param ld_size [I] - Log dictionary content size
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_render_ld_set(
    odsp_mcu_log_render_t *render, uint8_t *ld, uint32_t ld_size);

/**
 * Get the log dictionary content.
 *
 * @param render  [I] - Render object
 * @param ld_size [O] - Log dictionary content size
 *
 * @return Log dictionary content
 */
uint8_t *odsp_mcu_log_render_ld_get(odsp_mcu_log_render_t *render, uint32_t *ld_size);

/**
 * @private
 * Set render callback
 *
 * @param render       [I] - Render object
 * @param callback     [I] - Callback
 * @param callback_ctx [I] - Callback contentx
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t odsp_mcu_log_render_callback_set(
    odsp_mcu_log_render_t *render,
    const odsp_mcu_log_callback_t *callback,
    void *callback_ctx);

/**
 * To clean up log render, e.g. close files
 *
 * @param render Log render to cleanup
 */
void odsp_mcu_log_render_finish(odsp_mcu_log_render_t *render);

/**
 * Dump FW trace log to buffer for debugging
 * 
 * @param die       The physical ASIC die being accessed.
 * @param buff      Buffer to contain data
 * @param buff_size Buffer size
 */ 
odsp_status_t odsp_mcu_debug_log_query(uint32_t die, char* buff, uint32_t buff_size);

/**
 * Print the log binary buffer
 *
 * @param buffer Log binary buffer queried by odsp_mcu_debug_log_query() API
 * @param buffer_len Buffer len
 * @param tld_file Path to log dictionary (.tld)
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure.
 */
odsp_status_t odsp_mcu_debug_log_print_buffer(char *buffer, uint32_t buffer_len, char *log_dict);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __MCU_LOG_H__ */


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* __ODSP_API_H__ */
