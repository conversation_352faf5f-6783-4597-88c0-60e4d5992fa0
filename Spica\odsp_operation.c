/** @file evalboard.c
 *****************************************************************************
 *
 * @brief
 *      Test file used to run the examples against an XMLRPC server running in
 *      the Marvell Explorer GUI against an evaluation board.
 *
 *****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ****************************************************************************/

#include <stdlib.h>
#include <string.h>
#include <time.h>

#include "Drivers.h"
#include "odsp_types.h"
#include "odsp_api.h"
#include "Application.h"


// Counters for recording register accesses
//static int g_register_read_count  = 0;
//static int g_register_write_count = 0;

//#if defined ODSP_HAS_OLYMPUS
//#    include "olympus/inphi_olympus_register_client.h"
//#else
//#    include "xmlrpcpp/inphi_xmlrpc_register_client.h"
//#endif


//int inphi_register_write_count(void)
//{
//    int write_count = g_register_write_count;
//    g_register_write_count = 0;
//    return write_count;
//}

//int inphi_register_read_count(void)
//{
//    int read_count = g_register_read_count;
//    g_register_read_count = 0;
//    return read_count;
//}

//#if defined(ODSP_HAS_OLYMPUS)
//int odsp_dbg_set_remote_server(const char* ip, int port)
//{
//    (void)ip;
//    (void)port;
//    return inphi_olympus_open(0x591);
//}

//void odsp_dbg_close(void)
//{
//    inphi_olympus_close();
//}


//int odsp_reg_get(uint32_t die, uint32_t addr, uint16_t* data)
//{
//    uint64_t tmp;
//    int status;

//    g_register_read_count++;

//    status = inphi_olympus_reg_get(die, addr, &tmp);
//    *data = (uint32_t)tmp;

//#if defined(ODSP_HAS_REGISTER_LOGGING)
//    ODSP_PRINTF("  > odsp_reg_get(die=%x, addr=%06x, data=%08x);\n", die, addr, *data);
//#endif
//    return status;
//}

//int odsp_reg_set(uint32_t die, uint32_t addr, uint32_t data)
//{
//    g_register_write_count++;

//#if defined(ODSP_HAS_REGISTER_LOGGING)
//    ODSP_PRINTF("  > odsp_reg_set(die=%x, addr=%06x, data=%08x);\n", die, addr, data);
//#endif
//    return inphi_olympus_reg_set(die, addr, data);
//}


//#else // XML-RPC

//int odsp_dbg_set_remote_server(const char* ip, int port)
//{
//    return inphi_xmlrpc_open(ip, port);
//}

//void odsp_dbg_close(void)
//{
//    inphi_xmlrpc_close();
//}

//int odsp_reg_get(uint32_t die, uint32_t addr, uint32_t* data)
//{
//    uint64_t tmp;
//    int status;

//    g_register_read_count++;

//    status = inphi_xmlrpc_reg_get(die, addr, &tmp);
//    *data = (uint32_t)tmp;

//#if defined(ODSP_HAS_REGISTER_LOGGING)
//    ODSP_PRINTF("  > odsp_reg_get(die=%x, addr=%06x, data=%08x);\n", die, addr, *data);
//#endif
//    return status;
//}

//int odsp_reg_set(uint32_t die, uint32_t addr, uint32_t data)
//{
//    g_register_write_count++;

//#if defined(ODSP_HAS_REGISTER_LOGGING)
//    ODSP_PRINTF("  > odsp_reg_set(die=%x, addr=%06x, data=%08x);\n", die, addr, data);
//#endif
//    return inphi_xmlrpc_reg_set(die, addr, data);
//}

//#endif // ODSP_HAS_XMLRPC

odsp_status_t odsp_reg_get(uint32_t die, uint32_t addr, uint16_t* data)
{
	inphi_status_t status= ODSP_OK;
//	uint8_t mdio_port = die & 0xf;
//	uint16_t asic_instance = die >>8;
	
//	uint8_t phyad = (uint8_t)(die & 0xf);
	
	uint8_t devad = addr >> 16;
	
	uint32_t mdio_reg_addr = addr;
	
	status |= mdio_read(devad, mdio_reg_addr, data);
	
//  switch(die)
//	{
//		case 0:
//			status |= mdio_read(devad, mdio_reg_addr, data);
//		  break;
//		case 1:
//			status |= mdio2_read(devad, mdio_reg_addr, data);
//		  break;
//	}

//	if((!mdio_read(devad, mdio_reg_addr, data)))
//	{
//		return INPHI_ERROR;
//	}
	return ODSP_OK;
}




odsp_status_t odsp_reg_set(uint32_t die, uint32_t addr, uint16_t data)
{
	inphi_status_t status= ODSP_OK;
//	uint8_t phyad = die & 0xf;
//	uint16_t asic_instance = die >>8;
	
	uint8_t devad = addr >> 16;
	
	uint32_t mdio_reg_addr = addr;
	
	status |= mdio_write( devad, mdio_reg_addr, data);
	
//	switch(die)
//	{
//		case 0:
//			status |= mdio_write( devad, mdio_reg_addr, data);
//		  break;
//		case 1:
//			status |= mdio2_write( devad, mdio_reg_addr, data);
//		  break;
//	}
	
//	if(!mdio_write( devad, mdio_reg_addr, data))
//	{
//		return INPHI_ERROR;
//	}

	return ODSP_OK;
}




/**
 * The following example describes the process of accessing
 * the ASIC registers.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_program_firmware(uint32_t die)
{
   odsp_status_t status = ODSP_OK;

    // Download the firmware bundled with the API to the ASIC
    // and then jump to the new application image. This method
    // will download the microcode to all parts in the ASIC
    // and verify that the firmware is valid.

#if defined(ODSP_HAS_INLINE_APP_FW) && (ODSP_HAS_INLINE_APP_FW == 1)
    status |= odsp_mcu_download_firmware(die, false);
#endif //defined(ODSP_HAS_INLINE_APP_FW) && (ODSP_HAS_INLINE_APP_FW == 1)

    if(ODSP_OK != status)
    {
        printf("Failed downloading the firmware to the ASIC\n");
    }
    
    return status;

}


//static odsp_status_t init_1x100g_mission(uint32_t die, uint32_t chn_id);


/* This helper function will create 1 bundle in Mission mode:
 *     - Host 1x53.125Gbps PAM4 <=> Line 1x53.125Gbps PAM4
 *     - FEC disabled
 *     - chn_id: host channel equal to line channel
 *
 * Return ODSP_OK on success, ODSP_ERROR on failure
 */
static odsp_status_t init_1x100g_mission(uint32_t die, uint32_t chn_id)
{
    odsp_rules_t rules;
    e_odsp_operational_mode operational_mode = ODSP_MODE_MISSION;
    e_odsp_protocol_mode    protocol_mode    = ODSP_PROT_MODE_50G_1Px26p6_TO_1Px26p6;//ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
    e_odsp_fec_mode         fec_mode         = ODSP_FEC_MODE_BYPASS;
    e_odsp_intf             intf             = ODSP_INTF_HOST | ODSP_INTF_LINE;
    odsp_status_t status = ODSP_OK;

    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
     * - Signaling: PAM
     * - Baudrate: 53.125Gbd
     * - FEC disabled
     *  ...
     */
    status |= odsp_bundle_rules_default_set(die,
            chn_id, intf, operational_mode, protocol_mode, fec_mode, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: Build default bundle rules failed\n");
        return status;
    }
		
		 // Configure Line TX rules
    rules.ltx[chn_id].channel_enable  = true;

    // Customize XBAR rules if necessary
    rules.ltx_clk_xbar[chn_id] = chn_id;
    rules.htx_clk_xbar[chn_id] = chn_id;

    // Apply rules to start bundle
    status |= odsp_bundle_enter_operational_state(die, chn_id, intf, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
        return status;
    }

    return status;
}


odsp_status_t example_init_8bundles_8x100g_mission(uint32_t die)
{
    odsp_status_t status = ODSP_OK;

//    status |= init_1x100g_mission(die, 1);
//    status |= init_1x100g_mission(die, 2);
//    status |= init_1x100g_mission(die, 3);
//    status |= init_1x100g_mission(die, 4);
//    status |= init_1x100g_mission(die, 5);
//    status |= init_1x100g_mission(die, 6);
//    status |= init_1x100g_mission(die, 7);
//    status |= init_1x100g_mission(die, 8);

//#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
//    // Query then show all bundles
//    odsp_bundles_info_query_dump(die);
//#endif

//    return status;
	    for (uint32_t channel = 1; channel <= 8; channel++)
        {
					status |= init_1x100g_mission(die, channel);
				 printf("channel%d:%d\r\n",channel, status);
				}
    return status;
}


/////////////////////////////////SFEC/////////////////////////////////////////////////////////////
static bool _is_channel_inverted(uint32_t channel)
{
    if (channel > 4)
        return true;
    return false;
}

/* 
 * This helper function will create 1 bundle 100G Mission mode for 100G KP1 FEC + line SFEC:
 *    - Host 1x53.125Gbps PAM4 <=> Line 1x55.781250Gbps PAM4.
 *    - ODSP_FEC_MODE_BYPASS, FEC monitor is disabled
 * Return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t init_1x100g_kp1_mission_sfec(uint32_t die, uint32_t channel)
{
    odsp_rules_t rules;
    e_odsp_operational_mode operational_mode = ODSP_MODE_LINE_PCS;//ODSP_MODE_LINE_PCS;//ODSP_MODE_MISSION;//ODSP_MODE_DUAL_PRBS;//
    e_odsp_protocol_mode    protocol_mode    = ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;//ODSP_PROT_MODE_50G_1Px26p6_TO_1Px26p6;//
    e_odsp_fec_mode         fec_mode         = ODSP_FEC_MODE_BYPASS;
    e_odsp_intf             intf             = ODSP_INTF_LINE;//ODSP_INTF_HOST | ODSP_INTF_LINE;//
    odsp_status_t status = ODSP_OK;

    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
     * - Signaling: PAM
     * - Baudrate: 53.125Gbd
     * - FEC disabled
     *  ...
     */
    status |= odsp_bundle_rules_default_set(die,
            channel, intf, operational_mode, protocol_mode, fec_mode, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: Build default bundle rules failed\n");
        return status;
    }

    /* Customize channel setting
     */
    
    // Configure Line TX rules
    rules.ltx[channel].channel_enable  = true;
    rules.ltx[channel].squelch_lock    = false;
    rules.ltx[channel].lut_mode        = ODSP_TX_LUT_7TAP_LIN;//ODSP_TX_LUT_4TAP;//
    rules.ltx[channel].ieee_demap      = true;
    rules.ltx[channel].gray_mapping    = true;
    rules.ltx[channel].invert_chan     = false;
    rules.ltx[channel].fir_taps[0]     = -50;
    rules.ltx[channel].fir_taps[1]     = 850;
    rules.ltx[channel].fir_taps[2]     = 50;
    rules.ltx[channel].fir_taps[3]     = 0;
    rules.ltx[channel].fir_taps[4]     = 0;
    rules.ltx[channel].fir_taps[5]     = 0;
    rules.ltx[channel].fir_taps[6]     = 0;
    rules.ltx[channel].inner_eye1      = 700;
    rules.ltx[channel].inner_eye2      = 1600;

//    // Configure Host TX rules_1
//    rules.htx[channel].channel_enable  = true;
//    rules.htx[channel].squelch_lock    = false;
//    rules.htx[channel].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[channel].ieee_demap      = true;
//    rules.htx[channel].gray_mapping    = true;
//    rules.htx[channel].invert_chan     = false;
//    rules.htx[channel].fir_taps[0]     = -50;
//    rules.htx[channel].fir_taps[1]     = 650;
//    rules.htx[channel].fir_taps[2]     = 50;
//    rules.htx[channel].fir_taps[3]     = 0;
//    rules.htx[channel].fir_taps[4]     = 0;
//    rules.htx[channel].fir_taps[5]     = 0;
//    rules.htx[channel].fir_taps[6]     = 0;
//    rules.htx[channel].inner_eye1      = 1000;
//    rules.htx[channel].inner_eye2      = 2000;

    // Configure Line RX rules
    rules.lrx[channel].channel_enable  = true;
    rules.lrx[channel].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;//ODSP_DSP_MODE_SLC1;
    rules.lrx[channel].gray_mapping    = true;
    rules.lrx[channel].ieee_demap      = true;
    rules.lrx[channel].invert_chan     = false;
		
//		// Configure Host RX rules_1
//    rules.hrx[channel].channel_enable  = true;
//    rules.hrx[channel].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[channel].gray_mapping    = true;
//    rules.hrx[channel].ieee_demap      = true;
//    rules.hrx[channel].invert_chan     = false;

		
		    // Setting the Egress FEC rules_1
    rules.eg_fec.enable          = true;
    rules.eg_fec.mode            = ODSP_FEC_MODE_TP_GEN;//ODSP_FEC_MODE_BYPASS;//
    rules.eg_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
    rules.eg_fec.num_in_chs      = 0;
    rules.eg_fec.in_fec_type     = ODSP_FEC_TYPE_UNKNOWN;//ODSP_FEC_TYPE_KP;//
    rules.eg_fec.num_out_chs     = 1;
    rules.eg_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
    rules.eg_fec.stream_chans    = 0;//(0x1<<(channel-1));//2;//
    rules.eg_fec.sub_sampling_en = false;
    rules.eg_fec.awake_cws       = 10;
    rules.eg_fec.sleep_cws       = 90;
    
    // Setting the Ingress FEC rules_1
    rules.ig_fec.enable          = true;
    rules.ig_fec.mode            = ODSP_FEC_MODE_BYPASS;//ODSP_FEC_MODE_TP_GEN;//
    rules.ig_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
    rules.ig_fec.num_in_chs      = 1;
    rules.ig_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
    rules.ig_fec.num_out_chs     = 0;
    rules.ig_fec.out_fec_type    = ODSP_FEC_TYPE_UNKNOWN;//ODSP_FEC_TYPE_KP;//
    rules.ig_fec.stream_chans    = (0x1<<(channel));
    rules.ig_fec.sub_sampling_en = false;
    rules.ig_fec.awake_cws       = 10;
    rules.ig_fec.sleep_cws       = 90;

    // Apply rules to start bundle
    status |= odsp_bundle_enter_operational_state(die, channel, intf, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
        return status;
    }
		
//		  // Wait for all line RX links to lock
//    status = odsp_bundle_wait_for_link_ready(die, channel, intf, 5000*1000);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: odsp_bundle_wait_for_link_ready(die=%d, first_chn=%d, intf=%d)\n", die, channel, intf);
//    }

    return status;
}

odsp_status_t example_8x100g_kp1_mission_sfec(uint32_t die)
{
    odsp_status_t status = ODSP_OK;
    for (uint32_t channel = 1; channel <= 8; channel++)
    {
        status |= init_1x100g_kp1_mission_sfec(die, channel);
			  printf("channel%d:%d\r\n",channel, status);
    }
    ODSP_MDELAY(5000);
#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
    for (uint32_t channel = 1; channel <= 8; channel++)
    {
        odsp_sfec_status_query_print(die, channel, ODSP_INTF_LTX);
        odsp_sfec_status_query_print(die, channel, ODSP_INTF_LRX);
    }
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

    return status;
}


//odsp_status_t example_odsp_mode_line_pcs()
//{
//    odsp_status_t status = ODSP_OK;
//    uint8_t first_chn = 1;
//    e_odsp_intf intf = ODSP_INTF_HOST | ODSP_INTF_LINE;
//    e_odsp_operational_mode operating_mode;
//    e_odsp_protocol_mode protocol_mode;
//    e_odsp_fec_mode fec_mode;

//    // The ASIC to initialize. In this we're not using
//    // multiple ASICs within the same GUI so it can be set to 0.
//    uint32_t die = 0;
//    
//    /** Configure the 1 bundle(s) as below:
//     *
//     **************************************************************************
//     * Bundle Index: 1
//     * Operational Mode: ODSP_MODE_LINE_PCS
//     * Protocol: ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1
//     * Channels: 
//     *    HTX: [] | HRX: []
//     *    LTX: [1] | LRX: [1]
//     * PRBS Rules:
//     *    HTX: OFF | HRX: OFF
//     *    LTX: ON | LRX: ON
//     * FEC:
//     *    IG: ODSP_FEC_MODE_BYPASS
//     *    EG: ODSP_FEC_MODE_TP_GEN
//     **************************************************************************
//     */

//    // Allocate the rules objects
//    odsp_rules_t rules;
//    
//    // Setup the default rules for bundle 1
//    first_chn = 1;
//    intf = ODSP_INTF_LINE;
//    operating_mode = ODSP_MODE_LINE_PRBS;
//    protocol_mode = ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
//    fec_mode = ODSP_FEC_MODE_BYPASS;

//    status = odsp_bundle_rules_default_set(
//        die, first_chn, intf,
//        operating_mode, protocol_mode, fec_mode, &rules);

//    if(status != ODSP_OK)
//    {
//        printf("ERROR: odsp_bundle_rules_default_set(die=%d, first_chn=%d, intf=%d,...) failed\n", die, first_chn, intf);
//    }

//    // There are some channels enabled by "odsp_bundle_rules_default_set".
//    // Clear all these channels before setting the advanced configurations
//    //clear_all_enabled_channels(&rules);
//    
//    // Configure Line TX rules
//    rules.ltx[1].channel_enable  = true;
//    rules.ltx[1].squelch_lock    = false;
//    rules.ltx[1].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[1].ieee_demap      = true;
//    rules.ltx[1].gray_mapping    = true;
//    rules.ltx[1].invert_chan     = false;
//    rules.ltx[1].fir_taps[0]     = -50;
//    rules.ltx[1].fir_taps[1]     = 650;
//    rules.ltx[1].fir_taps[2]     = 50;
//    rules.ltx[1].fir_taps[3]     = 0;
//    rules.ltx[1].fir_taps[4]     = 0;
//    rules.ltx[1].fir_taps[5]     = 0;
//    rules.ltx[1].fir_taps[6]     = 0;
//    rules.ltx[1].inner_eye1      = 1000;
//    rules.ltx[1].inner_eye2      = 2000;
//    
//    // Configure Line RX rules
//    rules.lrx[1].channel_enable  = true;
//    rules.lrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[1].gray_mapping    = true;
//    rules.lrx[1].ieee_demap      = true;
//    rules.lrx[1].invert_chan     = false;
//    
////    // Setting the Egress FEC rules
////    rules.eg_fec.enable          = true;
////    rules.eg_fec.mode            = ODSP_FEC_MODE_TP_GEN;
////    rules.eg_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
////    rules.eg_fec.num_in_chs      = 0;
////    rules.eg_fec.in_fec_type     = ODSP_FEC_TYPE_UNKNOWN;
////    rules.eg_fec.num_out_chs     = 1;
////    rules.eg_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
////    rules.eg_fec.stream_chans    = 0;
////    rules.eg_fec.sub_sampling_en = false;
////    rules.eg_fec.awake_cws       = 10;
////    rules.eg_fec.sleep_cws       = 90;
//    
////    // Setting the Ingress FEC rules
////    rules.ig_fec.enable          = true;
////    rules.ig_fec.mode            = ODSP_FEC_MODE_BYPASS;
////    rules.ig_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
////    rules.ig_fec.num_in_chs      = 1;
////    rules.ig_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
////    rules.ig_fec.num_out_chs     = 0;
////    rules.ig_fec.out_fec_type    = ODSP_FEC_TYPE_UNKNOWN;
////    rules.ig_fec.stream_chans    = 2;
////    rules.ig_fec.sub_sampling_en = false;
////    rules.ig_fec.awake_cws       = 10;
////    rules.ig_fec.sleep_cws       = 90;
//    /**
//     * As ODSP_IMPLICIT_BUNDLES_TEARDOWN is enabled by default, there is no longer a
//     * requirement for the user to call "odsp_init()" prior to calling "odsp_enter_operational_state()"
//     * although user still can if they want to, it won't break anything.
//     * With this flag enabled, API will identify all existing enabled bundles, which contain channels from the 
//     * new bundles, and tells the firmware to tear them down.
//     */
//    // Put the device into operational state
//    status = odsp_bundle_enter_operational_state(die, first_chn, intf, &rules);
//    if (status != ODSP_OK)
//    {
//        printf("Error with odsp_bundle_enter_operational_state(die=%d, first_chn=%d, intf=%d, rules)\n", die, first_chn, intf);
//    }


//    

//    
//    // Setup the ODSP_INTF_LTX PRBS generator
//    for(uint32_t channel = 1; channel <= ODSP_MAX_CHANNELS; channel++)
//    {
//        if (!rules.ltx[channel].channel_enable)
//        {
//            continue;
//        }

//        odsp_prbs_gen_rules_t gen_rules;

//        // Set default PRBS generation/monitor rules
//        if (odsp_prbs_gen_rules_default_set(&gen_rules) != ODSP_OK)
//        {
//            printf("ERROR: odsp_prbs_gen_rules_default_set(&gen_rules) failed\n");
//        }

//        // Setup the PRBS pattern
//        gen_rules.prbs_mode          = ODSP_PRBS_MODE_MSB_LSB;  // Combined or MSB/LSB
//        gen_rules.prbs_pattern       = ODSP_PRBS_PAT_PRBS31;
//        gen_rules.prbs_pattern_lsb   = ODSP_PRBS_PAT_PRBS31;  // Only used in MSB/LSB mode
//        gen_rules.nrz_mode           = false;

//        // Configure the PRBS generator
//        status = odsp_prbs_gen_config(die, channel, ODSP_INTF_LTX, &gen_rules);

//        if (status != ODSP_OK)
//        {
//            printf("ERROR: odsp_prbs_gen_config(die=%d, channel=%d, ODSP_INTF_LTX, &gen_rules)\n", die, channel);
//        }
//    }
//    
//    // Enable the ODSP_INTF_LRX checker
//    for(uint32_t channel = 1; channel <= ODSP_MAX_CHANNELS; channel++)
//    {
//        if (!rules.lrx[channel].channel_enable)
//        {
//            continue;
//        }

//        odsp_prbs_chk_rules_t chk_rules;

//        // Set default PRBS generation/monitor rules
//        if (odsp_prbs_chk_rules_default_set(&chk_rules) != ODSP_OK)
//        {
//            printf("ERROR: odsp_prbs_chk_rules_default_set(&chk_rules)\n");
//        }
//        // Setup the checker
//        chk_rules.prbs_mode                 = ODSP_PRBS_MODE_MSB_LSB;  // Combined or MSB/LSB
//        chk_rules.prbs_pattern              = ODSP_PRBS_PAT_PRBS31;
//        chk_rules.prbs_pattern_lsb          = ODSP_PRBS_PAT_PRBS31;  // Only used in MSB/LSB mode
//        chk_rules.nrz_mode                  = false;

//        // Configure the PRBS checker
//        status = odsp_prbs_chk_config(die, channel, ODSP_INTF_LRX, &chk_rules);
//        if (status != ODSP_OK)
//        {
//            printf("ERROR: odsp_prbs_chk_config(die=%d, channel=%d, ODSP_INTF_LRX, &chk_rules)\n", die, channel);
//        }
//    }
//		
//		    // Wait for all line RX links to lock
//    status = odsp_bundle_wait_for_link_ready(die, first_chn, intf, 5000*1000);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: odsp_bundle_wait_for_link_ready(die=%d, first_chn=%d, intf=%d)\n", die, first_chn, intf);
//    }

//    ODSP_MDELAY(100);
//    
//    // Clear the PRBS checker counters
//    for (uint8_t channel = 1; channel <= ODSP_MAX_CHANNELS; channel++)
//    {
//        if (!rules.lrx[channel].channel_enable)
//        {
//            continue;
//        }

//        odsp_prbs_chk_status_t chk_status;
//        status = odsp_prbs_chk_status(die, channel, ODSP_INTF_LRX, &chk_status);
//        if (status != ODSP_OK)
//        {
//            printf("ERROR: Clear odsp_prbs_chk_status(die=%d, channel=%d, intf=ODSP_INTF_LRX)\n", die, channel);
//        }
//    }

//    // Accumulate Traffic
//    ODSP_MDELAY(2000);
//    
//    // Query the PRBS Status
//    for (uint8_t channel = 1; channel <= ODSP_MAX_CHANNELS; channel++)
//    {
//        if (!rules.lrx[channel].channel_enable)
//        {
//            continue;
//        }

//        odsp_prbs_chk_status_t chk_status;
//        status = odsp_prbs_chk_status(die, channel, ODSP_INTF_LRX, &chk_status);
//        if (status != ODSP_OK)
//        {
//            printf("ERROR: Query odsp_prbs_chk_status(die=%d, channel=%d, intf=ODSP_INTF_LRX)\n", die, channel);
//        }

//        // Check for lock
//        if ((!chk_status.prbs_lock) || ((chk_status.prbs_mode == ODSP_PRBS_MODE_MSB_LSB) && (!chk_status.prbs_lock_lsb)))
//        {
//            printf("PRBS not locked channel=%d, intf=ODSP_INTF_LRX!\n", channel);
//        }

//        status = odsp_prbs_chk_status_print(die, channel, ODSP_INTF_LRX, &chk_status);

//        double ber_msb, ber_lsb;
//        status = odsp_prbs_chk_ber(&chk_status, &ber_msb, &ber_lsb);
//    }

//    return status;
//}



///////////////////////////////////////////////////SFEC+//////////////////////////////////


/* 
 * This helper function will create 1 bundle of 100G KP1 FEC + line SFEC+:
 *    - Host 1x53.125Gbps PAM4 <=> Line 1x56.666666Gbps PAM4.
 *    - ODSP_FEC_MODE_BYPASS, FEC monitor is disabled
 * Return ODSP_OK on success, ODSP_ERROR on failure
 */
static odsp_status_t init_1x100g_kp1_mission_sfec_plus(uint32_t die, uint32_t channel)
{
    odsp_rules_t rules;
    e_odsp_operational_mode operational_mode = ODSP_MODE_MISSION;
    e_odsp_protocol_mode    protocol_mode    = ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
    e_odsp_fec_mode         fec_mode         = ODSP_FEC_MODE_BYPASS;
    e_odsp_intf             intf             = ODSP_INTF_HOST | ODSP_INTF_LINE;
    odsp_status_t status = ODSP_OK;

    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
     * - Signaling: PAM
     * - Baudrate: 53.125Gbd
     * - FEC disabled
     *  ...
     */
    status |= odsp_bundle_rules_default_set(die,
            channel, intf, operational_mode, protocol_mode, fec_mode, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: Build default bundle rules failed\n");
        return status;
    }

    /* Customize channel setting
     */
    
    // Configure Line TX rules
    rules.ltx[channel].channel_enable  = true;
    rules.ltx[channel].squelch_lock    = false;
    rules.ltx[channel].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[channel].ieee_demap      = true;
    rules.ltx[channel].gray_mapping    = true;
    rules.ltx[channel].invert_chan     = _is_channel_inverted(channel);
    rules.ltx[channel].fir_taps[0]     = -50;
    rules.ltx[channel].fir_taps[1]     = 400;
    rules.ltx[channel].fir_taps[2]     = 50;
    rules.ltx[channel].fir_taps[3]     = 0;
    rules.ltx[channel].fir_taps[4]     = 0;
    rules.ltx[channel].fir_taps[5]     = 0;
    rules.ltx[channel].fir_taps[6]     = 0;
    rules.ltx[channel].inner_eye1      = 1000;
    rules.ltx[channel].inner_eye2      = 2000;

    // Configure Host TX rules
    rules.htx[channel].channel_enable  = true;
    rules.htx[channel].squelch_lock    = false;
    rules.htx[channel].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.htx[channel].ieee_demap      = true;
    rules.htx[channel].gray_mapping    = true;
    rules.htx[channel].invert_chan     = _is_channel_inverted(channel);
    rules.htx[channel].fir_taps[0]     = -50;
    rules.htx[channel].fir_taps[1]     = 650;
    rules.htx[channel].fir_taps[2]     = 50;
    rules.htx[channel].fir_taps[3]     = 0;
    rules.htx[channel].fir_taps[4]     = 0;
    rules.htx[channel].fir_taps[5]     = 0;
    rules.htx[channel].fir_taps[6]     = 0;
    rules.htx[channel].inner_eye1      = 1000;
    rules.htx[channel].inner_eye2      = 2000;


    // Configure Line RX rules
    rules.lrx[channel].channel_enable  = true;
    rules.lrx[channel].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[channel].gray_mapping    = true;
    rules.lrx[channel].ieee_demap      = true;
    rules.lrx[channel].invert_chan     = _is_channel_inverted(channel);

    // Configure Host RX rules
    rules.hrx[channel].channel_enable  = true;
    rules.hrx[channel].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
    rules.hrx[channel].gray_mapping    = true;
    rules.hrx[channel].ieee_demap      = true;
    rules.hrx[channel].invert_chan     = _is_channel_inverted(channel);

    // Setting the LTX xbar rules
    rules.ltx_xbar_src_chan[channel][0] = channel;
    rules.ltx_xbar_src_chan[channel][1] = 0xf;
    rules.ltx_xbar_src_chan[channel][2] = 0xf;
    rules.ltx_xbar_src_chan[channel][3] = 0xf;

    // Setting the LTX xbar source clock
    rules.ltx_clk_xbar[channel] = channel;

    // Setting the HTX xbar rules
    rules.htx_xbar_src_chan[channel][0] = channel;
    rules.htx_xbar_src_chan[channel][1] = 0xf;
    rules.htx_xbar_src_chan[channel][2] = 0xf;
    rules.htx_xbar_src_chan[channel][3] = 0xf;

    // Setting the HTX xbar source clock
    rules.htx_clk_xbar[channel] = channel;

    // Customize some rules if necessary
    // Setting the QC thresholds
    rules.hrx_qc.snr_threshold_mm_enter  = ODSP_RX_QC_SNR_THRESH_17dB;
    rules.hrx_qc.snr_threshold_mm_exit   = ODSP_RX_QC_SNR_THRESH_16dB;
    rules.hrx_qc.slc_err_limit           = 50;

    // Override the TX driver type if necessary
    rules.ltx_driver_type_override       = ODSP_TX_DRIVER_TYPE_SIPHO_1P00V;

    //sfec setting
    rules.sfec.sfec_mode = ODSP_SFEC_PLUS;
    rules.sfec.sfec_cfg  = ODSP_SFEC_1_X_100;
    rules.host_side.baud_rate = ODSP_BAUD_RATE_53p1;
    rules.line_side.baud_rate = ODSP_BAUD_RATE_53p1_SFECP;

    // Apply rules to start bundle
    status |= odsp_bundle_enter_operational_state(die, channel, intf, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
        return status;
    }

    return status;
}

odsp_status_t example_8x100g_kp1_mission_sfec_plus(uint32_t die)
{
    odsp_status_t status = ODSP_OK;
    for (uint32_t channel = 1; channel <= 8; channel++)
    {
        status |= init_1x100g_kp1_mission_sfec_plus(die, channel);
    }
    ODSP_MDELAY(5000);
#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
    for (uint32_t channel = 1; channel <= 8; channel++)
    {
        odsp_sfec_plus_status_query_print(die, channel, ODSP_INTF_LTX);
        odsp_sfec_plus_status_query_print(die, channel, ODSP_INTF_LRX);
    }
#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
    return status;
}



///////////////////////////////VDM/////////////////////////////////////////////////////

//VDM example
typedef  enum
{
    VDM_START,
    VDM_POLL,
    VDM_GET,
    VDM_POST
} e_vdm_state;

static const char* _fec_intf_to_string(e_odsp_intf intf)
{
    if (intf == ODSP_INTF_IG)
        return "IG";
    else if (intf == ODSP_INTF_EG)
        return "EG";
    return "UNKNOWN INTF";
}

odsp_status_t fec_stats_poller_request(uint32_t die, uint32_t channel, e_odsp_intf intf)
{
    odsp_fec_stats_cp_block_t stats;
    odsp_status_t status = ODSP_OK;
    
    printf("%s VDM poller for channel: %d\n", _fec_intf_to_string(intf), channel);
    // Note that this is just an example of how to design a non-blocking FSM. If you're using 25MHz MDIO
    // or 1MHz I2C then the IO won't be a bottleneck and a complicated FSM is not required.

    // make sure the same stats data is used for BOTH the request and the get
    // incrementally grab each block
    int block = 0;
    // track our VDM state
    e_vdm_state state = VDM_START;
    odsp_status_t poll_status;
    // ... whatever other stuff ...
    time_t seconds_start, seconds_end;
    //seconds_start = time(NULL);
	  seconds_start = SysTick->VAL;//HAL_GetTick();

    while (state <= VDM_POST)
    {
        switch(state)
        {
            case VDM_START:
                printf("VDM request\n");
                // request stats from the FW, clearing right afterwards
                status |= odsp_fec_stats_poller_request(die, channel, intf, true, &stats);
                // non-blocking, exit right away
                state += 1;
                // reset our block too
                block = 0;
                break;

            case VDM_POLL:
                // see if the FW has copied the stats, don't copy the buffer yet
                poll_status = odsp_fec_stats_poller_get(die, channel, intf, 0, &stats);
                if(poll_status == ODSP_POLLER_ERROR) {
                    //some error, go back and try again
                    state = VDM_START;
                }
                else if(poll_status == ODSP_POLLER_WAITING) {
                    //stay here, FW isn't ready yet
                }
                else {
                    //FW has copied, move on
                    printf("VDM captured\n");
                    state += 1;
                }
                break;

            case VDM_GET:
                // FW has finished copying the stats, now pull them out of the FW copy buffer one-by-one
                printf("VDM get block %d\n", block);
                poll_status = odsp_fec_stats_poller_get(die, channel, intf, 1<<block, &stats);
                if(poll_status == ODSP_POLLER_ERROR) {
                    //some error, go back and try again
                    state = VDM_START;
                }
                else if(poll_status == ODSP_POLLER_WAITING) {
                    //stay here, FW isn't ready yet
                    // (technically this can't happen as of 1.10)
                }
                else {
                    //block was grabbed, proceed to the next one
                    block += 1;
                    if(block >= ODSP_FEC_STATS_CP_BLOCKS) {
                        //done all the blocks, reset
                        block = 0;
                        state += 1;
                    }
                }
                break;

            case VDM_POST:
                // Normally you will want to populate the CMIS register space with the captured VDM
                // stats. In this example we're just going to print to the console log.
                // NOTE: There is no need to call odsp_fec_stats_poller_calc_ber in module FW; this is for debug only!
                {
                    odsp_fec_stats_poller_t avg_rates;
                    odsp_fec_stats_poller_t min_rates;
                    odsp_fec_stats_poller_t max_rates;
                    odsp_fec_stats_poller_t cur_rates;
                    status |= odsp_fec_stats_poller_calc_ber(die, channel, intf, &stats, &avg_rates, &min_rates, &max_rates, &cur_rates);

                    printf("===> Average <========\n");
                    printf("  ber  : %7.2e\n", avg_rates.ber);
                    printf("  ser  : %7.2e\n", avg_rates.ser);
                    printf("  ferc : %7.2e\n", avg_rates.ferc);
                    printf("  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        printf("%d-%7.2e  ", i+1, avg_rates.corrected_ratio_hist[i]);
                    printf("\n");
                    printf("===> Min. <========\n");
                    printf( "  ber  : %7.2e\n", min_rates.ber);
                    printf( "  ser  : %7.2e\n", min_rates.ser);
                    printf( "  ferc : %7.2e\n", min_rates.ferc);
                    printf( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        printf("%d-%7.2e  ", i+1, min_rates.corrected_ratio_hist[i]);
                    printf("\n");
                    printf("===> Max. <========\n");
                    printf( "  ber  : %7.2e\n", max_rates.ber);
                    printf( "  ser  : %7.2e\n", max_rates.ser);
                    printf( "  ferc : %7.2e\n", max_rates.ferc);
                    printf( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        printf("%d-%7.2e  ", i+1, max_rates.corrected_ratio_hist[i]);
                    printf("\n");
                    printf("===> Current <========\n");
                    printf( "  ber  : %7.2e\n", cur_rates.ber);
                    printf( "  ser  : %7.2e\n", cur_rates.ser);
                    printf( "  ferc : %7.2e\n", cur_rates.ferc);
                    printf( "  hist : ");
                    for (uint16_t i=0; i<15; i++)
                        printf("%d-%7.2e  ", i+1, cur_rates.corrected_ratio_hist[i]);
                    printf("\n");

                }
                state += 1;
                break;

            default:
                break;
        }
        //seconds_end = time(NULL);
				seconds_end = SysTick->VAL;//HAL_GetTick();;
        if ((seconds_end - seconds_start) > 10)
        {
            ODSP_PRINTF( "%s odsp_fec_stats_poller_get for channel %d, timeout after 10s \n", _fec_intf_to_string(intf), channel);
            status = ODSP_ERROR;
            break;
        }
    }

    if (status != ODSP_OK || (state != VDM_POST + 1))
        return ODSP_ERROR;

    return status;
}




////////////////////////////////fec Mon/////////////////////////////////////////////////////////////////

odsp_status_t enable_1x100g_fec_mon(uint32_t die, e_odsp_intf intf, uint32_t channel, bool enable)
{
    odsp_status_t status = ODSP_OK;
    //then enable FEC monitoring separately at IG and EG
    odsp_fec_rules_t rules;
	
	
    
    rules.enable          = enable;
    rules.mode            = ODSP_FEC_MODE_BYPASS;
    rules.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
    rules.in_fec_type     = ODSP_FEC_TYPE_KP;
    rules.num_in_chs      = 1;
    rules.out_fec_type    = ODSP_FEC_TYPE_UNKNOWN;//ODSP_FEC_TYPE_KP;//
    rules.num_out_chs     = 0;
    rules.stream_chans    = 0x1 << channel;//2;//
    rules.sub_sampling_en = false;
    rules.awake_cws       = 10;
    rules.sleep_cws       = 90;
	
	
//		    // Setting the Egress FEC rules_1
//    rules.eg_fec.enable          = true;
//    rules.eg_fec.mode            = ODSP_FEC_MODE_TP_GEN;//ODSP_FEC_MODE_BYPASS;//
//    rules.eg_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
//    rules.eg_fec.num_in_chs      = 0;
//    rules.eg_fec.in_fec_type     = ODSP_FEC_TYPE_UNKNOWN;//ODSP_FEC_TYPE_KP;//
//    rules.eg_fec.num_out_chs     = 1;
//    rules.eg_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
//    rules.eg_fec.stream_chans    = 0;//(0x1<<(channel-1));//2;//
//    rules.eg_fec.sub_sampling_en = false;
//    rules.eg_fec.awake_cws       = 10;
//    rules.eg_fec.sleep_cws       = 90;
//    
//    // Setting the Ingress FEC rules_1
//    rules.ig_fec.enable          = true;
//    rules.ig_fec.mode            = ODSP_FEC_MODE_BYPASS;//ODSP_FEC_MODE_TP_GEN;//
//    rules.ig_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_100;
//    rules.ig_fec.num_in_chs      = 1;
//    rules.ig_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
//    rules.ig_fec.num_out_chs     = 0;
//    rules.ig_fec.out_fec_type    = ODSP_FEC_TYPE_UNKNOWN;//ODSP_FEC_TYPE_KP;//
//    rules.ig_fec.stream_chans    = (0x1<<(channel));
//    rules.ig_fec.sub_sampling_en = false;
//    rules.ig_fec.awake_cws       = 10;
//    rules.ig_fec.sleep_cws       = 90;

	    status = odsp_fec_mon_cfg(die, channel, intf, &rules);
    if(status != ODSP_OK)
    {
       printf("%s FEC mon configuration error\r\n", intf == ODSP_INTF_IG ? "IG" : "EG");
    }
		else printf("FEC Mon config done channel%d\r\n", channel);
    //Wait for FEC stream lock
    if (odsp_fec_mon_is_ready(die, channel, intf, 5000000) != true)
    {
        printf("Timed out waiting for the bundle %d fec monitor to lock\r\n", channel);
    }
		else printf("FEC Mon ready channel%d\r\n", channel);

#if defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)

    odsp_fec_pcs_bundle_status_query_dump(die, channel, intf);
    odsp_fec_pcs_stats_query_dump(die, channel, intf);
		printf("SNR: %lf",odsp_rx_dsp_snr_read_db(die, channel, intf));
		

#endif //defined(ODSP_HAS_DIAGNOSTIC_DUMPS) && (ODSP_HAS_DIAGNOSTIC_DUMPS==1)
    
    return status;

}

static odsp_status_t enable_8x100g_fec_mon(uint32_t die, e_odsp_intf intf, bool enable)
{
    odsp_status_t status = ODSP_OK;
    for (uint32_t channel = 1; channel <= 1; channel = channel+1)
    {
        status |= enable_1x100g_fec_mon(die, intf, channel, enable);
    }
    return status;
}







////////////////////////////////////////fec VDM///////////////////////////////////////////////////////

static odsp_status_t enable_1x100g_fec_vdm(uint32_t die, e_odsp_intf intf, uint32_t channel, bool enable)
{
    odsp_status_t status = ODSP_OK;

//#if !defined(ODSP_DONT_USE_STDLIB)
    //enable FEC VDM poller in FW
    odsp_fec_stats_poller_rules_t poll_rules = {.en = enable, .interval_time = 0, .accumulation_time = 1000};//1s
    status |= odsp_fec_stats_poller_cfg(die, channel, intf, &poll_rules);
		printf("FEC stats poller config channel%d\r\n", channel);
//#endif //!defined(ODSP_DONT_USE_STDLIB)

    return status;
}

static odsp_status_t enable_8x100g_fec_vdm(uint32_t die, e_odsp_intf intf, bool enable)
{
    odsp_status_t status = ODSP_OK;
    for (uint32_t channel = 1; channel <= 1; channel = channel+7)
    {
        status |= enable_1x100g_fec_vdm(die, intf, channel, enable);
    }
    return status;
}

static odsp_status_t poll_8x100g_fec_vdm(uint32_t die, e_odsp_intf intf)
{
    odsp_status_t status = ODSP_OK;
    for (uint32_t channel = 1; channel <= 1; channel = channel+7)
    {
        status = fec_stats_poller_request(die, channel, intf);
        if (status != ODSP_OK)
            printf("fec stats poller failed at bundle %d\r\n", channel);
				else printf("FEC stats poller request done channel%d\r\n", channel);
    }
    return status;
}


odsp_status_t example_enable_8x100g_mission_100g_fec_mon_vdm(uint32_t die)
{
    odsp_status_t status = ODSP_OK;
//    status = example_init_8bundles_8x100g_mission(die);//enable_8x100g_mission(die);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: enable_8x100g_mission(die)\n");
//        return status;
//    }
    
    status = enable_8x100g_fec_mon(die, ODSP_INTF_IG, true);
    if (status != ODSP_OK)
    {
        printf("ERROR: enable_8x100g_fec_mon(die, ODSP_INTF_IG, true);\n");
        return status;
    }
    
//    status = enable_8x100g_fec_mon(die, ODSP_INTF_EG, true);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: enable_8x100g_fec_mon(die, ODSP_INTF_EG, true)\n");
//        return status;
//    }
    
//    status = enable_8x100g_fec_vdm(die, ODSP_INTF_IG, true);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: enable_8x100g_fec_vdm(die, ODSP_INTF_IG, true)\n");
//        return status;
//    }    
//    status = enable_8x100g_fec_vdm(die, ODSP_INTF_EG, true);
//    {
//        printf("ERROR: enable_8x100g_fec_vdm(die, ODSP_INTF_EG, true)\n");
//        return status;
//    }  
  
//    ODSP_UDELAY(5000000);
//    status = poll_8x100g_fec_vdm(die, ODSP_INTF_IG);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: poll_8x100g_fec_vdm(die, ODSP_INTF_IG)\n");
//        return status;
//    } 
   
//    status = poll_8x100g_fec_vdm(die, ODSP_INTF_EG);
//    if (status != ODSP_OK)
//    {
//        printf("ERROR: poll_8x100g_fec_vdm(die, ODSP_INTF_EG)\n");
//        return status;
//    }    
    return status;
}



odsp_status_t init_channel_process(uint32_t die, uint32_t channel)
{
    odsp_rules_t rules_1;
    e_odsp_operational_mode operational_mode = ODSP_MODE_MISSION;//global_operational_mode;//
    e_odsp_protocol_mode    protocol_mode    = ODSP_PROT_MODE_50G_1Px26p6_TO_1Px26p6;//global_protocol_mode;//ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;//ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
    e_odsp_fec_mode         fec_mode         = ODSP_FEC_MODE_BYPASS;//global_fec_mode;//
    e_odsp_intf             intf             = ODSP_INTF_LINE;//global_intf;// ODSP_INTF_HOST | 
    odsp_status_t status = ODSP_OK;	

    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
     * - Signaling: PAM
     * - Baudrate: 53.125Gbd
     * - FEC disabled
     *  ...
     */
    status |= odsp_bundle_rules_default_set(die,
            channel, intf, operational_mode, protocol_mode, fec_mode, &rules_1);
    if(status != ODSP_OK)
    {
        printf("ERROR: Build default bundle rules failed\n");
        return status;
    }

//    /* Customize channel setting
//     */
//    
    // Configure Line TX rules_1
    rules_1.ltx[channel].channel_enable  = true;
    rules_1.ltx[channel].squelch_lock    = false;
    rules_1.ltx[channel].lut_mode        = ODSP_TX_LUT_7TAP_LIN;
    rules_1.ltx[channel].ieee_demap      = true;
    rules_1.ltx[channel].gray_mapping    = true;
    rules_1.ltx[channel].invert_chan     = false;
    rules_1.ltx[channel].fir_taps[0]     = -50;
    rules_1.ltx[channel].fir_taps[1]     = 650;
    rules_1.ltx[channel].fir_taps[2]     = 50;
    rules_1.ltx[channel].fir_taps[3]     = 0;
    rules_1.ltx[channel].fir_taps[4]     = 0;
    rules_1.ltx[channel].fir_taps[5]     = 0;
    rules_1.ltx[channel].fir_taps[6]     = 0;
    rules_1.ltx[channel].inner_eye1      = 1000;
    rules_1.ltx[channel].inner_eye2      = 2000;
    
    // Configure Line RX rules_1
    rules_1.lrx[1].channel_enable  = true;
    rules_1.lrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
    rules_1.lrx[1].gray_mapping    = true;
    rules_1.lrx[1].ieee_demap      = true;
    rules_1.lrx[1].invert_chan     = false;

    // Apply rules to start bundle
    status |= odsp_bundle_enter_operational_state(die, channel, intf, &rules_1);
    if(status != ODSP_OK)
    {
        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
        return status;
    }

    return status;
}



//odsp_status_t init_allchannel_process(uint32_t die )
//{
//    odsp_rules_t rules;
//    e_odsp_operational_mode operating_mode = ODSP_MODE_DUAL_PCS;;//ODSP_MODE_DUAL_PRBS;//ODSP_MODE_MISSION;//ODSP_MODE_LINE_PRBS;//operational_mode = global_operational_mode;//
//    e_odsp_protocol_mode    protocol_mode = ODSP_PROT_MODE_400G_8Px26p6_TO_8Px26p6;//ODSP_PROT_MODE_400G_4Px53p1_TO_4Px53p1;//ODSP_PROT_MODE_800G_8Px53p1_TO_8Px53p1;//protocol_mode    = global_protocol_mode;//ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
//    e_odsp_fec_mode         fec_mode = ODSP_FEC_MODE_BYPASS;//fec_mode         = global_fec_mode;//ODSP_FEC_MODE_BYPASS;
//    e_odsp_intf             intf = ODSP_INTF_HOST | ODSP_INTF_LINE;//intf             = global_intf;// ODSP_INTF_HOST | ODSP_INTF_LINE;
//    odsp_status_t status = ODSP_OK;	

//	  uint32_t channel = 1;


//    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
//     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
//     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
//     * - Signaling: PAM
//     * - Baudrate: 53.125Gbd
//     * - FEC disabled
//     *  ...
//     */
//    status |= odsp_bundle_rules_default_set(die,
//            channel, intf, operating_mode, protocol_mode, fec_mode, &rules);
//    if(status != ODSP_OK)
//    {
//        printf("ERROR: Build default bundle rules failed\n");
//        return status;
//    }
//		
//		//clear_all_enabled_channels(&rules);

////    /* Customize channel setting
////     */
////    
//    // Configure Line TX rules
//    rules.ltx[1].channel_enable  = true;
//    rules.ltx[1].squelch_lock    = false;
//    rules.ltx[1].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[1].ieee_demap      = true;
//    rules.ltx[1].gray_mapping    = true;
//    rules.ltx[1].invert_chan     = false;
//    rules.ltx[1].fir_taps[0]     = -50;
//    rules.ltx[1].fir_taps[1]     = 800;
//    rules.ltx[1].fir_taps[2]     = 50;
//    rules.ltx[1].fir_taps[3]     = 0;
//    rules.ltx[1].fir_taps[4]     = 0;
//    rules.ltx[1].fir_taps[5]     = 0;
//    rules.ltx[1].fir_taps[6]     = 0;
//    rules.ltx[1].inner_eye1      = 1000;
//    rules.ltx[1].inner_eye2      = 2000;

//    rules.ltx[2].channel_enable  = true;
//    rules.ltx[2].squelch_lock    = false;
//    rules.ltx[2].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[2].ieee_demap      = true;
//    rules.ltx[2].gray_mapping    = true;
//    rules.ltx[2].invert_chan     = false;
//    rules.ltx[2].fir_taps[0]     = -50;
//    rules.ltx[2].fir_taps[1]     = 800;
//    rules.ltx[2].fir_taps[2]     = 50;
//    rules.ltx[2].fir_taps[3]     = 0;
//    rules.ltx[2].fir_taps[4]     = 0;
//    rules.ltx[2].fir_taps[5]     = 0;
//    rules.ltx[2].fir_taps[6]     = 0;
//    rules.ltx[2].inner_eye1      = 1000;
//    rules.ltx[2].inner_eye2      = 2000;

//    rules.ltx[3].channel_enable  = true;
//    rules.ltx[3].squelch_lock    = false;
//    rules.ltx[3].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[3].ieee_demap      = true;
//    rules.ltx[3].gray_mapping    = true;
//    rules.ltx[3].invert_chan     = false;
//    rules.ltx[3].fir_taps[0]     = -50;
//    rules.ltx[3].fir_taps[1]     = 800;
//    rules.ltx[3].fir_taps[2]     = 50;
//    rules.ltx[3].fir_taps[3]     = 0;
//    rules.ltx[3].fir_taps[4]     = 0;
//    rules.ltx[3].fir_taps[5]     = 0;
//    rules.ltx[3].fir_taps[6]     = 0;
//    rules.ltx[3].inner_eye1      = 1000;
//    rules.ltx[3].inner_eye2      = 2000;

//    rules.ltx[4].channel_enable  = true;
//    rules.ltx[4].squelch_lock    = false;
//    rules.ltx[4].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[4].ieee_demap      = true;
//    rules.ltx[4].gray_mapping    = true;
//    rules.ltx[4].invert_chan     = false;
//    rules.ltx[4].fir_taps[0]     = -50;
//    rules.ltx[4].fir_taps[1]     = 800;
//    rules.ltx[4].fir_taps[2]     = 50;
//    rules.ltx[4].fir_taps[3]     = 0;
//    rules.ltx[4].fir_taps[4]     = 0;
//    rules.ltx[4].fir_taps[5]     = 0;
//    rules.ltx[4].fir_taps[6]     = 0;
//    rules.ltx[4].inner_eye1      = 1000;
//    rules.ltx[4].inner_eye2      = 2000;

//    rules.ltx[5].channel_enable  = true;
//    rules.ltx[5].squelch_lock    = false;
//    rules.ltx[5].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[5].ieee_demap      = true;
//    rules.ltx[5].gray_mapping    = true;
//    rules.ltx[5].invert_chan     = false;
//    rules.ltx[5].fir_taps[0]     = -50;
//    rules.ltx[5].fir_taps[1]     = 800;
//    rules.ltx[5].fir_taps[2]     = 50;
//    rules.ltx[5].fir_taps[3]     = 0;
//    rules.ltx[5].fir_taps[4]     = 0;
//    rules.ltx[5].fir_taps[5]     = 0;
//    rules.ltx[5].fir_taps[6]     = 0;
//    rules.ltx[5].inner_eye1      = 1000;
//    rules.ltx[5].inner_eye2      = 2000;

//    rules.ltx[6].channel_enable  = true;
//    rules.ltx[6].squelch_lock    = false;
//    rules.ltx[6].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[6].ieee_demap      = true;
//    rules.ltx[6].gray_mapping    = true;
//    rules.ltx[6].invert_chan     = false;
//    rules.ltx[6].fir_taps[0]     = -50;
//    rules.ltx[6].fir_taps[1]     = 800;
//    rules.ltx[6].fir_taps[2]     = 50;
//    rules.ltx[6].fir_taps[3]     = 0;
//    rules.ltx[6].fir_taps[4]     = 0;
//    rules.ltx[6].fir_taps[5]     = 0;
//    rules.ltx[6].fir_taps[6]     = 0;
//    rules.ltx[6].inner_eye1      = 1000;
//    rules.ltx[6].inner_eye2      = 2000;

//    rules.ltx[7].channel_enable  = true;
//    rules.ltx[7].squelch_lock    = false;
//    rules.ltx[7].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[7].ieee_demap      = true;
//    rules.ltx[7].gray_mapping    = true;
//    rules.ltx[7].invert_chan     = false;
//    rules.ltx[7].fir_taps[0]     = -50;
//    rules.ltx[7].fir_taps[1]     = 800;
//    rules.ltx[7].fir_taps[2]     = 50;
//    rules.ltx[7].fir_taps[3]     = 0;
//    rules.ltx[7].fir_taps[4]     = 0;
//    rules.ltx[7].fir_taps[5]     = 0;
//    rules.ltx[7].fir_taps[6]     = 0;
//    rules.ltx[7].inner_eye1      = 1000;
//    rules.ltx[7].inner_eye2      = 2000;

//    rules.ltx[8].channel_enable  = true;
//    rules.ltx[8].squelch_lock    = false;
//    rules.ltx[8].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.ltx[8].ieee_demap      = true;
//    rules.ltx[8].gray_mapping    = true;
//    rules.ltx[8].invert_chan     = false;
//    rules.ltx[8].fir_taps[0]     = -50;
//    rules.ltx[8].fir_taps[1]     = 800;
//    rules.ltx[8].fir_taps[2]     = 50;
//    rules.ltx[8].fir_taps[3]     = 0;
//    rules.ltx[8].fir_taps[4]     = 0;
//    rules.ltx[8].fir_taps[5]     = 0;
//    rules.ltx[8].fir_taps[6]     = 0;
//    rules.ltx[8].inner_eye1      = 1000;
//    rules.ltx[8].inner_eye2      = 2000;
//    
//    // Configure Host TX rules
//    rules.htx[1].channel_enable  = true;
//    rules.htx[1].squelch_lock    = false;
//    rules.htx[1].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[1].ieee_demap      = true;
//    rules.htx[1].gray_mapping    = true;
//    rules.htx[1].invert_chan     = false;
//    rules.htx[1].fir_taps[0]     = -50;
//    rules.htx[1].fir_taps[1]     = 800;
//    rules.htx[1].fir_taps[2]     = 50;
//    rules.htx[1].fir_taps[3]     = 0;
//    rules.htx[1].fir_taps[4]     = 0;
//    rules.htx[1].fir_taps[5]     = 0;
//    rules.htx[1].fir_taps[6]     = 0;
//    rules.htx[1].inner_eye1      = 1000;
//    rules.htx[1].inner_eye2      = 2000;

//    rules.htx[2].channel_enable  = true;
//    rules.htx[2].squelch_lock    = false;
//    rules.htx[2].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[2].ieee_demap      = true;
//    rules.htx[2].gray_mapping    = true;
//    rules.htx[2].invert_chan     = false;
//    rules.htx[2].fir_taps[0]     = -50;
//    rules.htx[2].fir_taps[1]     = 800;
//    rules.htx[2].fir_taps[2]     = 50;
//    rules.htx[2].fir_taps[3]     = 0;
//    rules.htx[2].fir_taps[4]     = 0;
//    rules.htx[2].fir_taps[5]     = 0;
//    rules.htx[2].fir_taps[6]     = 0;
//    rules.htx[2].inner_eye1      = 1000;
//    rules.htx[2].inner_eye2      = 2000;

//    rules.htx[3].channel_enable  = true;
//    rules.htx[3].squelch_lock    = false;
//    rules.htx[3].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[3].ieee_demap      = true;
//    rules.htx[3].gray_mapping    = true;
//    rules.htx[3].invert_chan     = false;
//    rules.htx[3].fir_taps[0]     = -50;
//    rules.htx[3].fir_taps[1]     = 800;
//    rules.htx[3].fir_taps[2]     = 50;
//    rules.htx[3].fir_taps[3]     = 0;
//    rules.htx[3].fir_taps[4]     = 0;
//    rules.htx[3].fir_taps[5]     = 0;
//    rules.htx[3].fir_taps[6]     = 0;
//    rules.htx[3].inner_eye1      = 1000;
//    rules.htx[3].inner_eye2      = 2000;

//    rules.htx[4].channel_enable  = true;
//    rules.htx[4].squelch_lock    = false;
//    rules.htx[4].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[4].ieee_demap      = true;
//    rules.htx[4].gray_mapping    = true;
//    rules.htx[4].invert_chan     = false;
//    rules.htx[4].fir_taps[0]     = -50;
//    rules.htx[4].fir_taps[1]     = 800;
//    rules.htx[4].fir_taps[2]     = 50;
//    rules.htx[4].fir_taps[3]     = 0;
//    rules.htx[4].fir_taps[4]     = 0;
//    rules.htx[4].fir_taps[5]     = 0;
//    rules.htx[4].fir_taps[6]     = 0;
//    rules.htx[4].inner_eye1      = 1000;
//    rules.htx[4].inner_eye2      = 2000;

//    rules.htx[5].channel_enable  = true;
//    rules.htx[5].squelch_lock    = false;
//    rules.htx[5].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[5].ieee_demap      = true;
//    rules.htx[5].gray_mapping    = true;
//    rules.htx[5].invert_chan     = false;
//    rules.htx[5].fir_taps[0]     = -50;
//    rules.htx[5].fir_taps[1]     = 800;
//    rules.htx[5].fir_taps[2]     = 50;
//    rules.htx[5].fir_taps[3]     = 0;
//    rules.htx[5].fir_taps[4]     = 0;
//    rules.htx[5].fir_taps[5]     = 0;
//    rules.htx[5].fir_taps[6]     = 0;
//    rules.htx[5].inner_eye1      = 1000;
//    rules.htx[5].inner_eye2      = 2000;

//    rules.htx[6].channel_enable  = true;
//    rules.htx[6].squelch_lock    = false;
//    rules.htx[6].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[6].ieee_demap      = true;
//    rules.htx[6].gray_mapping    = true;
//    rules.htx[6].invert_chan     = false;
//    rules.htx[6].fir_taps[0]     = -50;
//    rules.htx[6].fir_taps[1]     = 800;
//    rules.htx[6].fir_taps[2]     = 50;
//    rules.htx[6].fir_taps[3]     = 0;
//    rules.htx[6].fir_taps[4]     = 0;
//    rules.htx[6].fir_taps[5]     = 0;
//    rules.htx[6].fir_taps[6]     = 0;
//    rules.htx[6].inner_eye1      = 1000;
//    rules.htx[6].inner_eye2      = 2000;

//    rules.htx[7].channel_enable  = true;
//    rules.htx[7].squelch_lock    = false;
//    rules.htx[7].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[7].ieee_demap      = true;
//    rules.htx[7].gray_mapping    = true;
//    rules.htx[7].invert_chan     = false;
//    rules.htx[7].fir_taps[0]     = -50;
//    rules.htx[7].fir_taps[1]     = 800;
//    rules.htx[7].fir_taps[2]     = 50;
//    rules.htx[7].fir_taps[3]     = 0;
//    rules.htx[7].fir_taps[4]     = 0;
//    rules.htx[7].fir_taps[5]     = 0;
//    rules.htx[7].fir_taps[6]     = 0;
//    rules.htx[7].inner_eye1      = 1000;
//    rules.htx[7].inner_eye2      = 2000;

//    rules.htx[8].channel_enable  = true;
//    rules.htx[8].squelch_lock    = false;
//    rules.htx[8].lut_mode        = ODSP_TX_LUT_4TAP;
//    rules.htx[8].ieee_demap      = true;
//    rules.htx[8].gray_mapping    = true;
//    rules.htx[8].invert_chan     = false;
//    rules.htx[8].fir_taps[0]     = -50;
//    rules.htx[8].fir_taps[1]     = 800;
//    rules.htx[8].fir_taps[2]     = 50;
//    rules.htx[8].fir_taps[3]     = 0;
//    rules.htx[8].fir_taps[4]     = 0;
//    rules.htx[8].fir_taps[5]     = 0;
//    rules.htx[8].fir_taps[6]     = 0;
//    rules.htx[8].inner_eye1      = 1000;
//    rules.htx[8].inner_eye2      = 2000;
//    
//    // Configure Line RX rules
//    rules.lrx[1].channel_enable  = true;
//    rules.lrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[1].gray_mapping    = true;
//    rules.lrx[1].ieee_demap      = true;
//    rules.lrx[1].invert_chan     = false;

//    rules.lrx[2].channel_enable  = true;
//    rules.lrx[2].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[2].gray_mapping    = true;
//    rules.lrx[2].ieee_demap      = true;
//    rules.lrx[2].invert_chan     = false;

//    rules.lrx[3].channel_enable  = true;
//    rules.lrx[3].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[3].gray_mapping    = true;
//    rules.lrx[3].ieee_demap      = true;
//    rules.lrx[3].invert_chan     = false;

//    rules.lrx[4].channel_enable  = true;
//    rules.lrx[4].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[4].gray_mapping    = true;
//    rules.lrx[4].ieee_demap      = true;
//    rules.lrx[4].invert_chan     = false;

//    rules.lrx[5].channel_enable  = true;
//    rules.lrx[5].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[5].gray_mapping    = true;
//    rules.lrx[5].ieee_demap      = true;
//    rules.lrx[5].invert_chan     = false;

//    rules.lrx[6].channel_enable  = true;
//    rules.lrx[6].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[6].gray_mapping    = true;
//    rules.lrx[6].ieee_demap      = true;
//    rules.lrx[6].invert_chan     = false;

//    rules.lrx[7].channel_enable  = true;
//    rules.lrx[7].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[7].gray_mapping    = true;
//    rules.lrx[7].ieee_demap      = true;
//    rules.lrx[7].invert_chan     = false;

//    rules.lrx[8].channel_enable  = true;
//    rules.lrx[8].dsp_mode        = ODSP_DSP_MODE_SLC1;
//    rules.lrx[8].gray_mapping    = true;
//    rules.lrx[8].ieee_demap      = true;
//    rules.lrx[8].invert_chan     = false;
//    
//    // Configure Host RX rules
//    rules.hrx[1].channel_enable  = true;
//    rules.hrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[1].gray_mapping    = true;
//    rules.hrx[1].ieee_demap      = true;
//    rules.hrx[1].invert_chan     = false;

//    rules.hrx[2].channel_enable  = true;
//    rules.hrx[2].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[2].gray_mapping    = true;
//    rules.hrx[2].ieee_demap      = true;
//    rules.hrx[2].invert_chan     = false;

//    rules.hrx[3].channel_enable  = true;
//    rules.hrx[3].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[3].gray_mapping    = true;
//    rules.hrx[3].ieee_demap      = true;
//    rules.hrx[3].invert_chan     = false;

//    rules.hrx[4].channel_enable  = true;
//    rules.hrx[4].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[4].gray_mapping    = true;
//    rules.hrx[4].ieee_demap      = true;
//    rules.hrx[4].invert_chan     = false;

//    rules.hrx[5].channel_enable  = true;
//    rules.hrx[5].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[5].gray_mapping    = true;
//    rules.hrx[5].ieee_demap      = true;
//    rules.hrx[5].invert_chan     = false;

//    rules.hrx[6].channel_enable  = true;
//    rules.hrx[6].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[6].gray_mapping    = true;
//    rules.hrx[6].ieee_demap      = true;
//    rules.hrx[6].invert_chan     = false;

//    rules.hrx[7].channel_enable  = true;
//    rules.hrx[7].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[7].gray_mapping    = true;
//    rules.hrx[7].ieee_demap      = true;
//    rules.hrx[7].invert_chan     = false;

//    rules.hrx[8].channel_enable  = true;
//    rules.hrx[8].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
//    rules.hrx[8].gray_mapping    = true;
//    rules.hrx[8].ieee_demap      = true;
//    rules.hrx[8].invert_chan     = false;

//    // Setting the Egress FEC rules
//    rules.eg_fec.enable          = true;
//    rules.eg_fec.mode            = ODSP_FEC_MODE_BYPASS;
//    rules.eg_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_400;
//    rules.eg_fec.num_in_chs      = 8;
//    rules.eg_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
//    rules.eg_fec.num_out_chs     = 8;
//    rules.eg_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
//    rules.eg_fec.stream_chans    = 510;
//    rules.eg_fec.sub_sampling_en = false;
//    rules.eg_fec.awake_cws       = 10;
//    rules.eg_fec.sleep_cws       = 90;
//    
//    // Setting the Ingress FEC rules
//    rules.ig_fec.enable          = true;
//    rules.ig_fec.mode            = ODSP_FEC_MODE_BYPASS;
//    rules.ig_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_400;
//    rules.ig_fec.num_in_chs      = 8;
//    rules.ig_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
//    rules.ig_fec.num_out_chs     = 8;
//    rules.ig_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
//    rules.ig_fec.stream_chans    = 510;
//    rules.ig_fec.sub_sampling_en = false;
//    rules.ig_fec.awake_cws       = 10;
//    rules.ig_fec.sleep_cws       = 90;

//    
////    // Configure Line TX rules
////    rules.ltx[1].channel_enable  = true;
////    rules.ltx[1].squelch_lock    = false;
////    rules.ltx[1].lut_mode        = ODSP_TX_LUT_7TAP_LIN;
////    rules.ltx[1].ieee_demap      = true;
////    rules.ltx[1].gray_mapping    = true;
////    rules.ltx[1].invert_chan     = false;
////    rules.ltx[1].fir_taps[0]     = -50;
////    rules.ltx[1].fir_taps[1]     = 650;
////    rules.ltx[1].fir_taps[2]     = 50;
////    rules.ltx[1].fir_taps[3]     = 0;
////    rules.ltx[1].fir_taps[4]     = 0;
////    rules.ltx[1].fir_taps[5]     = 0;
////    rules.ltx[1].fir_taps[6]     = 0;
////    rules.ltx[1].inner_eye1      = 1000;
////    rules.ltx[1].inner_eye2      = 2000;

////    rules.ltx[2].channel_enable  = true;
////    rules.ltx[2].squelch_lock    = false;
////    rules.ltx[2].lut_mode        = ODSP_TX_LUT_7TAP_LIN;
////    rules.ltx[2].ieee_demap      = true;
////    rules.ltx[2].gray_mapping    = true;
////    rules.ltx[2].invert_chan     = false;
////    rules.ltx[2].fir_taps[0]     = -50;
////    rules.ltx[2].fir_taps[1]     = 650;
////    rules.ltx[2].fir_taps[2]     = 50;
////    rules.ltx[2].fir_taps[3]     = 0;
////    rules.ltx[2].fir_taps[4]     = 0;
////    rules.ltx[2].fir_taps[5]     = 0;
////    rules.ltx[2].fir_taps[6]     = 0;
////    rules.ltx[2].inner_eye1      = 1000;
////    rules.ltx[2].inner_eye2      = 2000;

////    rules.ltx[3].channel_enable  = true;
////    rules.ltx[3].squelch_lock    = false;
////    rules.ltx[3].lut_mode        = ODSP_TX_LUT_7TAP_LIN;
////    rules.ltx[3].ieee_demap      = true;
////    rules.ltx[3].gray_mapping    = true;
////    rules.ltx[3].invert_chan     = false;
////    rules.ltx[3].fir_taps[0]     = -50;
////    rules.ltx[3].fir_taps[1]     = 650;
////    rules.ltx[3].fir_taps[2]     = 50;
////    rules.ltx[3].fir_taps[3]     = 0;
////    rules.ltx[3].fir_taps[4]     = 0;
////    rules.ltx[3].fir_taps[5]     = 0;
////    rules.ltx[3].fir_taps[6]     = 0;
////    rules.ltx[3].inner_eye1      = 1000;
////    rules.ltx[3].inner_eye2      = 2000;

////    rules.ltx[4].channel_enable  = true;
////    rules.ltx[4].squelch_lock    = false;
////    rules.ltx[4].lut_mode        = ODSP_TX_LUT_7TAP_LIN;
////    rules.ltx[4].ieee_demap      = true;
////    rules.ltx[4].gray_mapping    = true;
////    rules.ltx[4].invert_chan     = false;
////    rules.ltx[4].fir_taps[0]     = -50;
////    rules.ltx[4].fir_taps[1]     = 650;
////    rules.ltx[4].fir_taps[2]     = 50;
////    rules.ltx[4].fir_taps[3]     = 0;
////    rules.ltx[4].fir_taps[4]     = 0;
////    rules.ltx[4].fir_taps[5]     = 0;
////    rules.ltx[4].fir_taps[6]     = 0;
////    rules.ltx[4].inner_eye1      = 1000;
////    rules.ltx[4].inner_eye2      = 2000;
////    
////    // Configure Line RX rules
////    rules.lrx[1].channel_enable  = true;
////    rules.lrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
////    rules.lrx[1].gray_mapping    = true;
////    rules.lrx[1].ieee_demap      = true;
////    rules.lrx[1].invert_chan     = false;

////    rules.lrx[2].channel_enable  = true;
////    rules.lrx[2].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
////    rules.lrx[2].gray_mapping    = true;
////    rules.lrx[2].ieee_demap      = true;
////    rules.lrx[2].invert_chan     = false;

////    rules.lrx[3].channel_enable  = true;
////    rules.lrx[3].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
////    rules.lrx[3].gray_mapping    = true;
////    rules.lrx[3].ieee_demap      = true;
////    rules.lrx[3].invert_chan     = false;

////    rules.lrx[4].channel_enable  = true;
////    rules.lrx[4].dsp_mode        = ODSP_DSP_MODE_SLC1_RC_SLC2;
////    rules.lrx[4].gray_mapping    = true;
////    rules.lrx[4].ieee_demap      = true;
////    rules.lrx[4].invert_chan     = false;
//		
//		
//    /**
//     * As ODSP_IMPLICIT_BUNDLES_TEARDOWN is enabled by default, there is no longer a
//     * requirement for the user to call "odsp_init()" prior to calling "odsp_enter_operational_state()"
//     * although user still can if they want to, it won't break anything.
//     * With this flag enabled, API will identify all existing enabled bundles, which contain channels from the 
//     * new bundles, and tells the firmware to tear them down.
//     */
//    // Put the device into operational state
//    status = odsp_bundle_enter_operational_state(die, channel, intf, &rules);
////    if (status != ODSP_OK)
////    {
////        ODSP_CRIT("Error with odsp_bundle_enter_operational_state(die=%d, first_chn=%d, intf=%d, rules)\n", die, first_chn, intf);
////    }		
//			  printf("init_allchannel%d:%d\r\n",channel, status);
//				printf("%4x\r\n",ODSP_EFUSE_EF_ATE__METAL_10__READ(0));
//				
////		status = enable_fec_vdm(die, 1, ODSP_INTF_IG);
////		printf("Ingress fec vdm :%d\r\n", status);
////		status = enable_fec_vdm(die, 1, ODSP_INTF_EG);
////		printf("Egress fec vdm :%d\r\n", status);

////    ODSP_MDELAY(5000);
//    


//		
//    if(status != ODSP_OK)
//    {
//        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
//        return status;
//    }

//    return status;
//}

odsp_status_t init_allchannel_process(uint32_t die )
{
    odsp_rules_t rules;
    e_odsp_operational_mode operating_mode = ODSP_MODE_LINE_PCS;//ODSP_MODE_DUAL_PCS;;//ODSP_MODE_DUAL_PRBS;//ODSP_MODE_MISSION;//ODSP_MODE_LINE_PRBS;//operational_mode = global_operational_mode;//
    e_odsp_protocol_mode    protocol_mode = ODSP_PROT_MODE_400G_8Px26p6_TO_8Px26p6;//ODSP_PROT_MODE_400G_4Px53p1_TO_4Px53p1;//ODSP_PROT_MODE_800G_8Px53p1_TO_8Px53p1;//protocol_mode    = global_protocol_mode;//ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1;
    e_odsp_fec_mode         fec_mode = ODSP_FEC_MODE_BYPASS;//fec_mode         = global_fec_mode;//ODSP_FEC_MODE_BYPASS;
    e_odsp_intf             intf = ODSP_INTF_LINE;//ODSP_INTF_HOST | ODSP_INTF_LINE;//intf             = global_intf;// ODSP_INTF_HOST | ODSP_INTF_LINE;
    odsp_status_t status = ODSP_OK;	

	  uint32_t channel = 1;


    /* Based on Operation mode MISSION and Protocol mode 100G_1Px53p1,
     * the odsp_bundle_rules_default_set will auto-generate rules with below info:
     * - Each bundle has 1 channel enabled for both TX/RX Host/Line side
     * - Signaling: PAM
     * - Baudrate: 53.125Gbd
     * - FEC disabled
     *  ...
     */
    status |= odsp_bundle_rules_default_set(die,
            channel, intf, operating_mode, protocol_mode, fec_mode, &rules);
    if(status != ODSP_OK)
    {
        printf("ERROR: Build default bundle rules failed\n");
        return status;
    }
		
		//clear_all_enabled_channels(&rules);

//    /* Customize channel setting
    // Configure Line TX rules
    rules.ltx[1].channel_enable  = true;
    rules.ltx[1].squelch_lock    = false;
    rules.ltx[1].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[1].ieee_demap      = true;
    rules.ltx[1].gray_mapping    = true;
    rules.ltx[1].invert_chan     = false;
    rules.ltx[1].fir_taps[0]     = -50;
    rules.ltx[1].fir_taps[1]     = 650;
    rules.ltx[1].fir_taps[2]     = 50;
    rules.ltx[1].fir_taps[3]     = 0;
    rules.ltx[1].fir_taps[4]     = 0;
    rules.ltx[1].fir_taps[5]     = 0;
    rules.ltx[1].fir_taps[6]     = 0;
    rules.ltx[1].inner_eye1      = 1000;
    rules.ltx[1].inner_eye2      = 2000;

    rules.ltx[2].channel_enable  = true;
    rules.ltx[2].squelch_lock    = false;
    rules.ltx[2].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[2].ieee_demap      = true;
    rules.ltx[2].gray_mapping    = true;
    rules.ltx[2].invert_chan     = false;
    rules.ltx[2].fir_taps[0]     = -50;
    rules.ltx[2].fir_taps[1]     = 650;
    rules.ltx[2].fir_taps[2]     = 50;
    rules.ltx[2].fir_taps[3]     = 0;
    rules.ltx[2].fir_taps[4]     = 0;
    rules.ltx[2].fir_taps[5]     = 0;
    rules.ltx[2].fir_taps[6]     = 0;
    rules.ltx[2].inner_eye1      = 1000;
    rules.ltx[2].inner_eye2      = 2000;

    rules.ltx[3].channel_enable  = true;
    rules.ltx[3].squelch_lock    = false;
    rules.ltx[3].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[3].ieee_demap      = true;
    rules.ltx[3].gray_mapping    = true;
    rules.ltx[3].invert_chan     = false;
    rules.ltx[3].fir_taps[0]     = -50;
    rules.ltx[3].fir_taps[1]     = 650;
    rules.ltx[3].fir_taps[2]     = 50;
    rules.ltx[3].fir_taps[3]     = 0;
    rules.ltx[3].fir_taps[4]     = 0;
    rules.ltx[3].fir_taps[5]     = 0;
    rules.ltx[3].fir_taps[6]     = 0;
    rules.ltx[3].inner_eye1      = 1000;
    rules.ltx[3].inner_eye2      = 2000;

    rules.ltx[4].channel_enable  = true;
    rules.ltx[4].squelch_lock    = false;
    rules.ltx[4].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[4].ieee_demap      = true;
    rules.ltx[4].gray_mapping    = true;
    rules.ltx[4].invert_chan     = false;
    rules.ltx[4].fir_taps[0]     = -50;
    rules.ltx[4].fir_taps[1]     = 650;
    rules.ltx[4].fir_taps[2]     = 50;
    rules.ltx[4].fir_taps[3]     = 0;
    rules.ltx[4].fir_taps[4]     = 0;
    rules.ltx[4].fir_taps[5]     = 0;
    rules.ltx[4].fir_taps[6]     = 0;
    rules.ltx[4].inner_eye1      = 1000;
    rules.ltx[4].inner_eye2      = 2000;

    rules.ltx[5].channel_enable  = true;
    rules.ltx[5].squelch_lock    = false;
    rules.ltx[5].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[5].ieee_demap      = true;
    rules.ltx[5].gray_mapping    = true;
    rules.ltx[5].invert_chan     = false;
    rules.ltx[5].fir_taps[0]     = -50;
    rules.ltx[5].fir_taps[1]     = 650;
    rules.ltx[5].fir_taps[2]     = 50;
    rules.ltx[5].fir_taps[3]     = 0;
    rules.ltx[5].fir_taps[4]     = 0;
    rules.ltx[5].fir_taps[5]     = 0;
    rules.ltx[5].fir_taps[6]     = 0;
    rules.ltx[5].inner_eye1      = 1000;
    rules.ltx[5].inner_eye2      = 2000;

    rules.ltx[6].channel_enable  = true;
    rules.ltx[6].squelch_lock    = false;
    rules.ltx[6].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[6].ieee_demap      = true;
    rules.ltx[6].gray_mapping    = true;
    rules.ltx[6].invert_chan     = false;
    rules.ltx[6].fir_taps[0]     = -50;
    rules.ltx[6].fir_taps[1]     = 650;
    rules.ltx[6].fir_taps[2]     = 50;
    rules.ltx[6].fir_taps[3]     = 0;
    rules.ltx[6].fir_taps[4]     = 0;
    rules.ltx[6].fir_taps[5]     = 0;
    rules.ltx[6].fir_taps[6]     = 0;
    rules.ltx[6].inner_eye1      = 1000;
    rules.ltx[6].inner_eye2      = 2000;

    rules.ltx[7].channel_enable  = true;
    rules.ltx[7].squelch_lock    = false;
    rules.ltx[7].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[7].ieee_demap      = true;
    rules.ltx[7].gray_mapping    = true;
    rules.ltx[7].invert_chan     = false;
    rules.ltx[7].fir_taps[0]     = -50;
    rules.ltx[7].fir_taps[1]     = 650;
    rules.ltx[7].fir_taps[2]     = 50;
    rules.ltx[7].fir_taps[3]     = 0;
    rules.ltx[7].fir_taps[4]     = 0;
    rules.ltx[7].fir_taps[5]     = 0;
    rules.ltx[7].fir_taps[6]     = 0;
    rules.ltx[7].inner_eye1      = 1000;
    rules.ltx[7].inner_eye2      = 2000;

    rules.ltx[8].channel_enable  = true;
    rules.ltx[8].squelch_lock    = false;
    rules.ltx[8].lut_mode        = ODSP_TX_LUT_4TAP;
    rules.ltx[8].ieee_demap      = true;
    rules.ltx[8].gray_mapping    = true;
    rules.ltx[8].invert_chan     = false;
    rules.ltx[8].fir_taps[0]     = -50;
    rules.ltx[8].fir_taps[1]     = 650;
    rules.ltx[8].fir_taps[2]     = 50;
    rules.ltx[8].fir_taps[3]     = 0;
    rules.ltx[8].fir_taps[4]     = 0;
    rules.ltx[8].fir_taps[5]     = 0;
    rules.ltx[8].fir_taps[6]     = 0;
    rules.ltx[8].inner_eye1      = 1000;
    rules.ltx[8].inner_eye2      = 2000;
    
    // Configure Line RX rules
    rules.lrx[1].channel_enable  = true;
    rules.lrx[1].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[1].gray_mapping    = true;
    rules.lrx[1].ieee_demap      = true;
    rules.lrx[1].invert_chan     = false;

    rules.lrx[2].channel_enable  = true;
    rules.lrx[2].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[2].gray_mapping    = true;
    rules.lrx[2].ieee_demap      = true;
    rules.lrx[2].invert_chan     = false;

    rules.lrx[3].channel_enable  = true;
    rules.lrx[3].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[3].gray_mapping    = true;
    rules.lrx[3].ieee_demap      = true;
    rules.lrx[3].invert_chan     = false;

    rules.lrx[4].channel_enable  = true;
    rules.lrx[4].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[4].gray_mapping    = true;
    rules.lrx[4].ieee_demap      = true;
    rules.lrx[4].invert_chan     = false;

    rules.lrx[5].channel_enable  = true;
    rules.lrx[5].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[5].gray_mapping    = true;
    rules.lrx[5].ieee_demap      = true;
    rules.lrx[5].invert_chan     = false;

    rules.lrx[6].channel_enable  = true;
    rules.lrx[6].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[6].gray_mapping    = true;
    rules.lrx[6].ieee_demap      = true;
    rules.lrx[6].invert_chan     = false;

    rules.lrx[7].channel_enable  = true;
    rules.lrx[7].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[7].gray_mapping    = true;
    rules.lrx[7].ieee_demap      = true;
    rules.lrx[7].invert_chan     = false;

    rules.lrx[8].channel_enable  = true;
    rules.lrx[8].dsp_mode        = ODSP_DSP_MODE_SLC1;
    rules.lrx[8].gray_mapping    = true;
    rules.lrx[8].ieee_demap      = true;
    rules.lrx[8].invert_chan     = false;
    
    // Setting the Egress FEC rules
    rules.eg_fec.enable          = true;
    rules.eg_fec.mode            = ODSP_FEC_MODE_TP_GEN;
    rules.eg_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_400;
    rules.eg_fec.num_in_chs      = 0;
    rules.eg_fec.in_fec_type     = ODSP_FEC_TYPE_UNKNOWN;
    rules.eg_fec.num_out_chs     = 8;
    rules.eg_fec.out_fec_type    = ODSP_FEC_TYPE_KP;
    rules.eg_fec.stream_chans    = 0;
    rules.eg_fec.sub_sampling_en = false;
    rules.eg_fec.awake_cws       = 10;
    rules.eg_fec.sleep_cws       = 90;
    
    // Setting the Ingress FEC rules
    rules.ig_fec.enable          = true;
    rules.ig_fec.mode            = ODSP_FEC_MODE_BYPASS;
    rules.ig_fec.nom_data_rate   = ODSP_NOM_DATA_RATE_400;
    rules.ig_fec.num_in_chs      = 8;
    rules.ig_fec.in_fec_type     = ODSP_FEC_TYPE_KP;
    rules.ig_fec.num_out_chs     = 0;
    rules.ig_fec.out_fec_type    = ODSP_FEC_TYPE_UNKNOWN;
    rules.ig_fec.stream_chans    = 510;
    rules.ig_fec.sub_sampling_en = false;
    rules.ig_fec.awake_cws       = 10;
    rules.ig_fec.sleep_cws       = 90;
		
		
    /**
     * As ODSP_IMPLICIT_BUNDLES_TEARDOWN is enabled by default, there is no longer a
     * requirement for the user to call "odsp_init()" prior to calling "odsp_enter_operational_state()"
     * although user still can if they want to, it won't break anything.
     * With this flag enabled, API will identify all existing enabled bundles, which contain channels from the 
     * new bundles, and tells the firmware to tear them down.
     */
    // Put the device into operational state
    status = odsp_bundle_enter_operational_state(die, channel, intf, &rules);
//    if (status != ODSP_OK)
//    {
//        ODSP_CRIT("Error with odsp_bundle_enter_operational_state(die=%d, first_chn=%d, intf=%d, rules)\n", die, first_chn, intf);
//    }		
			  printf("init_allchannel%d:%d\r\n",channel, status);
				
//		status = enable_fec_vdm(die, 1, ODSP_INTF_IG);
//		printf("Ingress fec vdm :%d\r\n", status);
//		status = enable_fec_vdm(die, 1, ODSP_INTF_EG);
//		printf("Egress fec vdm :%d\r\n", status);

//    ODSP_MDELAY(5000);
    


		
    if(status != ODSP_OK)
    {
        printf("ERROR: odsp_bundle_enter_operational_state failed\n");
        return status;
    }

    return status;
}




