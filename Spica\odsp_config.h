/** @file odsp_config.h
 ****************************************************************************
 *
 * @brief
 *     This module allows individual features in the API to be compiled
 *     in or out to manage code space.
 *
 ****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ***************************************************************************/
#ifndef __ODSP_CONFIG_H__
#define __ODSP_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

#undef ODSP_HAS_FLOATING_POINT

// Set to 1 if to bundle the application firmware with
// the API for programming via odsp_mcu_download_firmware()
#define ODSP_HAS_INLINE_APP_FW        1

// Set to 1 if you want to include support for downloading
// the firmware directly to the IRAM/DRAM 
#define ODSP_HAS_DIRECT_DOWNLOAD      1

// Set to 1 to include support for displaying diagnostic dumps
// This would only be useful on systems with some sort of console
// access.
#define ODSP_HAS_DIAGNOSTIC_DUMPS     1

// Set to 1 to include support for math.h
#define ODSP_HAS_MATH_DOT_H           1

// Set to 1 to include floating point math support
#define ODSP_HAS_FLOATING_POINT       1

// Set to 1 if host has micro-second granulariy delay
#define ODSP_HAS_UDELAY               1

// Turn on/off MCU diagnostic methods
#define ODSP_HAS_MCU_DIAGNOSTICS      1

// Turn on/off the eye monitor methods
#define ODSP_HAS_EYEMON               1

// Turn on/off conservative Inbound PIF reads.
// To speed up verifying the f/w image this
// can be set to 0.
#define ODSP_HAS_INBPIF_READ_POLLING  1

// Set the size of the verify buffer size
// when programming the firmware. This is
// a static buffer. Each entry in the array/buffer
// is 32b in size.
#define ODSP_UCODE_VERIFY_BUFFER_SIZE  32

// Turn on/off the methods for accessing the EEPROM
#define ODSP_HAS_EEPROM_ACCESS 1
#define ODSP_HAS_SHARED_EEPROM 1

#define ODSP_HAS_LOG_NOTE 0
#define ODSP_HAS_LOG_WARN 0
#define ODSP_HAS_LOG_CRIT 0

// Turn on/off logics that use list/block access functions for speed optimization
#if !defined(ODSP_HAS_MULTI_REGS_ACCESS)
#  define ODSP_HAS_MULTI_REGS_ACCESS 0
#endif

#if !defined(ODSP_DONT_USE_STDLIB)
#    if !defined(ODSP_HAS_FILESYSTEM)
#        define ODSP_HAS_FILESYSTEM 1
#    endif
#endif

#if defined(ODSP_DONT_USE_STDLIB)
#    undef ODSP_HAS_FILESYSTEM
#    undef ODSP_HAS_MATH_DOT_H
#    undef ODSP_HAS_DIAGNOSTIC_DUMPS
#    define ODSP_HAS_FILESYSTEM       0
#    define ODSP_HAS_MATH_DOT_H       0
#    define ODSP_HAS_DIAGNOSTIC_DUMPS 0
#endif

// Turn on/off returning error when inputting channels have been in provisioned bundles
#if !defined(ODSP_IMPLICIT_BUNDLES_TEARDOWN)
#   define ODSP_IMPLICIT_BUNDLES_TEARDOWN  1
#endif

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif /* __ODSP_CONFIG_H__ */
