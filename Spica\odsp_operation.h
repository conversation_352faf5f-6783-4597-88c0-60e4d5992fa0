/** @file examples.h
 *****************************************************************************
 *
 * @brief
 *    This module provides examples of using the API to interact with the device.
 *
 *****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ****************************************************************************/
#ifndef __ODSP_OPERATION_H__
#define __ODSP_OPERATION_H__

#include "odsp_api.h"

//// Connect to the eval board to run the examples
//odsp_status_t odsp_dbg_set_remote_server(const char* ip, int port);

//// Close the connection to the eval board (register with atexit)
//void odsp_dbg_close(void);

//// Counts for register accesses
//int odsp_register_write_count(void);
//int odsp_register_read_count(void);

odsp_status_t example_init_program_firmware(uint32_t die);

odsp_status_t init_channel_process(uint32_t die, uint32_t channel);
odsp_status_t init_allchannel_process(uint32_t die);

/**
 * @h2 Initialization Examples
 * ======================================================
 * This example will create 1 bundle 800G-FR4 in Mission mode
 *  All lanes work in non-breakout mode
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_1bundle_1x800g_mission(uint32_t die);

/** This example will create 1 bundle 800G-FR4 in Dual PRBS mode
 *  All lanes work in non-breakout mode
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_1bundle_1x800g_dual_prbs(uint32_t die);

/** This example will create 1 bundle 800G-FR4 in Host PRBS mode
 *  All lanes work in non-breakout mode
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_1bundle_1x800g_host_prbs(uint32_t die);

/** This example will create 1 bundle 800G-FR4 in Line PRBS mode
 *  All lanes work in non-breakout mode
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_1bundle_1x800g_line_prbs(uint32_t die);

/** This example will create 8 bundles 100G-FR4 in Mission mode
 *  All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_8bundles_8x100g_mission(uint32_t die);


/** This example will create 1 bundle 800G-FR4 in Dual PRBS mode
 *  PRBS generator and checker enabled on both Host/Line sides
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_800g_dual_prbs(uint32_t die);

/** This example will create 1 bundle 200G-FR4 and 1 bundle 400G-FR4 in mission mode
 *  All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_bundle_init_200g_400g_mission(uint32_t die);

/**
 * This example will create 2 bundles 400G-FR4 in Mission mode
 * All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_2bundles_2x400g_mission(uint32_t die);

/**
 * This example will create 2 bundles 400G-FR4 in Dual PRBS mode
 * All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_2bundles_2x400g_dual_prbs(uint32_t die);

/**
 * This example will create 2 bundles 200G-FR4 in Mission mode
 * All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_2bundles_2x200g_mission(uint32_t die);

/**
 * This example will create 1 bundles of 400G in Mission mode
 * Host 8x53.125 Gbps PAM to Line 4x106.25 Gbps PAM4
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_1bundle_8x53g_to_4x106g_mission(uint32_t die);

/**
 * This example will create 4 bundles of 100G in Mission mode
 * 4x(Host 2x53.125 Gbps PAM to Line 1x106.25 Gbps PAM4)
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_4bundles_2x53g_to_1x106g_mission(uint32_t die);

/**
 * This example of will create in mission mode:
 *  - 1 bundle 400GbE Host 4x106.25Gbps PAM4 to Line 4x106.25Gbps PAM4
 *  - 4 bundles 100GbE Host 2x53.125 Gbps PAM to Line 1x106.25 Gbps PAM4
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_2bundles_mix_1x400g_4x100g_mission(uint32_t die);

/**
 * This example of will create in mission mode:
 *  - 1 bundle 400GbE Host 4x106.25Gbps PAM4 to Line 4x106.25Gbps PAM4
 *  - 1 bundle 200GbE Host 4x53.125Gbps PAM to Line 4x53.125 Gbps PAM4
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_2bundles_mix_1x200g_1x400g_mission(uint32_t die);

/**
 * This example will create 1 bundles 400G-FR4 in Mission mode
 * All lanes work in non-breakout mode and FEC is disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_init_400g_mission(uint32_t die);

/**
* This example will create 8 bundle 50G in Mission mode
* with 50g kp1 FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_8x50g_kp1_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 2 bundle 200G KP4 in Mission mode
* with 200g kp4 FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_2x200g_kp4_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 2 bundle 100G KR4 in Mission mode
* with 100g kr4 FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_2x100g_kr4_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 4 bundle 200G KP2 in Mission mode
* with 200g kp2 FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_4x200g_kp2_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 2 bundle 400G KP4 in Mission mode
* with 400g kp4 FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_2x400g_kp4_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 1 bundle 400G KP4 and 1 bundle of 200G KP4 in Mission mode
* with 400g and 200g kp4 FEC monitor in turn: enable 400g FECMON to monitor for few period
* then disable the 400g FECMON and enable the 200g FECMON to monitor for few period, ...
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_400g_200g_kp4_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 1 bundle 400G KP4 and 1 bundle of 200G KP4 in Mission mode
* with 400g and 200g kp4 FEC monitor in turn: enable 200g FECMON to monitor for few period
* then disable the 200g FECMON and enable the 400g FECMON to monitor for few period, ...
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_200g_400g_kp4_mission_fec_mon_vdm(uint32_t die);

/**
* This example will create 1 bundle 100G in Mission mode
* with FEC monitor is disabled and then enable FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_1x100g_mission_fec_mon_vdm(uint32_t die);


odsp_status_t enable_1x100g_fec_mon(uint32_t die, e_odsp_intf intf, uint32_t channel, bool enable);
/**
* This example will create 8 bundles of 100G in Mission mode
* with FEC monitor is disabled and then enable 100G FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_8x100g_mission_100g_fec_mon_vdm(uint32_t die);

/**
* This example will create 8 bundles of 100G in Mission mode
* with FEC monitor is disabled and then enable 200G FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_8x100g_mission_4x200g_fec_mon_vdm(uint32_t die);

/**
* This example will create 8 bundles of 100G in Mission mode
* with FEC monitor is disabled and then enable 400G FEC monitor
*
* @param die           [I] - The physical ASIC die being accessed.
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_enable_8x100g_mission_2x400g_fec_mon_vdm(uint32_t die);



/* This helper function will create 1 bundle with SFEC enable, 1 lane per bundle in Mission mode:
* Datapath: 1x(Host 1x26.562500Gb PAM4 <=> Line 1x27.890625Gb PAM4)
*
* @param die           [I] - The physical ASIC die being accessed.*
*
* @return ODSP_OK on success, ODSP_ERROR on failure
*/
odsp_status_t example_1x50g_kp1_mission_sfec(uint32_t die);

/* This helper function will create 2 bundles with SFEC enable in Mission mode:
 * Datapath: 2x(Host 4x26.562500Gb PAM4 <=> Line 4x27.890625Gb PAM4)
 * FEC Disabled
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_2x200g_kp4_mission_sfec(uint32_t die);

//odsp_status_t example_odsp_mode_line_pcs(void);
odsp_status_t init_1x100g_kp1_mission_sfec(uint32_t die, uint32_t channel);
/* This helper function will create 8 bundles with SFEC enable, 1 lane per bundle in Mission mode:
 * Datapath: 8x(Host 1x53.125Gb PAM4 <=> Line 1x55.781250Gb PAM4)

 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_8x100g_kp1_mission_sfec(uint32_t die);

/* This helper function will create 4 bundles with SFEC enable in Mission mode:
 * Datapath: 4x(Host 2x53.125Gb PAM4 <=> Line 2x55.781250Gb PAM4)
 * FEC Disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_4x200g_kp2_mission_sfec(uint32_t die);

/* This helper function will create 2 bundles with SFEC enable in Mission mode:
 * Datapath: 2x(Host 4x53.125Gb PAM4 <=> Line 4x55.781250Gb PAM4)
 * FEC Disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_2x400g_kp4_mission_sfec(uint32_t die);

/* This helper function will create 1 bundle of 400G KP4 SFEC and 1 bundle of 200G KP4 SFEC in Mission mode:
 * Datapath: 1x(Host 4x53.125Gb PAM4 <=> Line 4x55.781250Gb PAM4) +1x (Host 4x26.562500Gb PAM4 <=> Line 4x27.890625Gb PAM4)
 * FEC Disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_400_200g_kp4_mission_sfec(uint32_t die);

/* This helper function will create 1 bundle of 200G KP4 SFEC and 1 bundle of 400G KP4 SFEC in Mission mode:
 * Datapath: 1x(Host 4x26.562500Gb PAM4 <=> Line 4x27.890625Gb PAM4) + 1x(Host 4x53.125Gb PAM4 <=> Line 4x55.781250Gb PAM4)
 * FEC Disabled
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_200_400g_kp4_mission_sfec(uint32_t die);

/* This helper function will create 8 bundles with SFEC+ enable, 1 lane per bundle in Mission mode:
 * Datapath: 8x(Host 1x53.125Gbps PAM4 <=> Line 1x56.666666Gbps PAM4)
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_8x100g_kp1_mission_sfec_plus(uint32_t die);

/* This helper function will create 8 bundles with SFEC+ enable, 1 lane per bundle in Mission mode:
 * Datapath: 4x(Host 2x53.125Gb PAM4 <=> Line 2x56.666666Gb PAM4)
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_4x200g_kp2_mission_sfec_plus(uint32_t die);

/* This helper function will create 2 bundles with SFEC+ enable, 4 lanes per bundle in Mission mode:
 * Datapath: 2x(Host 4x53.125Gb PAM4 <=> Line 4x56.666666Gb PAM4)
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_2x400g_kp4_mission_sfec_plus(uint32_t die);

/* This helper function will create 1 bundle of 200G KP4 with SFEC, and 1 bundle of 400G KP4 SFEC PLUS in Mission mode:
 * Datapath: (Host 4x26.562500Gb PAM4 <=> Line 4x27.890625Gb PAM4) + (Host 4x53.125Gb PAM4 <=> Line 4x56.666666Gb PAM4)
 *
 * @param die           [I] - The physical ASIC die being accessed.
 *
 * @return ODSP_OK on success, ODSP_ERROR on failure
 */
odsp_status_t example_200_400g_kp4_mission_sfec_plus(uint32_t die);


odsp_status_t fec_stats_poller_request(uint32_t die, uint32_t channel, e_odsp_intf intf);

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif // __EXAMPLES_H__

