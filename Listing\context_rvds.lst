


ARM Macro Assembler    Page 1 


    1 00000000         ;/*
    2 00000000         ;* Copyright (c) 2006-2018, RT-Thread Development Team
    3 00000000         ;*
    4 00000000         ;* SPDX-License-Identifier: Apache-2.0
    5 00000000         ;*
    6 00000000         ; * Change Logs:
    7 00000000         ; * Date           Author       Notes
    8 00000000         ; * 2009-01-17     Bernard      first version.
    9 00000000         ; * 2012-01-01     aozima       support context switch l
                       oad/store FPU register.
   10 00000000         ; * 2013-06-18     aozima       add restore MSP feature.
                       
   11 00000000         ; * 2013-06-23     aozima       support lazy stack optim
                       ized.
   12 00000000         ; * 2018-07-24     aozima       enhancement hard fault e
                       xception handler.
   13 00000000         ; */
   14 00000000         
   15 00000000         ;/**
   16 00000000         ; * @addtogroup cortex-m4
   17 00000000         ; */
   18 00000000         ;/*@{*/
   19 00000000         
   20 00000000 E000ED08 
                       SCB_VTOR
                               EQU              0xE000ED08  ; Vector Table Offs
                                                            et Register
   21 00000000 E000ED04 
                       NVIC_INT_CTRL
                               EQU              0xE000ED04  ; interrupt control
                                                             state register
   22 00000000 E000ED20 
                       NVIC_SYSPRI2
                               EQU              0xE000ED20  ; system priority r
                                                            egister (2)
   23 00000000 00FF0000 
                       NVIC_PENDSV_PRI
                               EQU              0x00FF0000  ; PendSV priority v
                                                            alue (lowest)
   24 00000000 10000000 
                       NVIC_PENDSVSET
                               EQU              0x10000000  ; value to trigger 
                                                            PendSV exception
   25 00000000         
   26 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   27 00000000                 THUMB
   28 00000000                 REQUIRE8
   29 00000000                 PRESERVE8
   30 00000000         
   31 00000000                 IMPORT           rt_thread_switch_interrupt_flag
   32 00000000                 IMPORT           rt_interrupt_from_thread
   33 00000000                 IMPORT           rt_interrupt_to_thread
   34 00000000         
   35 00000000         ;/*
   36 00000000         ; * rt_base_t rt_hw_interrupt_disable();
   37 00000000         ; */
   38 00000000         rt_hw_interrupt_disable
                               PROC



ARM Macro Assembler    Page 2 


   39 00000000                 EXPORT           rt_hw_interrupt_disable
   40 00000000 F3EF 8010       MRS              r0, PRIMASK
   41 00000004 B672            CPSID            I
   42 00000006 4770            BX               LR
   43 00000008                 ENDP
   44 00000008         
   45 00000008         ;/*
   46 00000008         ; * void rt_hw_interrupt_enable(rt_base_t level);
   47 00000008         ; */
   48 00000008         rt_hw_interrupt_enable
                               PROC
   49 00000008                 EXPORT           rt_hw_interrupt_enable
   50 00000008 F380 8810       MSR              PRIMASK, r0
   51 0000000C 4770            BX               LR
   52 0000000E                 ENDP
   53 0000000E         
   54 0000000E         ;/*
   55 0000000E         ; * void rt_hw_context_switch(rt_uint32 from, rt_uint32 
                       to);
   56 0000000E         ; * r0 --> from
   57 0000000E         ; * r1 --> to
   58 0000000E         ; */
   59 0000000E         rt_hw_context_switch_interrupt
   60 0000000E                 EXPORT           rt_hw_context_switch_interrupt
   61 0000000E         rt_hw_context_switch
                               PROC
   62 0000000E                 EXPORT           rt_hw_context_switch
   63 0000000E         
   64 0000000E         ; set rt_thread_switch_interrupt_flag to 1
   65 0000000E 4A43            LDR              r2, =rt_thread_switch_interrupt
_flag
   66 00000010 6813            LDR              r3, [r2]
   67 00000012 2B01            CMP              r3, #1
   68 00000014 D004            BEQ              _reswitch
   69 00000016 F04F 0301       MOV              r3, #1
   70 0000001A 6013            STR              r3, [r2]
   71 0000001C         
   72 0000001C 4A40            LDR              r2, =rt_interrupt_from_thread ;
                                                             set rt_interrupt_f
                                                            rom_thread
   73 0000001E 6010            STR              r0, [r2]
   74 00000020         
   75 00000020         _reswitch
   76 00000020 4A40            LDR              r2, =rt_interrupt_to_thread ; s
                                                            et rt_interrupt_to_
                                                            thread
   77 00000022 6011            STR              r1, [r2]
   78 00000024         
   79 00000024 4840            LDR              r0, =NVIC_INT_CTRL ; trigger th
                                                            e PendSV exception 
                                                            (causes context swi
                                                            tch)
   80 00000026 F04F 5180       LDR              r1, =NVIC_PENDSVSET
   81 0000002A 6001            STR              r1, [r0]
   82 0000002C 4770            BX               LR
   83 0000002E                 ENDP
   84 0000002E         
   85 0000002E         ; r0 --> switch from thread stack
   86 0000002E         ; r1 --> switch to thread stack



ARM Macro Assembler    Page 3 


   87 0000002E         ; psr, pc, lr, r12, r3, r2, r1, r0 are pushed into [from
                       ] stack
   88 0000002E         PendSV_Handler
                               PROC
   89 0000002E                 EXPORT           PendSV_Handler
   90 0000002E         
   91 0000002E         ; disable interrupt to protect context switch
   92 0000002E F3EF 8210       MRS              r2, PRIMASK
   93 00000032 B672            CPSID            I
   94 00000034         
   95 00000034         ; get rt_thread_switch_interrupt_flag
   96 00000034 4839            LDR              r0, =rt_thread_switch_interrupt
_flag
   97 00000036 6801            LDR              r1, [r0]
   98 00000038 B351            CBZ              r1, pendsv_exit ; pendsv alread
                                                            y handled
   99 0000003A         
  100 0000003A         ; clear rt_thread_switch_interrupt_flag to 0
  101 0000003A F04F 0100       MOV              r1, #0x00
  102 0000003E 6001            STR              r1, [r0]
  103 00000040         
  104 00000040 4837            LDR              r0, =rt_interrupt_from_thread
  105 00000042 6801            LDR              r1, [r0]
  106 00000044 B191            CBZ              r1, switch_to_thread ; skip reg
                                                            ister save at the f
                                                            irst time
  107 00000046         
  108 00000046 F3EF 8109       MRS              r1, psp     ; get from thread s
                                                            tack pointer
  109 0000004A         
  110 0000004A                 IF               {FPU} != "SoftVFP"
  111 0000004A F01E 0F10       TST              lr, #0x10   ; if(!EXC_RETURN[4]
                                                            )
  112 0000004E BF08 ED21 
              8B10             VSTMFDEQ         r1!, {d8 - d15} ; push FPU regi
                                                            ster s16~s31
  113 00000054                 ENDIF
  114 00000054         
  115 00000054 E921 0FF0       STMFD            r1!, {r4 - r11} ; push r4 - r11
                                                             register
  116 00000058         
  117 00000058                 IF               {FPU} != "SoftVFP"
  118 00000058 F04F 0400       MOV              r4, #0x00   ; flag = 0
  119 0000005C         
  120 0000005C F01E 0F10       TST              lr, #0x10   ; if(!EXC_RETURN[4]
                                                            )
  121 00000060 BF08 2401       MOVEQ            r4, #0x01   ; flag = 1
  122 00000064         
  123 00000064 F841 4D04       STMFD            r1!, {r4}   ; push flag
  124 00000068                 ENDIF
  125 00000068         
  126 00000068 6800            LDR              r0, [r0]
  127 0000006A 6001            STR              r1, [r0]    ; update from threa
                                                            d stack pointer
  128 0000006C         
  129 0000006C         switch_to_thread
  130 0000006C 492D            LDR              r1, =rt_interrupt_to_thread
  131 0000006E 6809            LDR              r1, [r1]
  132 00000070 6809            LDR              r1, [r1]    ; load thread stack



ARM Macro Assembler    Page 4 


                                                             pointer
  133 00000072         
  134 00000072                 IF               {FPU} != "SoftVFP"
  135 00000072 C908            LDMFD            r1!, {r3}   ; pop flag
  136 00000074                 ENDIF
  137 00000074         
  138 00000074 E8B1 0FF0       LDMFD            r1!, {r4 - r11} ; pop r4 - r11 
                                                            register
  139 00000078         
  140 00000078                 IF               {FPU} != "SoftVFP"
  141 00000078 2B00            CMP              r3,  #0     ; if(flag_r3 != 0)
  142 0000007A BF18 ECB1 
              8B10             VLDMFDNE         r1!, {d8 - d15} ; pop FPU regis
                                                            ter s16~s31
  143 00000080                 ENDIF
  144 00000080         
  145 00000080 F381 8809       MSR              psp, r1     ; update stack poin
                                                            ter
  146 00000084         
  147 00000084                 IF               {FPU} != "SoftVFP"
  148 00000084 F04E 0E10       ORR              lr, lr, #0x10 ; lr |=  (1 << 4)
                                                            , clean FPCA.
  149 00000088 2B00            CMP              r3,  #0     ; if(flag_r3 != 0)
  150 0000008A BF18 F02E 
              0E10             BICNE            lr, lr, #0x10 ; lr &= ~(1 << 4)
                                                            , set FPCA.
  151 00000090                 ENDIF
  152 00000090         
  153 00000090         pendsv_exit
  154 00000090         ; restore interrupt
  155 00000090 F382 8810       MSR              PRIMASK, r2
  156 00000094         
  157 00000094 F04E 0E04       ORR              lr, lr, #0x04
  158 00000098 4770            BX               lr
  159 0000009A                 ENDP
  160 0000009A         
  161 0000009A         ;/*
  162 0000009A         ; * void rt_hw_context_switch_to(rt_uint32 to);
  163 0000009A         ; * r0 --> to
  164 0000009A         ; * this fucntion is used to perform the first thread sw
                       itch
  165 0000009A         ; */
  166 0000009A         rt_hw_context_switch_to
                               PROC
  167 0000009A                 EXPORT           rt_hw_context_switch_to
  168 0000009A         ; set to thread
  169 0000009A 4922            LDR              r1, =rt_interrupt_to_thread
  170 0000009C 6008            STR              r0, [r1]
  171 0000009E         
  172 0000009E                 IF               {FPU} != "SoftVFP"
  173 0000009E         ; CLEAR CONTROL.FPCA
  174 0000009E F3EF 8214       MRS              r2, CONTROL ; read
  175 000000A2 F022 0204       BIC              r2, #0x04   ; modify
  176 000000A6 F382 8814       MSR              CONTROL, r2 ; write-back
  177 000000AA                 ENDIF
  178 000000AA         
  179 000000AA         ; set from thread to 0
  180 000000AA 491D            LDR              r1, =rt_interrupt_from_thread
  181 000000AC F04F 0000       MOV              r0, #0x0



ARM Macro Assembler    Page 5 


  182 000000B0 6008            STR              r0, [r1]
  183 000000B2         
  184 000000B2         ; set interrupt flag to 1
  185 000000B2 491A            LDR              r1, =rt_thread_switch_interrupt
_flag
  186 000000B4 F04F 0001       MOV              r0, #1
  187 000000B8 6008            STR              r0, [r1]
  188 000000BA         
  189 000000BA         ; set the PendSV exception priority
  190 000000BA 481C            LDR              r0, =NVIC_SYSPRI2
  191 000000BC F44F 017F       LDR              r1, =NVIC_PENDSV_PRI
  192 000000C0 F8D0 2000       LDR.W            r2, [r0,#0x00] ; read
  193 000000C4 EA41 0102       ORR              r1,r1,r2    ; modify
  194 000000C8 6001            STR              r1, [r0]    ; write-back
  195 000000CA         
  196 000000CA         ; trigger the PendSV exception (causes context switch)
  197 000000CA 4817            LDR              r0, =NVIC_INT_CTRL
  198 000000CC F04F 5180       LDR              r1, =NVIC_PENDSVSET
  199 000000D0 6001            STR              r1, [r0]
  200 000000D2         
  201 000000D2         ; restore MSP
  202 000000D2 4817            LDR              r0, =SCB_VTOR
  203 000000D4 6800            LDR              r0, [r0]
  204 000000D6 6800            LDR              r0, [r0]
  205 000000D8 F380 8808       MSR              msp, r0
  206 000000DC         
  207 000000DC         ; enable interrupts at processor level
  208 000000DC B661            CPSIE            F
  209 000000DE B662            CPSIE            I
  210 000000E0         
  211 000000E0         ; never reach here!
  212 000000E0                 ENDP
  213 000000E0         
  214 000000E0         ; compatible with old version
  215 000000E0         rt_hw_interrupt_thread_switch
                               PROC
  216 000000E0                 EXPORT           rt_hw_interrupt_thread_switch
  217 000000E0 4770            BX               lr
  218 000000E2                 ENDP
  219 000000E2         
  220 000000E2                 IMPORT           rt_hw_hard_fault_exception
  221 000000E2                 EXPORT           HardFault_Handler
  222 000000E2         HardFault_Handler
                               PROC
  223 000000E2         
  224 000000E2         ; get current context
  225 000000E2 F01E 0F04       TST              lr, #0x04   ; if(!EXC_RETURN[2]
                                                            )
  226 000000E6 BF0C            ITE              EQ
  227 000000E8 F3EF 8008       MRSEQ            r0, msp     ; [2]=0 ==> Z=1, ge
                                                            t fault context fro
                                                            m handler.
  228 000000EC F3EF 8009       MRSNE            r0, psp     ; [2]=1 ==> Z=0, ge
                                                            t fault context fro
                                                            m thread.
  229 000000F0         
  230 000000F0 E920 0FF0       STMFD            r0!, {r4 - r11} ; push r4 - r11
                                                             register
  231 000000F4                 IF               {FPU} != "SoftVFP"



ARM Macro Assembler    Page 6 


  232 000000F4 F840 ED04       STMFD            r0!, {lr}   ; push dummy for fl
                                                            ag
  233 000000F8                 ENDIF
  234 000000F8 F840 ED04       STMFD            r0!, {lr}   ; push exec_return 
                                                            register
  235 000000FC         
  236 000000FC F01E 0F04       TST              lr, #0x04   ; if(!EXC_RETURN[2]
                                                            )
  237 00000100 BF0C            ITE              EQ
  238 00000102 F380 8808       MSREQ            msp, r0     ; [2]=0 ==> Z=1, up
                                                            date stack pointer 
                                                            to MSP.
  239 00000106 F380 8809       MSRNE            psp, r0     ; [2]=1 ==> Z=0, up
                                                            date stack pointer 
                                                            to PSP.
  240 0000010A         
  241 0000010A B500            PUSH             {lr}
  242 0000010C F7FF FFFE       BL               rt_hw_hard_fault_exception
  243 00000110 F85D EB04       POP              {lr}
  244 00000114         
  245 00000114 F04E 0E04       ORR              lr, lr, #0x04
  246 00000118 4770            BX               lr
  247 0000011A                 ENDP
  248 0000011A         
  249 0000011A 00 00           ALIGN            4
  250 0000011C         
  251 0000011C                 END
              00000000 
              00000000 
              00000000 
              E000ED04 
              E000ED20 
              E000ED08 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=..\output\context_rvds.d -o..\output\context_rvds.o -IDriver
s\CMSIS\Device\ST\STM32F4xx\Include --predefine="__MICROLIB SETA 1" --predefine
="__UVISION_VERSION SETA 538" --list=..\listing\context_rvds.lst ..\rt-thread\3
.1.3\libcpu\arm\cortex-m4\context_rvds.S



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 26 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      None
Comment: .text unused
HardFault_Handler 000000E2

Symbol: HardFault_Handler
   Definitions
      At line 222 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 221 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: HardFault_Handler used once
PendSV_Handler 0000002E

Symbol: PendSV_Handler
   Definitions
      At line 88 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 89 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: PendSV_Handler used once
_reswitch 00000020

Symbol: _reswitch
   Definitions
      At line 75 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 68 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: _reswitch used once
pendsv_exit 00000090

Symbol: pendsv_exit
   Definitions
      At line 153 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 98 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: pendsv_exit used once
rt_hw_context_switch 0000000E

Symbol: rt_hw_context_switch
   Definitions
      At line 61 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 62 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: rt_hw_context_switch used once
rt_hw_context_switch_interrupt 0000000E

Symbol: rt_hw_context_switch_interrupt
   Definitions
      At line 59 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 60 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: rt_hw_context_switch_interrupt used once



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

rt_hw_context_switch_to 0000009A

Symbol: rt_hw_context_switch_to
   Definitions
      At line 166 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 167 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: rt_hw_context_switch_to used once
rt_hw_interrupt_disable 00000000

Symbol: rt_hw_interrupt_disable
   Definitions
      At line 38 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 39 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: rt_hw_interrupt_disable used once
rt_hw_interrupt_enable 00000008

Symbol: rt_hw_interrupt_enable
   Definitions
      At line 48 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 49 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
Comment: rt_hw_interrupt_enable used once
rt_hw_interrupt_thread_switch 000000E0

Symbol: rt_hw_interrupt_thread_switch
   Definitions
      At line 215 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 216 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: rt_hw_interrupt_thread_switch used once
switch_to_thread 0000006C

Symbol: switch_to_thread
   Definitions
      At line 129 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 106 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: switch_to_thread used once
12 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

NVIC_INT_CTRL E000ED04

Symbol: NVIC_INT_CTRL
   Definitions
      At line 21 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 79 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 197 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S

NVIC_PENDSVSET 10000000

Symbol: NVIC_PENDSVSET
   Definitions
      At line 24 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 80 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 198 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S

NVIC_PENDSV_PRI 00FF0000

Symbol: NVIC_PENDSV_PRI
   Definitions
      At line 23 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 191 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: NVIC_PENDSV_PRI used once
NVIC_SYSPRI2 E000ED20

Symbol: NVIC_SYSPRI2
   Definitions
      At line 22 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 190 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: NVIC_SYSPRI2 used once
SCB_VTOR E000ED08

Symbol: SCB_VTOR
   Definitions
      At line 20 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 202 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: SCB_VTOR used once
5 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

rt_hw_hard_fault_exception 00000000

Symbol: rt_hw_hard_fault_exception
   Definitions
      At line 220 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
   Uses
      At line 242 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
Comment: rt_hw_hard_fault_exception used once
rt_interrupt_from_thread 00000000

Symbol: rt_interrupt_from_thread
   Definitions
      At line 32 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 72 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 104 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
      At line 180 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S

rt_interrupt_to_thread 00000000

Symbol: rt_interrupt_to_thread
   Definitions
      At line 33 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 76 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 130 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S
      At line 169 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S

rt_thread_switch_interrupt_flag 00000000

Symbol: rt_thread_switch_interrupt_flag
   Definitions
      At line 31 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
   Uses
      At line 65 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 96 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.S
      At line 185 in file ..\rt-thread\3.1.3\libcpu\arm\cortex-m4\context_rvds.
S

4 symbols
356 symbols in table
