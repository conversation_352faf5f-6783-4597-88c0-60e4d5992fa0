/** @file odsp_rules.h
 ****************************************************************************
 *
 * @brief
 *     This module describes the high level configuration rules for the API.
 *
 ****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ***************************************************************************/
#ifndef __ODSP_RULES_H__
#define __ODSP_RULES_H__

#include "odsp_rtos.h"

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

/**
 * @h2 ASIC Package and Channel Utilities
 * =======================================================
 *
 * @brief
 * Maximum number of channels
 */
#define ODSP_MAX_CHANNELS 8

/** Maximum number of bundles */
#define ODSP_MAX_BUNDLES 8

/** Max size of histogram array */
#define ODSP_HIST_DATA_SIZE_MAX  80

/** Max number of FFE TAP */
#define ODSP_FFE_TAP_COUNT 15

/** Max number of RC TAP */
#define ODSP_RC_TAP_COUNT 16

/** 
 * Maximum number of FEC streams 
 * @private
 */
#define ODSP_MAX_FEC_STREAMS 8

/** 
 * Maximum number of FEC lanes 
 * @private
 */
#define ODSP_MAX_FEC_LANES   32

/** 
 * The maximum words of output ADC histogram data 
 * @private
 */
#define ODSP_ADC_BIN_SIZE 64

/**
 * @h2 High Level Configuration Rules
 * =======================================================
 * The following section describes the public types and structures used by the API
 *
 * @brief
 * The operational mode of the ASIC
 */
typedef enum
{
    /**
     * Mission mode.
     * This mode is previously known as Duplex Retimer mode.
     *
     * This is the main operational mode of the device.
     * It configures the ASIC to pass traffic straight
     * through the ingress and egress channels.
     *
     * @{pre,
     *   HOST (PAM)         LINE (PAM)
     *      +-------------------+
     *      !      +-----+      !
     *   >>----------------------->>
     *      !      !     !      !
     *      !      !     !      !
     *      !      !     !      !
     *   <<-----------------------<<
     *      !      +-----+      !
     *      +-------------------+
     * }
     */
    ODSP_MODE_MISSION  = 0,

    /**
     * Line PRBS mode.
     *
     * This is a diagnostic mode that configures the device to send PRBS
     * traffic out the line transmitters, with the clock sourced from the
     * reference. This may be looped back to the
     * device via an external loopback to check PRBS on the line receiver.
     * All interfaces are setup for PAM4 signalling by default.
     *
     * @{pre,
     *   HOST               LINE (PAM)
     *      +-------------------+
     *      !      +-----+      !
     *      !      !     ! PRBS---->>
     *      !      !     !      !
     *      !      !     !      !
     *      !      !     !      !
     *      !      !     !   <<------
     *      !      +-----+      !
     *      +-------------------+
     * }
     */
    ODSP_MODE_LINE_PRBS = 3,

    /**
     * Host PRBS mode.
     *
     * This is a diagnostic mode that configures the device to send PRBS
     * traffic out the host bundle, with the clock sourced from the
     * reference. This may be looped back to the
     * device via an external loopback to check PRBS on the host receiver.
     * On gearbox packages, host interfaces are setup for NRZ by default.
     * On non-gearbox packages, host interfaces are setup for PAM4 by default.
     *
     * @{pre,
     *   HOST (PAM/NRZ)       LINE
     *       +-------------------+
     *       !      +-----+      !
     *   ------>>   !     !      !
     *       !      !     !      !
     *       !      !     !      !
     *       !      !     !      !
     *   <<----PRBS !     !      !
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_HOST_PRBS = 4,

    /**
     * Dual PRBS mode.
     *
     * This is a diagnostic mode that configures the device to send PRBS
     * traffic out the host and line interfaces, with the clock sourced from
     * the reference. The host and line interfaces
     * may be looped back to the device via external loopbacks to check PRBS
     * on the their respective interfaces.
     * On gearbox packages, host interfaces are setup for NRZ by default.
     * On non-gearbox packages, host interfaces are setup for PAM4 by default.
     * Line interfaces are setup for PAM4 on all packages by default.
     *
     * @{pre,
     *   HOST (PAM/NRZ)       LINE (PAM)
     *       +-------------------+
     *       !      +-----+      !
     *   ------>>   !     ! PRBS---->>
     *       !      !     !      !
     *       !      !     !      !
     *       !      !     !      !
     *   <<----PRBS !     !   <<------
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_DUAL_PRBS = 5,

    /**
     * Shallow line loopback.
     *
     * In this mode only the line side bundle is used, the host interfaces are disabled.
     * The data received on the line side receivers is looped back in
     * the digital to the line side transmitters.
     * This mode can also be referred to as a line side shallow digital loopback.
     *
     * @{pre,
     *   HOST               LINE (PAM)
     *      +-------------------+
     *      !      +-----+      !
     *      !      !     !   +----->>
     *      !      !     !   !  !
     *      !      !     !   !  !
     *      !      !     !   !  !
     *      !      !     !   +-----<<
     *      !      +-----+      !
     *      +-------------------+
     * }
     */
    ODSP_MODE_SHALLOW_LINE_LOOPBACK = 6,

    /**
     * Shallow host loopback.
     *
     * In this mode only the host side bundle is used, the line interfaces are disabled.
     * The data received on the host side receivers is looped back in
     * the digital to the host side transmitters.
     * This mode can also be referred to as a host side shallow digital loopback.
     *
     * @{pre,
     *    HOST (PAM/NRZ)     LINE
     *       +-------------------+
     *       !      +-----+      !
     *   >>------+  !     !      !
     *       !   !  !     !      !
     *       !   !  !     !      !
     *       !   !  !     !      !
     *   <<------+  !     !      !
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_SHALLOW_HOST_LOOPBACK = 7,

    /**
     * Ingress PRBS mode.
     *
     * This is a diagnostic mode that configures the device to send PRBS
     * traffic out the line Tx interfaces and check the PRBS traffic coming
     * into the host Rx interface, with the clock sourced from
     * the reference.
     *
     * @{pre,
     *   HOST (PAM/NRZ)       LINE (PAM)
     *       +-------------------+
     *       !      +-----+      !
     *   --->> PRBS !     ! PRBS---->>
     *       !      !     !      !
     *       !      !     !      !
     *       !      !     !      !
     *   <<-------------------------<<
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_INGRESS_PRBS = 9,

    /**
     * Egress PRBS mode.
     *
     * This is a diagnostic mode that configures the device to send PRBS
     * traffic out the host Tx interfaces and check the PRBS traffic coming
     * into the line Rx interface, with the clock sourced from
     * the reference.
     *
     * @{pre,
     *   HOST (PAM/NRZ)       LINE (PAM)
     *       +-------------------+
     *       !      +-----+      !
     *   >>------------------------->>
     *       !      !     !      !
     *       !      !     !      !
     *       !      !     !      !
     *   <<----PRBS !     ! PRBS <<---
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_EGRESS_PRBS = 10,

    /**
     * Line PCS mode.
     *
     * This is a diagnostic mode that configures the device to send framed
     * traffic from the egress FP, out the line transmitters, with the clock sourced from the
     * reference. This may be looped back to the
     * device via an external loopback to the line receiver and back into the ingress FP.
     * This mode support maximum 400Gbps PCS data.
     *
     * @{pre,
     * HOST                       LINE
     *      +-------------------+
     *      !      +-----+      !
     *      !      ! PCS------------>>
     *      !      !     !      !
     *      !      !  FP !      !
     *      !      !     !      !
     *      !      !   <<-------------
     *      !      +-----+      !
     *      +-------------------+
     * }
     */
    ODSP_MODE_LINE_PCS = 14,

    /**
     * Host PCS mode.
     *
     * This is a diagnostic mode that configures the device to send framed
     * traffic from the ingress FP, out the host transmitters, with the clock sourced from the
     * reference. This may be looped back to the
     * device via an external loopback to the host receiver and back into the egress FP.
     * This mode support maximum 400Gbps PCS data.
     *
     * @{pre,
     * HOST                       LINE
     *       +-------------------+
     *       !      +-----+      !
     *   ------------->>  !      !
     *       !      !     !      !
     *       !      !  FP !      !
     *       !      !     !      !
     *   <<-----------PCS !      !
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_HOST_PCS = 15,

    /**
     * Dual PCS mode.
     *
     * This is a diagnostic mode that configures the device to send PCS
     * traffic out the host and line interfaces, with the clock sourced from
     * the reference. The host and line interfaces
     * may be looped back to the device via external loopbacks to check FEC MON
     * on the their respective interfaces.
     *
     * @{pre,
     * HOST                       LINE
     *       +-------------------+
     *       !      +-----+      !
     *   ----------->> PCS----------->>
     *       !      !     !      !
     *       !      !  FP !      !
     *       !      !     !      !
     *   <<----------PCS <<-----------
     *       !      +-----+      !
     *       +-------------------+
     * }
     */
    ODSP_MODE_DUAL_PCS = 16,

    /** Last mode placeholder */
    ODSP_MODE_END,

} e_odsp_operational_mode;

/**
 * Forward Error Correction (FEC) Modes
 * The FEC mode defines the overall flow of data through the FEC blocks.
 *
 * It can be thought of as the output config; whether data is routed around
 * the core (ODSP_FEC_MODE_BYPASS) or regenerated in the core (ODSP_FEC_MODE_REGEN).
 * Only FEC_BYPASS mode is supported on Porrima Gen4 device.
 *
 */
typedef enum
{
    /** No RS-FEC encode/decode, output data is from bypass path.
     * Data agnostic, data type can be anything
     */
    ODSP_FEC_MODE_BYPASS = 0,

    /** FEC core generates data. Data output is the FEC PCS test-pattern (IDLE/RF/LF) and/or in-band
     * packets. Data is encoded with RS-FEC.
     * Maximum 400Gbps data is supported.This feature is enabled for only one FEC stream.
     */
    ODSP_FEC_MODE_TP_GEN = 0x2,

    /** FEC Unknown */
    ODSP_FEC_MODE_UNKNOWN

} e_odsp_fec_mode;

/**
 * Data/FEC types
 */
typedef enum
{
    /**
     * Reed-Solomon Forward Error Correction Clause 91
     * RS(528,514) FEC
     */
    ODSP_FEC_TYPE_RS528,
    ODSP_FEC_TYPE_KR = ODSP_FEC_TYPE_RS528,

    /**
     * Reed-Solomon Forward Error Correction Clause 91
     * RS(544,514) FEC
     */
    ODSP_FEC_TYPE_RS544,
    ODSP_FEC_TYPE_KP = ODSP_FEC_TYPE_RS544,

    /**
     * Reed-Solomon Forward Error Correction Clause 161
     * RS(544,514) FEC Interleave
     */
    ODSP_FEC_TYPE_RS544_INT,
    ODSP_FEC_TYPE_CK = ODSP_FEC_TYPE_RS544_INT,

    /** FEC invalid mode */
    ODSP_FEC_TYPE_UNKNOWN
} e_odsp_fec_type;

/**
 * Helper enum for the nominal fec data rates.
 */
typedef enum
{
    /** 800G */
    ODSP_NOM_DATA_RATE_800 = 0,
    /** 400G */
    ODSP_NOM_DATA_RATE_400  = 1,
    /** 200G */
    ODSP_NOM_DATA_RATE_200  = 2,
    /** 100G */
    ODSP_NOM_DATA_RATE_100 = 3,
    /** 50G */
    ODSP_NOM_DATA_RATE_50 = 4,
    /** 25G */
    ODSP_NOM_DATA_RATE_25 = 5,

    /** Invalid rate */
    ODSP_NOM_DATA_RATE_UNKNOWN,
} e_odsp_nom_data_rate;

/**
 * FEC protocol modes
 */
typedef enum
{
    /** 100G RS544 (interleave) 802.3ck: in_fec_type in fec_rules is ODSP_FEC_TYPE_CK */
    ODSP_FEC_PROTOCOL_MODE_100G_CK    = 0,
    /** 100G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_KR */
    ODSP_FEC_PROTOCOL_MODE_100G_RS528 = 1,
    /** 100G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_KP */
    ODSP_FEC_PROTOCOL_MODE_100G_RS544 = 2,
    /** 200G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_CK */
    ODSP_FEC_PROTOCOL_MODE_200G_RS544 = 3,
    /** 400G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_CK */
    ODSP_FEC_PROTOCOL_MODE_400G_RS544 = 4,
    /** 25G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_KR */
    ODSP_FEC_PROTOCOL_MODE_25G_RS528 = 5,
    /** 50G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_KR */
    ODSP_FEC_PROTOCOL_MODE_50G_RS528 = 6,
    /** 50G RS528: in_fec_type in fec_rules is ODSP_FEC_TYPE_KP */
    ODSP_FEC_PROTOCOL_MODE_50G_RS544 = 7,
    /** 800G RS544 (Ethernet Consortium):  in_fec_type in fec_rules is ODSP_FEC_TYPE_CK*/
    ODSP_FEC_PROTOCOL_MODE_800G_RS544 = 8
} e_odsp_fec_protocol_mode;

/**
 * These are Ingress and Egress interface definitions used by the FEC block
 */
typedef enum
{
    /** Egress FEC */
    ODSP_FEC_INTF_EG = 0,
    /** Ingress FEC */
    ODSP_FEC_INTF_IG = 1,
    /** Last entry placeholder */
    ODSP_FEC_MAX_INTF = 2,
} e_odsp_fec_intf;

/**
 * FEC rules
 */
typedef struct
{
    /** Whether the FEC core is enabled or not. Relying on this flag, clock for the PCS-TX, RS DEC, RX IB, TX IB is enable or disable */
    bool enable;

    /** FEC data path mode, which in certain configs may be different between the ingress/egress */
    e_odsp_fec_mode mode;

    /** Nominal data rate of a stream (bundle) through the FEC (100/200/etc, not exact rates) */
    e_odsp_nom_data_rate nom_data_rate;

    /** Input number of lanes per stream (bundle) from the Rxd. It is along with in_fec_type and nom_data_rate to identify FEC mode of the RS DECODER */
    uint8_t num_in_chs;

    /** FEC input data type to the core */
    e_odsp_fec_type in_fec_type;

    /** Output number of lanes per stream (bundle) to the Txd. It is along with out_fec_type and nom_data_rate to identify FEC mode of the RS ENCODER */
    uint8_t num_out_chs;

    /** FEC output data type from the core. It is used to cfg the RS ENCODER */
    e_odsp_fec_type out_fec_type;

    /** Rx channel bit mask to indicate RX PMD channels which are input for the FEC DECODER. channel 1 at bit 1, channel 2 at bit 2, ... Bit 0 is not used
     * It is used along with stream to configure the RX XBAR, CDC FIFO */
    uint16_t stream_chans;

    /** Enable/disable sub-sampling rules for saving power in fec monitoring mode.
    If it is enable in FEC REGEN mode, data can be lost aligned. Default is disable.

    'A' is the number of codewords that the monitor is awake for.
    'S' is the number of codewords that the monitor is sleeping for.

    For 400G and 800G A+S must be an even number.

    The sub-sampling duty cycle is A/(A+S). So if you want a 10% duty cycle you could set A to 10 and S to 90.
    It would be interesting to analyze the power for different ratios.
    For example, how does the power compare for the following sub-sampling configurations:
    1.  En=0 (100%)
    2.  En=1,A=10,S=90 (10%)
    3.  En=1,A=1,S=9 (10%)
    4.  En=1,A=10,S=990 (1%)
    5.  En=1,A=1,S=99 (1%)

    Codewords(CWs) vs time:
        For example, with KP fec_type, number of bit per FEC codeword is 5440 bits. So
        25G KP: then it is about 5 CWs per us.
        100G KP bits/s: 4 x 26,562,500,000 bits/s = 19,531,250 CWs/s => 20 CWs/us.
        800G KP bits/s: => 156 CWs/us.
    */
    bool sub_sampling_en;
    /** 'A' param. Max value = 2^12 - 1.
     * if it is greater than 0xFFF, it is rounded to 0xFFF to set to HW register.
     * When sub_sample_en = false, FW set zero to HW register. */
    uint16_t awake_cws;
    /** 'S' param. Max value = 2^12 - 1.
     * if it is greater than 0xFFF, it is rounded to 0xFFF to set to HW register.
     * When sub_sample_en = false, FW set zero to HW register.
     * In 400G or 800G RSFEC: A+S is even. if A+S is not even, FW round S to (S + 1) when S < 0xFFF, or 0xFFE when S >= 0xFFF */
    uint16_t  sleep_cws;

} odsp_fec_rules_t;

/**
 * Selected data-rates. All units are kilo Baud per second (kBd/s).
 * Note that when the channel is configured for NRZ signalling, the data-rate equals the Baud rate.
 * When configured for PAM signalling, the data-rate is 2x the Baud rate
 */
typedef enum
{
    /** For KR4 */
    ODSP_BAUD_RATE_25p8         = 25781250,
    /** For KP4 */
    ODSP_BAUD_RATE_26p6         = 26562500,
    /** Proprietary Rate */
    ODSP_BAUD_RATE_41p3         = 41250000,
    /** For KR SL */
    ODSP_BAUD_RATE_51p6         = 51562500,
    /** For KP SL */
    ODSP_BAUD_RATE_53p1         = 53125000,
    /** Proprietary Rate */
    ODSP_BAUD_RATE_56p3         = 56250000,

    /** For 50G KP+SFEC. Legacy, same as ODSP_BAUD_RATE_26p6_SFEC */
    ODSP_BAUD_RATE_27p9         = 27890625,
    /** For 100G KP+SFEC. Legacy, same as ODSP_BAUD_RATE_53p1_SFEC */
    ODSP_BAUD_RATE_55p8         = 55781250,

    /** For OTU4(OTL4.4) */
    ODSP_BAUD_RATE_OTN_OTL_28G    = 27952494,
    /** For FOIC8 */
    ODSP_BAUD_RATE_OTN_FOIC_28G   = 27952369,
    /** For FlexO-xe-RS */
    ODSP_BAUD_RATE_OTN_FOIC_26p62 = 26623835,
    /** For FOICE8 */
    ODSP_BAUD_RATE_OTN_FOIC_26p63 = 26628000,
    /** For OTUC4(OTLC.4) */
    ODSP_BAUD_RATE_OTN_OTL_28p1   = 28076180,
    /** For FlexO-xe-RS */
    ODSP_BAUD_RATE_OTN_FOIC_53p2  = 53247670,
    /** For FOICE4 */
    ODSP_BAUD_RATE_OTN_FOIC_53p3  = 53256000,
    /** For FOIC4.8 */
    ODSP_BAUD_RATE_OTN_FOIC_56G   = 55904738,
    /** For 4xOTU4(OTL4.2) */
    ODSP_BAUD_RATE_OTN_OTL_56G    = 55904988,
    /** For OTLC4.8 */
    ODSP_BAUD_RATE_OTN_OTL_56p2   = 56152354,

    /** Same as above but for SFEC (base rate*21/20) */
    /** For KP4 */
    ODSP_BAUD_RATE_26p6_SFEC    = 27890625, // 26562500
    /** For KP SL */
    ODSP_BAUD_RATE_53p1_SFEC    = 55781250, // 53125000

    /*** Same as above but for SFEC+ (base rate*128/120)*/
    /** For KP4 */
    ODSP_BAUD_RATE_26p6_SFECP   = 28333333, // 26562500
    /** For KP SL */
    ODSP_BAUD_RATE_53p1_SFECP   = 56666666, // 53125000

} e_odsp_baud_rate;

/**
 * These are bundles or definitions used by the API to configure certain parts of the data path.
 * An interface (ODSP_INTF_LRX, ODSP_INTF_HTX, etc.) is a position on a particular channel's datapath.
 */

typedef enum
{
    ODSP_INTF_NONE       = 0,

    /** Line RX */
    ODSP_INTF_LRX        = 1 << 0,

    /** Line TX */
    ODSP_INTF_LTX        = 1 << 1,

    /** Host RX */
    ODSP_INTF_HRX        = 1 << 2,

    /** Host TX */
    ODSP_INTF_HTX        = 1 << 3,

    /** The Host side interfaces */
    ODSP_INTF_HOST  = ODSP_INTF_HRX | ODSP_INTF_HTX,

    /** The Line side interfaces */
    ODSP_INTF_LINE  = ODSP_INTF_LRX | ODSP_INTF_LTX,

    /** Egress FEC */
    ODSP_INTF_EG = ODSP_INTF_HRX,

    /** Ingress FEC */
    ODSP_INTF_IG = ODSP_INTF_LRX,

    /** All interfaces */
    ODSP_INTF_ALL       = 0xffff
} e_odsp_intf;

/**
 * The Transmitter Look-Up-Table (LUT) configuration
 */
typedef enum
{
    /** 4-tap LUT mode, need to program the LUT before using it */
    ODSP_TX_LUT_4TAP     = 0,

    /**
     * Bypass mode. One-tap LUT mode, uses rows 0-3 of the LUT */
    ODSP_TX_LUT_BYPASS   = 1,

    /** Seven-tap linear mode. Does not use LUT, convolution output is used directly */
    ODSP_TX_LUT_7TAP_LIN = 2,

    /**Seven-tap LUT mode. Convolution result is put through LUT, using all 0-255 rows */
    ODSP_TX_LUT_7TAP_LUT = 3

} e_odsp_lut_mode;

/**
 * The signal/encoding mode (NRZ vs. PAM)
 */
typedef enum
{
    /** PAM signalling mode */
    ODSP_SIGNAL_MODE_PAM = 0,

    /** NRZ signalling mode */
    ODSP_SIGNAL_MODE_NRZ = 1,

    /** Invalid signalling mode */
    ODSP_SIGNAL_MODE_UNKNOWN = 2
} e_odsp_signal_mode;

/**
 * The DSP mode
 *
 * Each of the suggestions below are only guidelines, your selection of DSP mode is very
 * system dependant. Contact your customer support rep and start a discussion on which DSP
 * mode is best for your platform.
 *
 * Nomenclature:
 *
 * - FFE is the feed-forward equalizer and is enabled for all modes
 * - Slicer is what slices the PAM4 signal at different voltages, and is enabled for all modes
 * - RC is the reflection canceler, which extends the FFE and smooths out the tail in the pulse response.
 *   Used for links with strong reflections or too much energy in the pulse response tail.
 * - LDEQ is the level-dependent equalizer, which will equalize the eyes differently for each
 *   voltage level. Used for optics, which may have non-uniform eye openings at each voltage.
 * - DFE is the decision feedback equalizer used for strenuous links.
 *
 */
typedef enum
{
    /** PAM4 slicer, used for short non-strenuous links */
    ODSP_DSP_MODE_SLC1,

    /** PAM4 slicer with reflection canceller */
    ODSP_DSP_MODE_SLC1_RC_SLC2,

    /** PAM4 slicer with MPI canceller */
    ODSP_DSP_MODE_SLC1_MPICAN_SLC2,

    /** PAM4 slicer with reflection canceller and MPI canceller */
    ODSP_DSP_MODE_SLC1_RC_MPICAN_SLC2,

    /** Decision Feedback Equalizer (DFE) */
    ODSP_DSP_MODE_DFE1,

    /** DFE with reflection canceller */
    ODSP_DSP_MODE_DFE1_RC_DFE2, 

    /** DFE with MPI canceller */
    ODSP_DSP_MODE_DFE1_MPICAN_DFE2,

    /** DFE with MPI canceller and reflection canceller */
    ODSP_DSP_MODE_DFE1_RC_MPICAN_DFE2,

    /** PAM4 slicer and Level dependent equalizer, used for short non-strenuous links */
    ODSP_DSP_MODE_SLC1_LDEQ,

    /** PAM4 slicer slicer with reflection canceller and Level dependent equalizer */
    ODSP_DSP_MODE_SLC1_RC_LDEQ,

    /** PAM4 slicer with MPI canceller and Level dependent equalizer */
    ODSP_DSP_MODE_SLC1_MPICAN_LDEQ,

    /** PAM4 slicer with reflection canceller, MPI canceller and Level dependent equalizer*/
    ODSP_DSP_MODE_SLC1_RC_MPICAN_LDEQ,

    /** Unknown */
    ODSP_DSP_MODE_UNKNOWN
} e_odsp_dsp_mode;

/**
  * The PHY package type which defines the channel mapping.
  */
typedef enum
{
    /** KGD package type */
    ODSP_PACKAGE_TYPE_KGD,

    /** 800G EML 13x13 package */
    ODSP_PACKAGE_TYPE_800G_EML_13x13,

    /** 800G SIPHO 13x13 package */
    ODSP_PACKAGE_TYPE_800G_SIPHO_13x13,

    /** Unknown package type */
    ODSP_PACKAGE_TYPE_UNKNOWN,

    /** For range checking of the package type, don't use this value. */
    ODSP_PACKAGE_TYPE_MAX,
} e_odsp_package_type;

/**
 * The device driver type
 */
typedef enum
{
    /** Driver type None */
    ODSP_TX_DRIVER_TYPE_NA = 0,
    /** Driver type EML */
    ODSP_TX_DRIVER_TYPE_EML_1P50V,
    /** Driver type 1V */
    ODSP_TX_DRIVER_TYPE_SIPHO_1P00V,
    /** Driver type SiPho 2.75V */
    ODSP_TX_DRIVER_TYPE_SIPHO_2P75V
} e_odsp_tx_driver_type;

/** TX STD driver voltage */
typedef enum
{
    /** Voltage 1V */
    ODSP_TX_DRIVER_VOLTAGE_1P00V,
    /** Voltage 1.5V */
    ODSP_TX_DRIVER_VOLTAGE_1P50V,
} e_odsp_tx_driver_voltage;

/**
 * The TX driver impedance(Sipho driver only)
 */
typedef enum
{
    /** Unknown impedance */
    ODSP_TX_DRIVER_IMPEDANCE_NA,
    /** Impedance 66 Ohms */
    ODSP_TX_DRIVER_IMPEDANCE_66,
    /** Impedance 85 Ohms */
    ODSP_TX_DRIVER_IMPEDANCE_85
} e_odsp_tx_driver_impedance;

/**
 * The protocol mode of the device
 */
typedef enum
{
    /** Host 4x106.25Gbps (PAM4) to Line 4x106.25Gbps (PAM4) */
    ODSP_PROT_MODE_400G_4Px53p1_TO_4Px53p1,
    /** Host 2x106.25Gbps (PAM4) to Line 2x106.25Gbps (PAM4) */
    ODSP_PROT_MODE_200G_2Px53p1_TO_2Px53p1,
    /** Host 1x106.25Gbps (PAM4) to Line 1x106.25Gbps (PAM4) */
    ODSP_PROT_MODE_100G_1Px53p1_TO_1Px53p1,
    /** Host 4x112Gbps (PAM4) to Line 4x112Gbps (PAM4) */
    ODSP_PROT_MODE_400G_OTLC4_TO_OTLC4,

    /** Host 8x53.125 Gbps (PAM4) to Line 8x53.125 Gbps (PAM4) */
    ODSP_PROT_MODE_400G_8Px26p6_TO_8Px26p6,
    /** Host 4x53.125 Gbps (PAM4) to Line 4x53.125 Gbps (PAM4) */
    ODSP_PROT_MODE_200G_4Px26p6_TO_4Px26p6,
    /** Host 2x53.125 Gbps (PAM4) to Line 2x53.125 Gbps (PAM4) */
    ODSP_PROT_MODE_100G_2Px26p6_TO_2Px26p6,
    /** Host 1x53.125 Gbps (PAM4) to Line 1x53.125 Gbps (PAM4) */
    ODSP_PROT_MODE_50G_1Px26p6_TO_1Px26p6,

    /** Host 8x25.78125 Gbps (NRZ) to Line 8x25.78125 Gbps (NRZ) */
    ODSP_PROT_MODE_200G_8Nx25p8_TO_8Nx25p8,
    /** Host 4x25.78125 Gbps (NRZ) to Line 4x25.78125 Gbps (NRZ) */
    ODSP_PROT_MODE_100G_4Nx25p8_TO_4Nx25p8,
    /** Host 1x25.78125 Gbps (NRZ) to Line 1x25.78125 Gbps (NRZ) */
    ODSP_PROT_MODE_25G_1Nx25p8_TO_1Nx25p8,

    /** Host 8x106.25Gbps (PAM4) to Line 8x106.25Gbps (PAM4) */
    ODSP_PROT_MODE_800G_8Px53p1_TO_8Px53p1,

    /** Host 2x53.125 Gbps (PAM) to Line 1x106.25 Gbps (PAM4) */
    ODSP_PROT_MODE_100G_2Px26p6_TO_1Px53p1,

    /** Host 4x53.125 Gbps (PAM) to Line 2x106.25 Gbps (PAM4) */
    ODSP_PROT_MODE_200G_4Px26p6_TO_2Px53p1,

    /** Host 8x53.125 Gbps (PAM) to Line 4x106.25 Gbps (PAM4) */
    ODSP_PROT_MODE_400G_8Px26p6_TO_4Px53p1,

    /** Host 1x106.25 (PAM) to Line 2x53.125 Gbps Gbps (PAM4) */
    ODSP_PROT_MODE_100G_1Px53p1_TO_2Px26p6,

    /** Host 2x106.25 (PAM) to Line 4x53.125 Gbps Gbps (PAM4) */
    ODSP_PROT_MODE_200G_2Px53p1_TO_4Px26p6,

    /** Host 4x106.25 (PAM) to Line 8x53.125 Gbps Gbps (PAM4) */
    ODSP_PROT_MODE_400G_4Px53p1_TO_8Px26p6,

    /** End of list */
    ODSP_PROT_MODE_END,

} e_odsp_protocol_mode;

/** Configure the Digital Timing Loop (MM or ZF) */
typedef enum
{
    /** Let the firmware select the timing mode based on the baud rate */
    ODSP_DTL_MODE_AUTO,

    /** Force MM mode */
    ODSP_DTL_MODE_MM,

    /** Force ZF mode */
    ODSP_DTL_MODE_ZF
} e_odsp_dtl_mode;

/** Configure the CDR mode */
typedef enum
{
    /** HW theta sweep done by STM */
    ODSP_CDR_MODE_HW_THETA_SWEEP = 0,

    /** Legacy FW theta sweep */
    ODSP_CDR_MODE_FW_THETA_SWEEP = 1,

    /** FW theta sweep with 3 MM sweeps + 1 ZF sweep */
    ODSP_CDR_MODE_FW_THETA_SWEEP_3MM_ZF = 2,

    /** CDR lock using phase scan procedure */
    ODSP_CDR_MODE_FW_PHASE_SWEEP = 3,

    /** CDR lock using oversampled TED (half rate only) */
    ODSP_CDR_MODE_HR_OVERSAMPLED_TED = 4,

} e_odsp_cdr_mode;

/**
 * LOS Assert/De-assert control
 */
typedef enum
{
    /** Input signal amplitude, 100 mv ppd  */
    ODSP_LOS_CTRL_AMP_100 = 0,
    /** Input signal amplitude, 150 mv ppd  */
    ODSP_LOS_CTRL_AMP_150 = 1,
    /** Input signal amplitude, 200 mv ppd  */
    ODSP_LOS_CTRL_AMP_200 = 2,
    /** Input signal amplitude, 250 mv ppd  */
    ODSP_LOS_CTRL_AMP_250 = 3,
    /** Input signal amplitude, 300 mv ppd  */
    ODSP_LOS_CTRL_AMP_300 = 4,
    /** Input signal amplitude, 350 mv ppd  */
    ODSP_LOS_CTRL_AMP_350 = 5,
    /** Input signal amplitude, 400 mv ppd  */
    ODSP_LOS_CTRL_AMP_400 = 6,
    /** Input signal amplitude, 450 mv ppd  */
    ODSP_LOS_CTRL_AMP_450 = 7,
    /** Input signal amplitude, 500 mv ppd  */
    ODSP_LOS_CTRL_AMP_500 = 8,
    /** Input signal amplitude, 550 mv ppd  */
    ODSP_LOS_CTRL_AMP_550 = 9,
    /** Input signal amplitude, 600 mv ppd  */
    ODSP_LOS_CTRL_AMP_600 = 10,
    /** Max LOS level */
    ODSP_LOS_CTRL_AMP_MAX = 11,

} e_odsp_los_ctrl_amp;

/**
 * LOS Assert/De-assert control thresholds
 */
typedef enum
{
    /** LOS threshold 0  */
    ODSP_LOS_THRESH_0 = 0,
    /** LOS threshold 1  */
    ODSP_LOS_THRESH_1 = 1,
    /** LOS threshold 2  */
    ODSP_LOS_THRESH_2 = 2,
    /** LOS threshold 3  */
    ODSP_LOS_THRESH_3 = 3,
    /** LOS threshold 4  */
    ODSP_LOS_THRESH_4 = 4,
    /** LOS threshold 5  */
    ODSP_LOS_THRESH_5 = 5,
    /** LOS threshold 6  */
    ODSP_LOS_THRESH_6 = 6,
    /** LOS threshold 7  */
    ODSP_LOS_THRESH_7 = 7,
    /** LOS threshold 8  */
    ODSP_LOS_THRESH_8 = 8,
    /** LOS threshold 9  */
    ODSP_LOS_THRESH_9 = 9,
}e_odsp_los_ctrl_thresh;

/**
 * The RX quality-check approximate SNR thresholds.
 * PAM supported range: 14p5dB to 21dB.
 * NRZ supported range: 10p1dB to 14dB.
 */
typedef enum
{
    /** SNR 10.1 (dB) */
    ODSP_RX_QC_SNR_THRESH_10p1dB   = 0x65,
    /** SNR 10.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_10p5dB   = 0x69,
    /** SNR 11 (dB) */
    ODSP_RX_QC_SNR_THRESH_11dB     = 0x6E,
    /** SNR 11.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_11p5dB   = 0x73,
    /** SNR 12 (dB) */
    ODSP_RX_QC_SNR_THRESH_12dB     = 0x78,
    /** SNR 12.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_12p5dB   = 0x7D,
    /** SNR 13 (dB) */
    ODSP_RX_QC_SNR_THRESH_13dB     = 0x82,
    /** SNR 13.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_13p5dB   = 0x87,
    /** SNR 14 (dB) */
    ODSP_RX_QC_SNR_THRESH_14dB     = 0x8C,
    /** SNR 14.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_14p5dB   = 0x91,
    /** SNR 15 (dB) */
    ODSP_RX_QC_SNR_THRESH_15dB     = 0x96,
    /** SNR 15.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_15p5dB   = 0x9B,
    /** SNR 16 (dB) */
    ODSP_RX_QC_SNR_THRESH_16dB     = 0xA0,
    /** SNR 16.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_16p5dB   = 0xA5,
    /** SNR 17 (dB) */
    ODSP_RX_QC_SNR_THRESH_17dB     = 0xAA,
    /** SNR 17.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_17p5dB   = 0xAF,
    /** SNR 18 (dB) */
    ODSP_RX_QC_SNR_THRESH_18dB     = 0xB4,
    /** SNR 18.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_18p5dB   = 0xB9,
    /** SNR 19 (dB) */
    ODSP_RX_QC_SNR_THRESH_19dB     = 0xBE,
    /** SNR 19.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_19p5dB   = 0xC3,
    /** SNR 20 (dB) */
    ODSP_RX_QC_SNR_THRESH_20dB     = 0xC8,
    /** SNR 20.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_20p5dB   = 0xCD,
    /** SNR 21 (dB) */
    ODSP_RX_QC_SNR_THRESH_21dB     = 0xD2,
    /** SNR 21.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_21p5dB   = 0xD7,
    /** SNR 22 (dB) */
    ODSP_RX_QC_SNR_THRESH_22dB     = 0xDC,
    /** SNR 22.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_22p5dB   = 0xE1,
    /** SNR 23 (dB) */
    ODSP_RX_QC_SNR_THRESH_23dB     = 0xE6,
    /** SNR 23.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_23p5dB   = 0xEB,
    /** SNR 24 (dB) */
    ODSP_RX_QC_SNR_THRESH_24dB     = 0xF0,
    /** SNR 24.5 (dB) */
    ODSP_RX_QC_SNR_THRESH_24p5dB   = 0xF5,
    /** SNR 25 (dB) */
    ODSP_RX_QC_SNR_THRESH_25dB     = 0xFA,

} e_odsp_rx_qc_snr_threshold;

/** 
 * FLL modes of operation
 */
typedef enum
{
    /** Counter-PD mode */
    ODSP_FLL_MODE_COUNTER_PD,
    /** BB-PD mode */
    ODSP_FLL_MODE_BB_PD,
} e_odsp_fll_mode;

/**
 * The vddhtx rule tells the FW what voltage level is connected to the analog power-supply, VDDHTX.  
 * The default is ODSP_VDD_1P150V.
 * 
 * - If the rule is not set, or set to ODSP_VDD_1P150V, then the FW configures the transmitter
 *   to operate under the assumption that VDDHTX is connected to 1.150V.
 *
 * - If the rule is set to ODSP_VDD_0P940V, the FW configures the transmitter to operate under
 *   the assumption that VDDHTX is connected to 0.940V.
 * 
 * The reason the default is set to ODSP_VDD_1P150V, is because regardless of the power supply
 * level, this assumption should not damage the part.  The contrary is not the case.
 * 
 * If you connect 1.150V to VDDHTX and tell the FW to assume 0.940V, you can damage the part.
 */
typedef enum 
{
    /** VDD 1.150V */
    ODSP_VDD_1P150V,
    /** VDD 0.940V */
    ODSP_VDD_0P940V,
} e_odsp_vdd;

/**
 * This structure contains the rules used to control TX FIR taps,
 * the device allows to configure these rules on the fly
 */
typedef struct
{
    /** Coefficient Pre-Tap, range -1000 to 1000 where -1000 = -1, 1000 = 1
     * Use the first N indexes for N-Tap mode */
    int16_t fir_taps[7];

    /** Scale PAM lower inner eye, range 500 to 1500 where 500 = 0.5, 1500 = 1.5
      * NOTE: This is used for LUT modes only, default eye levels are used for linear modes */
    uint16_t inner_eye1;

    /** Scale PAM upper inner eye, range 1500 to 2500 where 1500 = 1.5, 2500 = 2.5
      * NOTE: This is used for LUT modes only, default eye levels are used for linear modes */
    uint16_t inner_eye2;

} odsp_tx_fir_rules_t;

/** Internal use only (Do not modify) */
typedef enum
{
    /** Use default settings for EML cascode calibration */
    ODSP_TX_EML_CAL_USE_DEFAULT_SETTINGS,
    /** Use TX settings for EML cascode calibration */
    ODSP_TX_EML_CAL_USE_TX_SETTINGS,
    /** Use custom settings for EML cascode calibration */
    ODSP_TX_EML_CAL_USE_CUSTOM_SETTINGS,
} e_odsp_tx_eml_cal_mode;

/** Internal use only (Do not modify) */
typedef struct
{
    /** Enable the vbias_ctrl override (Do not modify) */
    bool vbias_ctrl_enable;

    /** Enable the rterm override (Do not modify) */
    bool rterm_enable;

    /** Enable the rterm_coarse override (Do not modify) */
    bool rterm_coarse_enable;

    /** Enable the cm_adj override (Do not modify) */
    bool cm_adj_enable;

    /** Enable the cascode override (Do not modify) */
    bool cascode_ctrl_enable;

    /** Control rterm calibration (Do not modify)  */
    bool rterm_cal_disable;

    /** Control cascode calibration (Do not modify)  */
    bool cascode_cal_disable;

    /** Control amplitude calibration (Do not modify)  */
    bool ampl_cal_disable;

    /** Control AVS calibration (Do not modify)  */
    bool avs_cal_disable;

    /** DAC current source BIAS control (Do not modify) */
    uint8_t vbias_ctrl;

    /** DAC termination trim code (bit 0:3) (Do not modify) */
    uint8_t rterm;

    /** DAC termination trim code (bit 4:5) (Do not modify) */
    uint8_t rterm_coarse;

    /** Common mode adjust (Do not modify) */
    uint8_t cm_adj;

    /** Cascode adjust value (Do not modify) */
    uint8_t cascode_ctrl;

    /** Target value (mV) for amplitude calibration (Do not modify) */
    uint16_t ampl_cal_value;

    /** Target value (mV) for cascode calibration (Do not modify) */
    uint16_t cascode_cal_value;

    /** Cascode calibration mode */
    e_odsp_tx_eml_cal_mode eml_cascode_cal_mode;

    /** Tx Fir used in EML cascode calibration */
    odsp_tx_fir_rules_t eml_cascode_txfir;
} odsp_tx_adv_tune_t;

/** Internal use only (Do not modify) */
typedef struct
{
     /** Internal use only (Do not modify) */
     odsp_tx_adv_tune_t tune;
} odsp_tx_adv_rules_t;

/**
 * This structure contains the rules used to control
 * the transmitters of the device.
 */
typedef struct 
{
    /** Channel Enable, set to false to cause the FW to ignore this channel. Channels set to false
     * will be set to a low power mode. */
    bool channel_enable;

    /**
     * Lock the squelch for manual control by host software. The firmware
     * may still squelch the TX but it cannot un-squelch when this flag
     * is asserted
     */
    bool squelch_lock;

    /**
     * LUT mode
     *
     * Note there are special restrictions around ODSP_TX_LUT_BYPASS, see the e_odsp_lut_mode
     * documentation for more details.
     */
    e_odsp_lut_mode lut_mode;

    /**
     * IEEE Demap, sometimes called bit order. True to use the IEEE standard bit order of LSB-first,
     * false to use legacy Marvell bit order of MSB-first.
     *
     * This should always be left to true unless the other device is connected
     * to (on either host or line) is a legacy Marvell device (ie 28nm PAM B0). Even in those cases, the
     * latest APIs for legacy devices support IEEE mode, and should be enabled on those devices.
     * */
    bool ieee_demap;

    /** Gray mapping */
    bool gray_mapping;  

    /** Tx channel inversion */
    bool invert_chan;

    /** Coefficient Pre-Tap, range -1000 to 1000 where -1000 = -1, 1000 = 1 
     * Use the first N indexes for N-Tap mode */
    int16_t fir_taps[7];

    /** Scale PAM lower inner eye, range 500 to 1500 where 500 = 0.5, 1500 = 1.5.  
      * NOTE: This is used for LUT modes only, default eye levels are used for linear modes */
    uint16_t inner_eye1;

    /** Scale PAM upper inner eye, range 1500 to 2500 where 1500 = 1.5, 2500 = 2.5.  
      * NOTE: This is used for LUT modes only, default eye levels are used for linear modes */
    uint16_t inner_eye2;

    /** DFE precoder enable.
     * The DFE precoder helps to transform burst errors from the DFE to error events with 
     * smaller number of bit flips in order to improve BER. The precoder should not be 
     * turned on in non-DFE modes since it can actually increase the BER.
     *
     * Note that the link partner's receive precoder must be enabled if
     * this rule is set to true.
    */
    bool dfe_precoder_en;
    
    /** FLL mode */
    e_odsp_fll_mode fll_mode;

    /** TX driver impedance, applicable for Sipho driver only */
    e_odsp_tx_driver_impedance driver_impedance;

    /** TX advanced rules */
    odsp_tx_adv_rules_t adv;

  
} odsp_tx_rules_t;

/**
 *  AFE BW control
 */
typedef enum
{
    /** Default deQ  BW  setting */
    ODSP_AFE_DEQ_BW_DEFAULT = 0,
    /** Automatic deQ  BW control */
    ODSP_AFE_DEQ_BW_AUTO = 1,
    /** deQ  Enable: low bandwidth  */
    ODSP_AFE_BW_DEQ_LOW   = 2,
    /** deQ  Disable: high bandwidth  */
    ODSP_AFE_BW_DEQ_HIGH = 3,

} e_odsp_afe_deQ_bw_cfg;

/**
 * Advanced rules for the receiver - do not modify
 */
typedef struct
{
    /** Do not modify */
    uint8_t ffe_leakage_eta;
    /** Do not modify */
    bool    ffe_fir_adapt_force;
    /** Do not modify */
    bool    ffe_gain_adapt_force;
    /** Do not modify */
    bool    ffe_dc_adapt_force;
    /** Do not modify */
    bool    skip_dsp_fine_tune;
    /** Do not modify */
    bool    afe_offset_comp_disable;
    /** DFE initial value U6.6. Do not modify */
    uint8_t dfe_init_value;
    /** Allow the user to set the AVS code. If 0: AVS code and SELCOMP are calibrated. */
    uint8_t avs_code;
    /** Allow the user to set the AVS SELCOMP. */
    uint8_t selcomp;
    /** afe_deQ BW control state 1*/
    e_odsp_afe_deQ_bw_cfg afe_bw_cfg1;
    /** afe_deQ BW control state 2*/
    e_odsp_afe_deQ_bw_cfg afe_bw_cfg2;

} odsp_rx_adv_rules_t;

/**
 * VGA Threshold
 */
typedef enum
{
    /** Standard VGA Threshold */
    ODSP_VGA_THRESHOLD_STANDARD = 0,
    /** Scaled VGA Threshold */
    ODSP_VGA_THRESHOLD_SCALED   = 1,
    /** Inverted VGA Threshold */
    ODSP_VGA_THRESHOLD_INVERTED = 2,
} e_odsp_vga_threshold;

/**
 * ADC Startup
 */
typedef enum
{
    /** ADC Startup Standard */
    ODSP_ADC_STARTUP_STANDARD = 0,
    /** ADC Startup Legacy */
    ODSP_ADC_STARTUP_LEGACY   = 1,
} e_odsp_adc_startup;

/**
 * This structure contains the rules used to control
 * the receivers of the device
 */
typedef struct
{
    /**
     * Enable/disable the RX channel. If this is set to
     * disable the channel will be powered down
     */
    bool channel_enable;

    /** DSP mode */
    e_odsp_dsp_mode dsp_mode;

    /** Gray mapping */
    bool gray_mapping;

    /**
     * IEEE Demap, sometimes called bit order. True to use the IEEE standard bit order of LSB-first,
     * false to use legacy bit order of MSB-first.
     *
     * This should always be left to true unless the other device is connected
     * to (on either host or line) is a legacy device (ie 28nm PAM B0). Even in those cases, the
     * latest APIs for legacy devices support IEEE mode, and should be enabled on those devices.
     */
    bool ieee_demap;

    /**
     * DFE precoder enable. The DFE precoder helps to transform
     * burst errors from the DFE to error events with smaller number of bit
     * flips in order to improve BER. The precoder should not be turned on in
     * non-DFE modes since it can actually increase the BER.
     *
     * @{note,
     * - the link partner's transmit precoder must be enabled if
     *   this rule is set to true.
     * - the precoder should only be enabled in PAM mode (it should
     *   be turned off in NRZ)
     * }
     */
    bool dfe_precoder_en;
    
    /** Rx channel inversion */
    bool invert_chan;

    /** Enable VGA tracking */
    bool vga_tracking;

    /** VGA Threshold */
    e_odsp_vga_threshold vga_threshold;

    /** Preamp bias current control */
    uint8_t preamp_bias_ctrl;

    /** Change the DTL mode (Meuller-Muller or Zero Forcing) */
    e_odsp_dtl_mode dtl_mode;
    
    /** Advanced rules for the RX */
    odsp_rx_adv_rules_t adv;

    /** Enable/disable ALL QC algorithms */
    bool qc_all_dis;

    /** Enable/disable QC histogram algorithms */
    bool qc_hist_dis;

    /** Enable/disable QC slicer algorithms */
    bool qc_slc_dis;

    /** Enable/disable QC SNR algorithms */
    bool qc_snr_dis;

    /** Enable/disable the double restart */
    bool qc_double_restart_dis;

    /** LOS Assert threshold (units are e_odsp_los_ctrl_thresh enum). */
    e_odsp_los_ctrl_thresh los_asrt_ctrl;

    /** LOS De-assert threshold (units are e_odsp_los_ctrl_thresh enum). */
    e_odsp_los_ctrl_thresh los_dsrt_ctrl;

    /** Enable/disable Loss of Signal support */
    bool los_en;

    /**
     * Eye optimization window used in theta sweep.
     */
    uint8_t theta_opt_win;

    /** CDR locking mode */
    e_odsp_cdr_mode cdr_mode;

} odsp_lrx_rules_t;

/**
 * This structure contains the rules used to control
 * the receivers of the device
 */
typedef struct
{
    /**
     * Enable/disable the RX channel. If this is set to
     * disable the channel will be powered down
     */
    bool channel_enable;

    /** DSP mode */
    e_odsp_dsp_mode dsp_mode;

    /** Gray mapping */
    bool gray_mapping;

    /**
     * IEEE Demap, sometimes called bit order. True to use the IEEE standard bit order of LSB-first,
     * false to use legacy bit order of MSB-first.
     *
     * This should always be left to true unless the other device is connected
     * to (on either host or line) is a legacy device (ie 28nm PAM B0). Even in those cases, the
     * latest APIs for legacy devices support IEEE mode, and should be enabled on those devices.
     */
    bool ieee_demap;

    /**
     * DFE precoder enable. The DFE precoder helps to transform
     * burst errors from the DFE to error events with smaller number of bit
     * flips in order to improve BER. The precoder should not be turned on in
     * non-DFE modes since it can actually increase the BER.
     *
     * @{note,
     * - the link partner's transmit precoder must be enabled if
     *   this rule is set to true.
     * - the precoder should only be enabled in PAM mode (it should
     *   be turned off in NRZ)
     * }
     */
    bool dfe_precoder_en;

    /** Rx channel inversion */
    bool invert_chan;

    /** Enable VGA tracking */
    bool vga_tracking;

    /** VGA Threshold */
    e_odsp_vga_threshold vga_threshold;

    /** Change the DTL mode (Meuller-Muller or Zero Forcing) */
    e_odsp_dtl_mode dtl_mode;

    /** Advanced rules for the RX */
    odsp_rx_adv_rules_t adv;

    /** Enable/disable ALL QC algorithms */
    bool qc_all_dis;

    /** Enable/disable QC histogram algorithms */
    bool qc_hist_dis;

    /** Enable/disable QC slicer algorithms */
    bool qc_slc_dis;

    /** Enable/disable QC SNR algorithms */
    bool qc_snr_dis;

    /** Enable/disable the double restart */
    bool qc_double_restart_dis;

    /** LOS Assert threshold (units are e_odsp_los_ctrl_thresh enum). */
    e_odsp_los_ctrl_thresh los_asrt_ctrl;

    /** LOS De-assert threshold (units are e_odsp_los_ctrl_thresh enum). */
    e_odsp_los_ctrl_thresh los_dsrt_ctrl;

    /** Enable/disable Loss of Signal support */
    bool los_en;

    /**
     * Eye optimization window used in theta sweep.
     */
    uint8_t theta_opt_win;

    /** CDR locking mode */
    e_odsp_cdr_mode cdr_mode;
} odsp_hrx_rules_t;

/**
 * This structure contains the RX quality-check rules.
 */
typedef struct
{
    /** RX QC SNR Threshold - Enter MM */
    e_odsp_rx_qc_snr_threshold snr_threshold_mm_enter;

    /** RX QC SNR Threshold - Exit MM */
    e_odsp_rx_qc_snr_threshold snr_threshold_mm_exit;

   /** The max allowable error percentage in the RX DSP slicer location in order that the RX state
    * can be deemed to be of acceptable quality.
    * 0% means the slicer has to be exactly the ideal value.
    * 100% means the slicer can deviate +/-100% from the ideal value.
    * Value is rounded down to the nearest 10% (ie 97% gets truncated to 90%) (default:50%)
    */
    uint8_t slc_err_limit;

} odsp_rx_qc_rules_t;

/**
 * This structure contains the RX quality check (QC) status.
 */
typedef struct
{
    /** RX QC encountered an error */
    bool qc_error;
    /** RX QC abandoned */
    bool qc_giveup;
    /** RX QC done */
    bool qc_done;
    /** RX QC invoked a restart */
    bool qc_restart;

    /** RX histogram check is done */
    bool qc_hist_done;
    /** RX slicer check is done */
    bool qc_slc_done;
    /** RX SNR check is done */
    bool qc_snr_done;

    /** RX histogram check is failed in data mode */
    bool qc_hist_chk_failed_data_mode;
    /** RX slicer check is failed in data mode */
    bool qc_slc_chk_failed_data_mode;
    /** RX SNR check is failed in data mode */
    bool qc_snr_chk_failed_data_mode;
    /** RX SNR check is failed during bring up */
    bool qc_snr_chk_failed_bringup;

} odsp_rx_qc_status_t;

/** 
 * Clock Monitor rates 
 */
typedef enum {
    /** 0.8 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_0p8G,
    /** 1.7 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_1p7G,
    /** 3.3 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_3p3G,
    /** 6.6 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_6p6G,
    /** 13.3 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_13p3G,
    /** 26.6 GHz clock monitor */
    ODSP_TX_MONCLK_RATE_26p6G,
} e_odsp_monclk_rate;

/** 
 * Clock monitor source selection 
 */
typedef enum {
    /** Monitor Ref clock from ERU0 */
    ODSP_TX_MONCLK_SOURCE_ERU0,
    /** Monitor Ref clock from ERU1 */
    ODSP_TX_MONCLK_SOURCE_ERU1,
    /** Monitor Ref clock from RX recovery clock */
    ODSP_TX_MONCLK_SOURCE_RCVRCLK,
    /** Invalid */
    ODSP_TX_MONCLK_SOURCE_INVALID,
}e_odsp_monclk_source;

/**
 * This structure contains the TX clock monitoring rules.
 */
typedef struct
{
    /** Enable/disable clock monitoring */
    bool monclk_en;

    /** Clock monitor rate */
    e_odsp_monclk_rate clk_rate;

    /** Clock monitor source selection */
    e_odsp_monclk_source clk_source;
} odsp_monclk_rules_t;

/**
 * This structure contains GCK clock rules for HRX interface
 */
typedef struct
{
    /**
     * Enable or disable GCK HRX clock recovery
     */
     bool gck_en;

    /** HRX channel ID that clock is recovered from */
    uint8_t hrx_gck_channel;

    /** LTX/HTX reference clock source from ERU0 or ERU1 */
    e_odsp_monclk_source ltx_clk_source;
    e_odsp_monclk_source htx_clk_source;
} odsp_gck_rules;

/**
 * Control the AFE input termination block
 */
typedef enum
{
    /** Let the Firmware determine the best setting */
    ODSP_AFE_TRIM_AUTO,

    /** 0 dB */
    ODSP_AFE_TRIM_0dB,

    /** -6 dB */
    ODSP_AFE_TRIM_NEG_6dB
} e_odsp_afe_trim;

/**
 * Defines the FLL BW configuration
 */
typedef enum
{
    /** FLL BW ~32khz  (Default)*/
    ODSP_FLL_BW_0 ,

    /** FLL BW ~66khz */
    ODSP_FLL_BW_1 ,

    /** FLL BW ~106khz */
    ODSP_FLL_BW_2 ,

    /** FLL BW ~176khz */
    ODSP_FLL_BW_3 ,

    /** FLL BW ~267khz */
    ODSP_FLL_BW_4 ,

    /** FLL BW ~383khz */
    ODSP_FLL_BW_5 ,

    /** FLL BW ~503khz */
    ODSP_FLL_BW_6 ,

    /** FLL BW ~632khz */
    ODSP_FLL_BW_7 ,

    /** FLL BW ~1069khz */
    ODSP_FLL_BW_8 ,

    /** FLL BW ~1435khz */
    ODSP_FLL_BW_9 ,

} e_odsp_fll_bw;

/**
 * Defines the SPLL PIOC mode
 */
typedef enum
{
    /** PIOC mode disabled */
    ODSP_PIOC_MODE_DISABLE,

    /** PIOC mode 1 enabled */
    ODSP_PIOC_MODE_1_ENABLE,

    /** PIOC mode 2 enabled */
    ODSP_PIOC_MODE_2_ENABLE,

    /** PIOC mode 3 enabled */
    ODSP_PIOC_MODE_3_ENABLE,
} e_odsp_pioc_mode;

typedef struct
{
    /** Background vtop calibration enable */
    bool enable;
    /** Period (in secs) for periodic update */
    uint32_t period;
    /** DeltaT for update on change in temperature  */
    uint16_t delta_temp;
    /** Executed calibrations */
    uint32_t cal_counter;
    /** Calibration is ongoing */
    bool ongoing;
} odsp_bandgap_rules_t;

/**
 * Advanced rules for diagnostics and debug features.
 *
 * Note: Leave these as their defaults unless instructed to by a Marvell AE
 */
typedef struct
{
    /** Configure EG FLL bandwidth */
    e_odsp_fll_bw htx_fll_bw ;

    /** Configure IG FLL bandwidth */
    e_odsp_fll_bw ltx_fll_bw ;

    /** GCK clock recovery */
    odsp_gck_rules gck;    

    /** Background bandgap calibration */
    odsp_bandgap_rules_t bandgap;

    /** SPLL PIOC mode */
    e_odsp_pioc_mode spll_pioc_mode;

} odsp_advanced_rules_t;

/**
 * SFEC Rates. They are applicable for LINE PAM4
 */
typedef enum {
    /** 1 x 50G */
    ODSP_SFEC_1_X_50,
    /** 2 x 50G */
    ODSP_SFEC_2_X_50,
    /** 4 x 50G */
    ODSP_SFEC_4_X_50,
    /** 8 x 50G */
    ODSP_SFEC_8_X_50,
    /** 1 x 100G */
    ODSP_SFEC_1_X_100,
    /** 2 x 100G */
    ODSP_SFEC_2_X_100,
    /** 4 x 100G */
    ODSP_SFEC_4_X_100,
    /** 8 x 100G */
    ODSP_SFEC_8_X_100,
    ODSP_SFEC_NONE
} e_odsp_sfec_cfg;

/**
 * SFEC types. They are applicable for LINE PAM4.
 */
typedef enum {
    /** SFEC mode = disabled */
    ODSP_SFEC_DIS  = 0,
    /** SFEC mode = true. Once SFEC is enable, baud rate for it should be set accordingly: sfec baud rate = (base rate*21/20), for examples:
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_25p8, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_25p8_SFEC,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_26p6, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_26p6_SFEC,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_41p3, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_41p3_SFEC,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_51p6, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_51p6_SFEC,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_53p1, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_53p1_SFEC,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_56p3, baud rate of this PMD channel for SFEC traffic is ODSP_BAUD_RATE_56p3_SFEC,
     */
    ODSP_SFEC      = 1,
    /** SFEC+ mode = true. Once SFEC+ is enable, baud rate for it should be set accordingly: SFEC+ baud rate = (base rate*128/120), for examples:
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_25p8, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_25p8_SFECP,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_26p6, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_26p6_SFECP,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_41p3, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_51p6_SFECP,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_51p6, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_51p6_SFECP,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_53p1, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_53p1_SFECP,
     if baud rate of a PMD channel for FEC traffic is ODSP_BAUD_RATE_56p3, baud rate of this PMD channel for SFEC+ traffic is ODSP_BAUD_RATE_56p3_SFECP,
     */
    ODSP_SFEC_PLUS = 2,
} e_odsp_sfec_mode;

/**
 * SFEC Rules
 */
typedef struct {
    /** SFEC mode, one of disabled, SFEC or SFEC+ */
    e_odsp_sfec_mode sfec_mode;
    /** SFEC configuration, see e_odsp_sfec_cfg enumerated type */
    e_odsp_sfec_cfg  sfec_cfg;
    // Additional rules can be added as needed
} odsp_sfec_rules_t;

/**
 * The common rules for all Host/Line channels in bundle
 */
typedef struct
{
    /** Signalling type, NRZ or PAM on HTX, PAM only on LTX */
    e_odsp_signal_mode signalling;

    /** Baud rate */
    uint32_t baud_rate;
}odsp_bundle_side_rules_t;

/** Transmitter rules for Line side */
typedef odsp_tx_rules_t odsp_ltx_rules_t;

/** Transmitter rules for Host side */
typedef odsp_tx_rules_t odsp_htx_rules_t;

/**
 * This structure contains the rules used to control
 * the operational behavior of the device.
 */
typedef struct 
{
    /** The ASIC package type */
    e_odsp_package_type package_type;

    /** If the package AND driver type are unmapped in the EFUSE, this specifies the bare die driver type */
    e_odsp_tx_driver_type ltx_driver_type_override;

    /* STD driver voltage */
    e_odsp_tx_driver_voltage ltx_std_driver_voltage;

    /** The RX AFE attenuation, applicable for HRX only */
    e_odsp_afe_trim hrx_afe_trim;

    /** The operational mode of the device */
    e_odsp_operational_mode operational_mode;

    /** The protocol mode of the device */
    e_odsp_protocol_mode protocol_mode;

    /** The common rules for all Host channels */
    odsp_bundle_side_rules_t host_side;

    /** The common rules for all line channels */
    odsp_bundle_side_rules_t line_side;

    /** Note the channel arrays are sized +1 because some package channels are 1-based */
    /** LRX Line receive rules */
    odsp_lrx_rules_t lrx[ODSP_MAX_CHANNELS+1];

    /** HRX Host receive rules */
    odsp_hrx_rules_t hrx[ODSP_MAX_CHANNELS+1];

    /** LTX Line transmit rules */
    odsp_ltx_rules_t ltx[ODSP_MAX_CHANNELS+1];

    /** HTX Host transmit rules */
    odsp_htx_rules_t htx[ODSP_MAX_CHANNELS+1];

    /**
     * In Forward Gearbox mode: specifies the HRX source channel and has up to four Rx channel selections per single LTX channel (4:1 forward gearbox)
     *
     * In Reverse Gearbox mode: specifies the Rx channel and interleave index used as a source for the given LTX channel.
     * Each Rx channel can contain up to 4 HTX channels of data.
     * - ltx_xbar_src_chan[channel][0] - Specifies the HRX source channel
     * - ltx_xbar_src_chan[channel][1] - Specifies the interleave index within the source channel (Valid values are 0, 1, 2, 3)
     */
    uint8_t ltx_xbar_src_chan[ODSP_MAX_CHANNELS+1][4];

    /**
     * LTX CLK xbar provides configurability of a clock source for each LTX channel
     */
    uint8_t ltx_clk_xbar[ODSP_MAX_CHANNELS+1];

    /**
     * In Forward Gearbox mode: specifies the Rx channel and interleave index used as a source for the given HTX channel.
     * Each Rx channel can contain up to 4 HTX channels of data.
     * - htx_xbar_src_chan[channel][0] - Specifies the LRX source channel
     * - htx_xbar_src_chan[channel][1] - Specifies the interleave index within the source channel (Valid values are 0, 1, 2, 3)
     *
     * In Reverse Gearbox mode: specifies the LRX source channel and has up to four Rx channel selections per single HTX channel (4:1 forward gearbox)
     */
    uint8_t htx_xbar_src_chan[ODSP_MAX_CHANNELS+1][4];

    /**
     * HTX CLK xbar provides configurability of a clock source for each HTX channel
     */
    uint8_t htx_clk_xbar[ODSP_MAX_CHANNELS+1];

    /**
     * LRX Quality-check rules
     */
    odsp_rx_qc_rules_t lrx_qc;

    /**
     * HRX Quality-check rules
     */
    odsp_rx_qc_rules_t hrx_qc;

    /** FEC rules for ingress direction */
    odsp_fec_rules_t ig_fec;

    /** FEC rules for egress direction */
    odsp_fec_rules_t eg_fec;

    /** SFEC No AM Mode Enable */
    bool sfec_no_am_mode;

    /** SFEC rules (per LRX/LTX channel but common per bundle) */
    odsp_sfec_rules_t sfec;

    /** Advanced rules */
    odsp_advanced_rules_t advanced;

    /** Flag to enable print out debug information */
    bool show_debug_info;

    /** Firmware download time-out value, units are 100 microseconds (default 5000 or 500ms) */
    uint32_t fw_dwld_timeout;

    /**
     * If this flag is set a warning will be generated if the firmware
     * version detected does not match the one the API was bundled with
     */
    bool fw_warn_if_mismatched;
} odsp_rules_t;

/**
 * Struct for saving the channel iterators
 *
 * @private
 */
typedef struct
{
    uint8_t num;
    uint8_t ch[8];
} odsp_channels_t;

/**
 * Channel configuration and status
 */
typedef struct {
    /** Indicate if channel is active */
    uint8_t enable;

    /** Signaling mode */
    uint8_t signal;
    /** Operational mode */
    uint8_t op_mode;
    /** Protocol mode */
    uint8_t pro_mode;
    /** Baud rate */
    uint32_t baud_rate;

    /** Source data channel IDs(LTX/HTX only) */
    uint8_t xbar_src_chn[4];
    /** Clock source(LTX/HTX only) */
    uint8_t xbar_clk_src;

    /** Signal detect(LRX/HRX only) */
    uint8_t los;
    /** FW lock status */
    uint8_t fw_lock;
    /** DSP status(LRX/HRX only) */
    uint8_t dsp_ready;
    /** PLL Lock status */
    uint8_t pll_lock;

    /** Number of times this channel has had to re-acquire lock */
    uint16_t reset_cnt;
} odsp_chn_info_t;

/**
 * Configuration and status of all channels
 */
typedef struct
{
    odsp_chn_info_t chn_info[ODSP_MAX_CHANNELS+1];
} odsp_interface_info_t;

/**
 * Configuration and status of all channels and interfaces
 */
typedef struct
{
    /*
     * Indexes:
     * - 0: LRX
     * - 1: HRX
     * - 2: HTX
     * - 3: LTX
     */
    odsp_interface_info_t intf_info[4];
} odsp_interfaces_info_t;

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif // __ODSP_RULES_H__
