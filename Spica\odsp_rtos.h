/** @file odsp_rtos.h
 ****************************************************************************
 *
 * @brief
 *    This contains all the RTOS(like system calls) and environment      *
 *    related macro's and stub utilities which should be modified or     *
 *    filled in as suited to the customer environment. It is important   *
 *    that this customization or porting of the driver is done BEFORE    *
 *    making any attempt to compile or use the driver.                   *
 *
 ****************************************************************************
 * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ****************************************************************************/
#ifndef __ODSP_RTOS_H__
#define __ODSP_RTOS_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "odsp_types.h"
#include "odsp_config.h"

#if (defined(_MSC_VER) || defined(__TINYC__)) && !defined(_WINDOWS)
   //We use _WINDOWS to signify tcc or msvc compilers
#  define _WINDOWS
#endif

//#if defined(_WINDOWS)
//#  include <windows.h>
//#endif

// rarely we intentionally want fall-throughs in switch statements
// in C++17 they added a fancy [[fallthrough]] statement which you can use,
// or the non-standard __attribute__((fallthrough)) in gcc/clang
//#define ODSP_FALLTHROUGH __attribute__((fallthrough))
//#define ODSP_FALLTHROUGH [[fallthrough]];
#define ODSP_FALLTHROUGH

/**********************************************************
 *         Input/Output Routines                          *
 **********************************************************/
#ifdef ODSP_DONT_USE_STDLIB
// You can either replace these with non-stdlib versions or remove them entirely
#  define ODSP_FPRINTF(...)
#  define ODSP_FLUSH()
#  define ODSP_FFLUSH(x)
#  define ODSP_PRINTF(...)
#  define ODSP_SNPRINTF(...) 0
#  define ODSP_STRNCAT(...)
#  define ODSP_NOTE(...)
#  define ODSP_WARN(...)
#  define ODSP_CRIT(...)
#  define ODSP_FN_START(...)
#  define ODSP_RETURN(...) return __VA_ARGS__
#  define ODSP_OFFSETOF(st, m) ((size_t)((char *)&((st *)(1024))->m - (char *)1024))
#else
/* Include any necessary library files when building the driver */
#  include <stdlib.h>        /* for malloc(), free(), abs() */
#  include <string.h>        /* for memcpy()                */
#  include <stdarg.h>        /* for variable args           */
#  include <stdio.h>         /* for printf variants         */
#  include <time.h>          // For nanosleep
#  include <stddef.h>        // for offsetof
#  if defined(ODSP_HAS_FLOATING_POINT)
#    include <math.h>          /* for pow, round            */
#    define ODSP_POW(...)  pow(__VA_ARGS__)
#  endif //defined(ODSP_HAS_FLOATING_POINT)
#    define ODSP_PRINTF(...)  printf(__VA_ARGS__); fflush(stdout);
#    define ODSP_FPRINTF(...) fprintf(__VA_ARGS__)
#  define ODSP_FLUSH()   fflush(stdout)
#  define ODSP_FFLUSH(x) fflush(x)
#  define ODSP_STRNCAT(...) strncat(__VA_ARGS__)
#  if !defined(__APPLE__) && !defined(_MSC_VER)
     int snprintf(char* s, size_t n, const char* format, ...);
#  endif /* __APPLE__ */
#  if defined(_MSC_VER)
     //MSVC does things differently...
#    define ODSP_SNPRINTF(...) _snprintf(__VA_ARGS__)
#  else
#    define ODSP_SNPRINTF(...) snprintf(__VA_ARGS__)
#  endif

   //logging functions, based on http://stackoverflow.com/a/1644898
   //To print out a 'note' message
#  define ODSP_NOTE(...) \
      do { if(ODSP_HAS_LOG_NOTE) { \
          ODSP_FPRINTF(stderr, __VA_ARGS__); \
          ODSP_FFLUSH(stderr); \
          } \
      } while(0)
   //To print out a 'warning' message
#  define ODSP_WARN(...) \
      do { if(ODSP_HAS_LOG_WARN) { \
          ODSP_FPRINTF(stderr, "[WARNING]  %s: ", __func__); \
          ODSP_FPRINTF(stderr, __VA_ARGS__); \
          ODSP_FFLUSH(stderr); \
          } \
      } while(0)
          
   //To print out a 'critical' message
#  define ODSP_CRIT(...) \
      do { if(ODSP_HAS_LOG_CRIT) { \
          ODSP_FPRINTF(stderr, "[CRITICAL] %s: ", __func__); \
          ODSP_FPRINTF(stderr, __VA_ARGS__); \
          ODSP_FFLUSH(stderr); \
          } \
      } while(0)

#  define ODSP_OFFSETOF(st, m) offsetof(st, m)

#  if defined(ODSP_HAS_MATH_DOT_H)
//   Must have math.h support to use pow; we don't provide a homebrew version
#    define ODSP_POW(...)  pow(__VA_ARGS__)
#  else
#    define ODSP_POW(...) (0)
#  endif


#  define ODSP_OFFSETOF(st, m) offsetof(st, m)
     
#endif /* ODSP_DONT_USE_STDLIB */

/**********************************************************
 *         Timer delay utilities                          *
 **********************************************************/
void ODSP_UDELAY(int usecs);
void ODSP_MDELAY(int msecs);

/**********************************************************
 *         Memory Handling                                *
 **********************************************************/
char *ODSP_STRNCPY(char *dest, const char *source, int count);
void *ODSP_MEMSET(void *dest, int ch, unsigned int count);
void *ODSP_MEMCPY(void *dest, const void *src, unsigned int count);

/**********************************************************
 *         String Handling                                *
 **********************************************************/
uint32_t ODSP_STRNLEN(const char *s, uint32_t max_len);

/**********************************************************
 *         Byte Swapping
 **********************************************************/
uint32_t ODSP_NTOHL(uint32_t data);

/**********************************************************
 *         Other utilities                                *
 **********************************************************/
unsigned int ODSP_ABS(int value);

/**
 * Calculates checksum on src data of given length
 *
 * Checksum is just a simple add and rotate
 *
 * @param src    [I] - Pointer to the source data
 * @param length [I] - Length of source data
 *
 * @return 32bit checksum
 */
uint32_t odsp_checksum(const void *src, unsigned int length);

/**
 * Initialize CRC table for calculating CRC
 *
 * @param crc_polynomial [I] - CRC crc_polynomial
 * @param table_size     [O] - Size of CRC table
 *
 * @return pointer to CRC table
 */
const uint32_t* odsp_crc_table(uint32_t crc_polynomial, uint32_t* table_size);

/**
 * Convert a raw register value in twos-compliment into a signed int.
 *
 * @param reg_value - Raw reg data
 * @param num_bits - Number of bits in the register
 *
 * @return int value
 */
int32_t odsp_reg2c_to_int(uint32_t reg_value, int num_bits);

/**
 * Utility method to calculate bitmask size
 *
 * @param mask    [I] - Bit-mask
 *
 * @return bitmask size
 */
uint8_t odsp_bitmask_size(uint32_t mask);


/* bit masks */
#define INBIT0  0x00000001
#define INBIT1  0x00000002
#define INBIT2  0x00000004
#define INBIT3  0x00000008
#define INBIT4  0x00000010
#define INBIT5  0x00000020
#define INBIT6  0x00000040
#define INBIT7  0x00000080

#define INBIT8  0x00000100
#define INBIT9  0x00000200
#define INBIT10 0x00000400
#define INBIT11 0x00000800
#define INBIT12 0x00001000
#define INBIT13 0x00002000
#define INBIT14 0x00004000
#define INBIT15 0x00008000

#define INBIT16 0x00010000 
#define INBIT17 0x00020000
#define INBIT18 0x00040000
#define INBIT19 0x00080000
#define INBIT20 0x00100000
#define INBIT21 0x00200000
#define INBIT22 0x00400000
#define INBIT23 0x00800000

#define INBIT24 0x01000000
#define INBIT25 0x02000000
#define INBIT26 0x04000000
#define INBIT27 0x08000000
#define INBIT28 0x10000000
#define INBIT29 0x20000000
#define INBIT30 0x40000000
#define INBIT31 0x80000000

/** Use this macro when assigning to a odsp_boolean,
 * since the odsp_boolean is really an unsigned char
 * 
 * @example
 * valid == TRUE iff bit3 OR bit5 is set in reg_val:
 * cs_boolean valid = ODSP_IF_SET(reg_val,INBIT3|INBIT5);
 */
#define ODSP_IF_SET(val,mask) ( ((val) & (mask)) != 0 )

/** True iff all bits in mask are set */
#define ODSP_IF_ALL_SET(val,mask) ( ((val) & (mask)) == mask )

/** True iff all bits in mask are cleared */
#define ODSP_IF_CLR(val,mask) ( ((val) & (mask)) == 0 )

/** Set mask bits in val */
#define ODSP_SET(val,mask) ( ((val) | (mask)) )

/** Clear mask bits in val */
#define ODSP_CLR(val,mask) ( ((val) & ~(mask)) )

/** Toggle mask bits in val */
#define ODSP_TOGGLE(val,mask) ( ((val) ^ (mask)) )


/** Simple define to help stringify enums for translation */
#define ODSP_TRANS_ENUM(value) case value: return #value;

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif /* __ODSP_RTOS_H__ */

