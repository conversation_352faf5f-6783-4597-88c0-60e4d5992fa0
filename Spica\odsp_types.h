/** @file odsp_types.h
 ****************************************************************************
 *
 * @brief
 *    This module contains common data types and defines used by
 *    the driver.
 *
 ****************************************************************************
 *  * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ****************************************************************************/
#ifndef __ODSP_TYPES_H__
#define __ODSP_TYPES_H__



#ifdef __cplusplus
//for the PRIu64 macros in g++
extern "C" {
#define __STDC_FORMAT_MACROS
#endif //__cplusplus

#include <inttypes.h>
#include <stdint.h>
#include <stdbool.h>

/*
 * Basic data types
 */
typedef int32_t odsp_status_t;

enum
{
    ODSP_OK = 0,
    ODSP_ERROR = -1
};


#ifndef __LINE__
#   define __LINE__ 0
#endif
#ifndef __FILE__
#   define __FILE__ "<unknown>"
#endif

#ifdef _MSC_VER
   //MSVC doesn't support __func__
#  define __func__ __FUNCTION__
#endif

/*
 * Other defines
 */
typedef uint32_t inphi_status_t;

#ifdef INPHI_OK
#  undef INPHI_OK
#endif

#ifdef INPHI_ERROR
#  undef INPHI_ERROR
#endif

#define INPHI_OK           ODSP_OK
#define INPHI_ERROR        ODSP_ERROR

#ifndef NULL
#  define NULL            0
#endif

#ifdef __cplusplus
} /* closing brace for extern "C" */
#endif

#endif /* __ODSP_TYPES_H__ */

