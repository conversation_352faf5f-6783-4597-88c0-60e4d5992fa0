/** @file odsp_rtos.c
 ****************************************************************************
 *
 * @brief
 *    This contains all the RTOS(like system calls) and environment      *
 *    related macro's and stub utilities which should be modified or     *
 *    filled in as suited to the customer environment. It is important   *
 *    that this customization or porting of the driver is done BEFORE    *
 *    making any attempt to compile or use the driver.                   *
 *
 ****************************************************************************
 *  * <AUTHOR>    This file contains information that is proprietary and confidential to
 *    Marvell Corporation.
 *
 *    This file can be used under the terms of the Marvell Software License
 *    Agreement. You should have received a copy of the license with this file,
 *    if not please contact your Marvell support staff.
 *
 *    Copyright (C) 2006-2024 Marvell Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 1.2.1.2116
 ****************************************************************************/
#include "odsp_rtos.h"
#include "Drivers.h"

//#if defined(_WINDOWS)
//int usleep(unsigned int usec)
//{
//    LARGE_INTEGER cur;    // The current timer value
//    LARGE_INTEGER end;    // When we stop looping
//    LARGE_INTEGER res;    // The resolution of the timer

//    /* If we are sleeping in msec then use Sleep() (not as accurate but less CPU heavy).  Also if there is
//       no performance timer available then fall back to this */
//    if(usec>1000 || QueryPerformanceFrequency(&res)==0)
//    {
//        Sleep(usec/1000);
//    }
//    else
//    {
//        QueryPerformanceCounter(&cur);
//        end.QuadPart = cur.QuadPart + (res.QuadPart*(usec/1000000.0));
//        do
//        {
//            QueryPerformanceCounter(&cur);
//        } while(cur.QuadPart<end.QuadPart);
//    }

//    return 0;
//}
//#else
//#include <unistd.h>
//int usleep(uint32_t usec);
//#endif


void ODSP_UDELAY(int usecs)
{
#ifdef ODSP_DONT_USE_STDLIB
    //#error "TO DO: Cannot compile without defining CS_UDELAY() for your system in platform/odsp_rtos.c"
#else
 //   usleep(usecs);
	Delay_Us(usecs);
#endif
}

void ODSP_MDELAY(int msecs)
{
    ODSP_UDELAY(msecs * 1000);
}

char *ODSP_STRNCPY(char *dest, const char *source, int count)
{
  char *start = dest;

  while (count && (*dest++ = *source++)) count--;
  if (count) while (--count) *dest++ = '\0';
  return start;
}

/* Calc simple 32bit checksum on src data */
uint32_t odsp_checksum(const void *src, unsigned int size)
{
    const uint8_t *s_ptr = (const uint8_t*)src;
    const uint8_t *end = s_ptr + size;
    uint32_t cksum = 0;

    for(; s_ptr < end; s_ptr++) {
        cksum = (cksum >> 1) | (cksum << (32 - 1));
        cksum += *s_ptr;
    }
    return cksum;
}

void *ODSP_MEMSET(void *dest, int ch, unsigned int count)
{
#ifdef ODSP_DONT_USE_STDLIB
    unsigned char *pb = (unsigned char *)dest;
    unsigned char *pbend = pb + count;
    unsigned char val = (unsigned char)ch;
    while (pb != pbend) *pb++ = val;
    return dest;
#else
    return memset(dest, ch, count);
#endif
}

void *ODSP_MEMCPY(void *dest, const void *src, unsigned int count)
{
#ifdef ODSP_DONT_USE_STDLIB
  char *d = dest;
  const char *s = src;
  while (count--)
    *d++ = *s++;
  return dest;
#else
    return memcpy(dest, src, count);
#endif
}


unsigned int ODSP_ABS(int value)
{
#ifdef ODSP_DONT_USE_STDLIB
    return (unsigned int) (value < 0 ? -value : value);
#else
    return (unsigned int) abs(value);
#endif
}


uint32_t ODSP_STRNLEN(const char *s, uint32_t max_len)
{
#ifdef ODSP_DONT_USE_STDLIB
    uint32_t i = 0;
    for (; i < max_len && s[i] != '\0'; ++i);
    return i;
#else
#  if !defined(_POSIX_C_SOURCE) || _POSIX_C_SOURCE < 200809L
    return (uint32_t)strlen(s);
#  else
    return (uint32_t)strnlen(s, max_len);
#  endif
#endif
}

#ifndef ODSP_DONT_USE_STDLIB
#    if !defined(_WINDOWS) && !defined(_WIN32) 
//#        include <arpa/inet.h> /* for ntohs, htons            */
#    endif
#endif

uint32_t ODSP_NTOHL(uint32_t data)
{
#if defined(ODSP_DONT_USE_STDLIB) || defined(_WINDOWS) || defined(_WIN64)
    // Platform independent ntohl
    uint8_t *datap = (uint8_t*)&data;
    return ((uint32_t)datap[0] << 24) |
           ((uint32_t)datap[1] << 16) |
           ((uint32_t)datap[2] << 8)  |
           ((uint32_t)datap[3]);
#else
//    return ntohl(data);
		return 0;
#endif
}


#define CRC_TABLE_SIZE 0x100

const uint32_t* odsp_crc_table(uint32_t crc_polynomial, uint32_t* table_size)
{
    static uint32_t crc_table[CRC_TABLE_SIZE];
    int i,j;

    /** Compute CRC table */
    for (i = 0; i < CRC_TABLE_SIZE; i++ )
    {
        /** Remainder from polynomial division */
        crc_table[i] = i;
        for (j = 0; j < 8; j++)
        {
            /** Value is odd or even */
            if (crc_table[i] & 0x01)
            {
                /** Odd */
                crc_table[i] >>= 1;
                crc_table[i] ^= crc_polynomial;
            }
            else
            {
                /** Even - nothing to be done */
                crc_table[i] >>= 1;
            }
        }
    }

    if (table_size)
        *table_size = CRC_TABLE_SIZE;

    return crc_table;
}

/* Convert a raw register value in twos-compliment into a signed int. */
int32_t odsp_reg2c_to_int(uint32_t reg_value, int num_bits)
{
    uint32_t max = 1<<num_bits;
    uint32_t mask = max-1;
    uint32_t half = 1<<(num_bits-1);
    uint32_t mag = (reg_value & mask);
    if(mag >= half) {
        return (int32_t)mag-max;
    }
    return (int32_t)mag;
}

/* Utility method to calculate bitmask size */
uint8_t odsp_bitmask_size(uint32_t mask)
{
    uint8_t mask_size = 0;

    for (uint8_t bit_i = 0; bit_i < 32; bit_i++)
    {
        if (mask & (1 << bit_i))
        {
            mask_size++;
        }
    }

    return mask_size;
}
